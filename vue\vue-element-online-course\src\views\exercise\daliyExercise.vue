<template>
  <div>
    <div class="exercise-box"></div>
    
  </div>
</template>
<script>
import { createDailyPaper } from "@/api/exercise.js";
//import moment from 'moment'
//import liuyunoTabs from "@/components/liuyuno-tabs/liuyuno-tabs.vue";
// import skeleton from './index.skeleton.vue'
export default {
  data() {
    return {
      exerciseBankId: "",
      exercisePageId: "",
      confirmvisible: false,
    };
  },
  components: {
    // liuyunoTabs
    // radialindicator
    // skeleton
  },
  created() {
    this.exerciseBankId = this.$route.query.tkid;
    // this.getKnowledges(this.postparams.exerciseBankId)
    // this.$refs.confirm.open()
    this.createDailyPaper();
  },

  methods: {
    createDailyPaper() {
      createDailyPaper(this.exerciseBankId).then((res) => {
        this.exercisePageId = res.id;
        if (!res.isNew) {
          // 已存在练习
          this.confirmvisible = true;
        } else {
          this.gotoexam();
        }
      });
    },
    goback() {
      this.$router.push({
        name: "ExerciseCenter",
      });
    },
    gotoexam() {
      this.$router.push({
        name: "StartExercise",
        query: {
          examId: this.exercisePageId,
        },
      });
    },
  },
};
</script>

<style>
.tishi-dialog  .el-dialog{
    width: 420px;
    height: 260px;
    border-radius: 4px;
    overflow: hidden;
}

.tishi-box {
  width: 380px;
    height: 200px;
  margin: auto;
  background: #fff;
  text-align: center;
  overflow: hidden;
  position: relative;
}
.tishi-box .title {
  color: #000;
  font-size: 20px;
  margin: 11px auto 35px;
}
.tishi-box .content {
  color: #37404f;
  font-size: 16px;
  text-align: left;
  margin: 0 40px;
}
.tishi-box .btns {
  border-top: 1px solid #eee;
  position: absolute;
  left: 0;
  bottom: 0;
}
.tishi-box .b-item {
  display: inline-block;
  width: 187px;
  height: 50px;
  line-height: 50px;
  cursor: pointer;
}
.tishi-box .b-item1 {
  color: #666;
  font-size: 18px;
  border-right: 1px solid #eee;
}
.tishi-box .b-item2 {
  color: #0078ff;
  font-size: 18px;
}
</style>
