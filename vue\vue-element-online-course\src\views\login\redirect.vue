<template>
  <div />
</template>
<script>
// import { setToken } from "@/utils/auth";
export default {
  name: "Redirect",
  watch: {
    $route: {
      handler: function (route) {
        this.$store.dispatch('user/passwordValid', true)
        this.$store.dispatch('user/phoneValid', true)
        this.$store.dispatch('user/isNew', false)

        const query = route.query;
        if (query) {
          this.$store.dispatch("user/settoken",query.token);
          this.$store.dispatch("user/getInfo");
          // setToken(query.token);
          this.init(query.redirect);
        }
      },
      deep: true,
      immediate: true,
    },
  },
  created() {
      this.$store.dispatch('user/passwordValid', true)
      this.$store.dispatch('user/phoneValid', true)
      this.$store.dispatch('user/isNew', false)
  },
  methods: {
    init(url) {
      this.$router.push({
        path: url || "/",
      });
    },
  },
};
</script>
