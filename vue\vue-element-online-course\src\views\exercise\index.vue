<template>

  <div class="exercise-cnt exercise-home" >
    <div class="exercise-box3">
      <div class="tiku-tab" style="cursor:auto">
        <div>题库：</div>
        <div>{{currentTiKu.name}}</div>
        <img src="/static/img/arrow-down.png" mode="aspectFit" />
      </div>
      <div class="report-box">
        <radialindicator v-if="showRate&&reportInfo.rightRate!=undefined" class="percent-box"
          :percentNum="reportInfo.rightRate" txt="正确率"></radialindicator>
        <div class="total-box">
          <div class="item-list">
            <div class="item" v-if="reportInfo.todaySubmitCount!=undefined">
              <div class="num">{{reportInfo.todaySubmitCount}}次</div>
              <div class="txt">今日做题次数</div>
              <div class="blank"></div>
            </div>
            <div class="item" v-if="reportInfo.todayAnswerQuesCount!=undefined">
              <div class="num">{{reportInfo.todayAnswerQuesCount}}道</div>
              <div class="txt">今日做题数量</div>
            </div>

          </div>
          <div class="item-list">
            <div class="item" v-if="reportInfo.totalSubmitCount!=undefined">
              <div class="num">{{reportInfo.totalSubmitCount}}次</div>
              <div class="txt">总做题次数</div>
              <div class="blank"></div>
            </div>
            <div class="item" v-if="reportInfo.totalAnswerQuesCount!=undefined">
              <div class="num">{{reportInfo.totalAnswerQuesCount}}道</div>
              <div class="txt">总做题道数</div>
            </div>
          </div>
        </div>
      </div>
      <div class="box-list-right">
        <div class="box2" @click="gotodaliyExercise()">
          <div class="txt">每日练习</div>
          <img src="/exercise/box2.png" />
        </div>
        <div class="box3" @click="gotoselfExercise()">
          <div class="txt">自主练习</div>
          <img src="/exercise/box3.png" />
        </div>
        <div class="box4" @click="gotokeysExercise()">
          <div class="txt">知识点练习</div>
          <img src="/exercise/box4.png" />
        </div>
      </div>
    </div>
    <div class="record-list-box">
      <div class="list-top">
        <div class="date-time" @click="showDate()">
          <div class="mouth">{{params.Month}}</div>月
          <div class="day">{{params.Date}}</div>日
        </div>
        <div class="tabs">
          <a :class="{active:index==activeIndex}" v-for="(v,index) in tabs" @click="tabClick(index)">{{v}}</a>
        </div>
      </div>
      <div class="recordlist">
        <div class="item" v-for="item in reportlist">
          <img class="thumb" src="/exercise/icon-exercise.png" mode="aspectFill" />
          <div class="right">
            <div class="title">{{item.exercisePaperName}}</div>
            <div class="txt">练习时间：{{item.startDate | DateFromte('YYYY-MM-DD HH:mm') }} </div>
            <div v-if="item.submitDate==null" class="statue statue0">未提交</div>
            <div v-else class="statue statue1">已提交</div>
          </div>
          <div class="btns">
            <div class="btn btn1" v-if="item.submitDate!=null" @click="enterResultPage(item.id)">查看解析</div>
            <div class="btn btn0" v-else @click="enterExercise(item.exercisePaperId)">进入练习</div>
          </div>
        </div>
      </div>
      <NoContent v-if="reportlist.length==0"></NoContent>
      <el-pagination v-if="params.total>params.MaxResultCount" class="my_pagination" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" :current-page="params.page" :page-sizes="[10, 20, 40]"
        :page-size="params.MaxResultCount" layout="total, sizes, prev, pager, next, jumper" :total="params.total">
      </el-pagination>
    </div>
    <el-dialog :visible.sync="datevisible" class="date-dialog" :show-close="false">
      <div class="h_title">
        <div class="title">选择日期</div>
        <a class="close" @click="datevisible=false">&times;</a>
      </div>
      <el-calendar v-model="params.Day" v-if="datevisible">
        <div slot="dateCell" slot-scope="{ data }" @click="clickcalendar(data)" class="temp">
          <span :class="data.isSelected ? 'is-selected-1' : ''">
            {{ data.day.split("-").slice(2).join("-") }}
          </span>
        </div>
      </el-calendar>
    </el-dialog>
    <el-dialog :visible.sync="tikulistvisible" class="tk-dialog" :show-close="false">
      <div class="h_title">
        <div class="title">选择题库</div>
        <a class="close" @click="tikulistvisible=false">&times;</a>
      </div>
      <el-scrollbar class="tiku-list-box" scroll-y="true">
        <div class="tiku-list">
          <div class="item" v-for="item in tikuList" @click="chooseTiKu(item)">
            <div :class="[currentTiKu.id===item.id?'checked txt':'txt']">{{item.name}}</div>
            <div class="img-span">
              <img v-if="currentTiKu.id===item.id" src="/exercise/icon-s.png" />
            </div>
          </div>
        </div>
      </el-scrollbar>
    </el-dialog>
    <el-dialog title="" :visible.sync="confirmvisible" class="tishi-dialog">
      <div class="tishi-box">
        <div class="title">温馨提示</div>
        <div class="content">已存在今日练习，是否继续答题？</div>
        <div class="btns">
          <div class="b-item b-item1" @click="confirmvisible = false">取消</div>
          <div class="b-item b-item2" @click="gotoexam()">继续答题</div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
  import radialindicator from "./radialIndicator.vue"
  import {
    getbanks, getMyReport, getMyRecord, createDailyPaper
  } from '@/api/exercise.js'
  import moment from 'moment'
  // import skeleton from './index.skeleton.vue'
  import NoContent from '@/components/NoContent'
  export default {
    data () {

      return {
        currentTiKu: {
          id:'',
          name:''
        },
        reportlist: [],
        tikuList: [],
        params: {
          Filter: '',
          ExerciseBankId: '',
          Day: '',
          Year: '',//年
          Month: '',//月
          Date: '',//日
          Sorting: '',
          SkipCount: 0,
          MaxResultCount: 10,
          isSubmit: '',
          total: 0,
          page: 1
        },
        tabs: [
          '全部',
          '未提交',
          '已提交',
        ],
        visible: false,
        isLoading: true,
        activeIndex: 0,
        reportInfo: {},
        tikulistvisible: false,
        datevisible: false,
        showRate: false,
        exercisePageId: "",
        confirmvisible: false,
      }
    },
    components: {
      radialindicator,
      NoContent
      // skeleton
    },
    watch: {
      $route: {
        handler: function (route) {
          // console.log(route.query.id)
          this.currentTiKu.id = route.query.id
          this.currentTiKu.name = route.query.name
          this.getMyReport(this.currentTiKu.id)
          this.params.ExerciseBankId = this.currentTiKu.id
          this.getMyRecord()
        },
        immediate: true
      }

   },
    created () {
      this.params.Day = moment().format('YYYY-MM-DD')
      var datevalue = this.params.Day.split('-')
      this.params.Year = datevalue[0]
      this.params.Month = datevalue[1]
      this.params.Date = datevalue[2]
    },

    methods: {
      handleSizeChange (val) {
        this.params.MaxResultCount = val
        this.getMyRecord()
      },
      handleCurrentChange (val) {
        this.params.page = val
        this.getMyRecord()
      },
      gotoRecord () {
        this.$router.push({
          name: "ExerciseRecord",
          query: {
            tkid: this.currentTiKu.id,
            tkName:this.currentTiKu.name
          }
        })
      },
      gotodaliyExercise () {
        this.createDailyPaper(this.currentTiKu.id);
        // this.$router.push({
        // 	name:"DaliyExercise",
        // 	query:{
        // 		tkid: this.currentTiKu.id
       //     tkName:this.currentTiKu.name
        // 	}
        // })
      },
      createDailyPaper (id) {
        createDailyPaper(id).then((res) => {
          this.exercisePageId = res.id;
          if (!res.isNew) {
            // 已存在练习
            this.confirmvisible = true;
          } else {
            this.gotoexam();
          }
        });
      },
      // goback() {
      // 	this.$router.push({
      // 		name: "ExerciseCenter",
      // 	});
      // },
      gotoexam () {
        this.$router.push({
          name: "StartExercise",
          query: {
            examId: this.exercisePageId,
            tkid: this.currentTiKu.id,
            tkName:this.currentTiKu.name
          },
        });
      },
      gotoselfExercise () {
        // this.$message.info('暂未开放，敬请期待！')
        this.$router.push({
        	name:"SelfExercise",
        	query:{
        		tkid: this.currentTiKu.id,
        		tkname:this.currentTiKu.name,
        	}
        })
      },
      gotokeysExercise () {
        // this.$message.info('暂未开放，敬请期待！')
        this.$router.push({
        	name:"KeysExercise",
        	query:{
        		tkid: this.currentTiKu.id,
           tkName:this.currentTiKu.name
        	}
        })
      },
      // getMyTiku () {
      //   getbanks().then(res => {
      //     if (res.items.length > 0) {
      //       this.tikuList = res.items
      //       this.currentTiKu = this.tikuList[0]
      //       this.getMyReport(this.currentTiKu.id)
      //       this.params.ExerciseBankId = this.currentTiKu.id
      //       this.getMyRecord()
      //     }

      //   })
      // },
      getMyReport (id) {
        getMyReport(id).then(res => {
          this.showRate = true
          this.reportInfo = res
        })
      },
      showTiKu () {
        // this.$refs.tiku.open()
        this.tikulistvisible = true
      },
      chooseTiKu (item) {
        // this.currentTiKu = item
        // this.$refs.tiku.close()
        this.tikulistvisible = false
        this.currentTiKu = item
        //this.$refs.tiku.close()
        this.params.ExerciseBankId = item.id
        this.reportlist = []
        this.params.page = 1
        this.getMyRecord()
        this.showRate = false
        this.getMyReport(this.currentTiKu.id)
      },
      getMyRecord () {
        this.params.SkipCount = (this.params.page - 1) * this.params.MaxResultCount
        getMyRecord(this.params).then(res => {
          this.params.total = res.totalCount
          this.reportlist = res.items

        })
      },

      showDate () {
        this.datevisible = true
      },
      // dayClick(){
      // 	this.datevisible = false
      // },
      clickcalendar (data) {
        // console.log(data)
        this.params.Day = data.day
        var datevalue = this.params.Day.split('-')
        this.params.Year = datevalue[0]
        this.params.Month = datevalue[1]
        this.params.Date = datevalue[2]
        this.datevisible = false
        this.reportlist = []
        this.params.page = 1
        this.getMyRecord()
      },
      enterExercise (id) {
        this.$router.push({
          name: "StartExercise",
          query: {
            examId: id,
            tkid: this.currentTiKu.id,
            tkName:this.currentTiKu.name
          }
        })

      },
      enterResultPage (id) {
        this.$router.push({
          name: "ResultExercise",
          query: {
            exerciseRecordId: id,
            tkid: this.currentTiKu.id,
            tkName:this.currentTiKu.name
          }
        })
      },
      tabClick (index) {
        this.activeIndex = index
        if (index == 0) this.params.isSubmit = ''
        if (index == 1) this.params.isSubmit = false
        if (index == 2) this.params.isSubmit = true
        this.reportlist = []
        this.params.page = 1
        this.getMyRecord()
      },

    }
  }
</script>

<style>
</style>
