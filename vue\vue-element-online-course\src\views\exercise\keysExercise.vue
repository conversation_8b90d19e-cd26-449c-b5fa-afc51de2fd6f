<template>
	<div class="exercise-cnt1">
		<el-breadcrumb separator-class="el-icon-arrow-right" class="bread_con">
          <el-breadcrumb-item :to="{ path: '/' }">
            <i class="el-icon-location-outline" />
          </el-breadcrumb-item>
          <el-breadcrumb-item :to="{ path: 'exerciseCenter' ,query:{ id:exerciseBankId,name:exerciseBankName}}">
            课程练习
          </el-breadcrumb-item>
          <el-breadcrumb-item>知识点练习</el-breadcrumb-item>
        </el-breadcrumb>
		<div class="h-title">知识点练习</div>
		<div class="exercise-box1">
			<div class="item-box" v-for="(item,index) in KnowledgesList" @click="enterExam(item)">
				<div class="item" >
					<img src="/exercise/icon-test.png" mode="aspectFill"/>
					<div class="title">{{item.name}}</div>
					<div class="num">{{item.questionCount}}<span>题</span></div>
				</div>
				<div class="blank"></div>
			</div>
		</div>
	</div>
</template>
<script>
	import {
		getKnowledges,
		getPaperKnowledge,
		getQuestions
	} from '@/api/exercise.js'
	export default {
		data() {
			return {
				KnowledgesList: [],
				exerciseBankId: '',
        exerciseBankName:''
			}
		},
		components: {
			// liuyunoTabs
			// radialindicator
			// skeleton
		},
		created() {
			this.exerciseBankId = this.$route.query.tkid
      this.exerciseBankName = this.$route.query.tkName
			this.getKnowledges(this.exerciseBankId)
		},

		methods: {
			getQuestions(data, i) {
				getQuestions(data).then(res => {
					this.KnowledgesList[i].questionCount = res.questionNumber
					this.$forceUpdate()
				})
			},
			getKnowledges(id) {
				getKnowledges(id).then(res => {
					// console.log(res)
					this.KnowledgesList = res.items
					this.KnowledgesList.map((item, index) => {
						let data = {
							Knowledges: item.id,
							Difficulty: '',
							QuestionType: '',
						}
						this.getQuestions(data,index)
					})
				})

			},
			enterExam(item){
				if(item.questionCount==0){
					this.$message.info('该知识下没有题目')
				} else{
					let data = {
						  exerciseBankId: this.exerciseBankId,
						  knowledgeId: item.id,
						  knowledgeName: item.name
					}
					getPaperKnowledge(data).then(res=>{
						this.$router.push({
							name:"StartExercise",
							query:{
								examId: res.id,
                tkid: this.exerciseBankId,
                tkName:this.exerciseBankName
							}
						})
					})
				}

			}

		}
	}
</script>

<style >
</style>
