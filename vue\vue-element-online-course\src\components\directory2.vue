<template>
  <div class="wx-Directory-div">
    <div
      v-for="item in directorylist"
      class="wx-item-dir"
    >
      <div class="title">
        {{ item.courseDirectoryName }}
      </div>
      <div
        v-for="r in item.resources"
        class="wx-reslist"
      >
        <div
          v-if="!unShowFinished || (unShowFinished&&!r.isFinished)"
          :class="[resourceId==r.id?'active':'','wx-resitem',freeModel==1&&r.isTrial==0?'grey':'']"
          :disable="isExpire"
          @click="clickRes(r)"
        >
          <div class="wx-top">
            <span class="wx-state">
              <!-- 已完成-->
              <img
                v-if="findRes(r)==2"
                src="/images/icon_point3.png"
                class="state"
              >
              <!-- 未完成-->
              <img
                v-if="findRes(r)==1"
                src="/images/icon_point2.png"
                class="state"
              >
              <!-- 未开始-->
              <img
                v-if="findRes(r)==0"
                src="/images/icon_point1.png"
                class="state"
              >
            </span>
            <!--video-->
            <div
              v-if="r.fileType=='.mp4'"
              class="wx-type-box"
            >
              <img
                src="/images/icon_mp41.png"
                class="type1"
              >
              <img
                src="/images/icon_mp42.png"
                class="type2"
              >
              <img
                src="/images/icon_mp43.png"
                class="type3"
              >
            </div>

            <!--pdf-->
            <div
              v-else-if="r.fileType=='.pdf' || r.resType =='pdfh5'"
              class="wx-type-box"
            >
              <img
                src="/images/icon_pdf1.png"
                class="type1"
              >
              <img
                src="/images/icon_pdf2.png"
                class="type2"
              >
              <img
                src="/images/icon_pdf3.png"
                class="type3"
              >
            </div>
            <div
              v-else-if="r.fileType=='.png'"
              class="wx-type-box"
            >
              <img
                src="/images/icon_img1.png"
                class="type1"
              >
              <img
                src="/images/icon_img2.png"
                class="type2"
              >
              <img
                src="/images/icon_img3.png"
                class="type3"
              >
            </div>
            <!--swf-->
            <div
              v-else-if="r.fileType=='.zip'||r.resType=='hundun-video'||r.resType=='geektime-video' || r.resType=='ximalaya' || r.resType=='videoh5'"
              class="wx-type-box"
            >
              <img
                src="/images/icon_mp41.png"
                class="type1"
              >
              <img
                src="/images/icon_mp42.png"
                class="type2"
              >
              <img
                src="/images/icon_mp43.png"
                class="type3"
              >
              <!-- <img
                src="/images/icon_animate1.png"
                class="type1"
              >
              <img
                src="/images/icon_animate2.png"
                class="type2"
              >
              <img
                src="/images/icon_animate3.png"
                class="type3"
              > -->
            </div>
            <div
              v-else-if=" r.resType=='geektime-html'"
              class="wx-type-box"
            >
              <img
                src="/images/icon_html1.png"
                class="type1"
              >
              <img
                src="/images/icon_html2.png"
                class="type2"
              >
              <img
                src="/images/icon_html3.png"
                class="type3"
              >
            </div>
            <div class="wx-resname">
              {{ r.name }}
            </div>
          </div>

          <div class="wx-fr">
            <div
              v-if="r.isTrial"
              class="wx-shikan_btn"
            >
              试看
            </div>
            <div
              v-if="r.duration > 0 && (r.fileType=='.mp4' || r.resType=='html'|| r.fileType=='.pdf' || r.resType =='pdfh5' || r.resType=='hundun-video'|| r.resType=='geektime-video'|| r.resType=='geektime-html'|| r.resType=='ximalaya' || r.resType=='videoh5')"
              class="wx-time wx-time1 "
            >
              {{ r.duration | timeFromte }}
            </div>
            <!-- <div class="wx-time wx-time2" v-if="findResDuration(r)!=null&&IsShowRecord"> 已学习 {{findResDuration(r) | timeFromte}} </div> -->
          </div>
        </div>
      </div>
      <Directory
        :directorylist="item.directories"
        :record-list="recordList"
        :un-show-finished="unShowFinished"
        :course-id="courseId"
        :resource-id="resourceId"
        :train-id="trainId"
        :free-model="freeModel"
        :is-show-record="IsShowRecord"
        :is-expire="isExpire"
      />
    </div>
  </div>
</template>
<script>
  import { updateCourseRecord } from '@/api/course';
  export default {
    name: 'Directory',
    props: {
      directorylist: Array,
      recordList: {},
      courseId: '',
      resourceId: '',
      trainId: '',
      freeModel: 0,//免费 = 0,收费 = 1,
      IsShowRecord: true, // 是否显示已学记录
      isExpire: false,
      unShowFinished: false, //是否只显示未完成
    },
    methods: {
      findRes (item) {
        if (this.recordList != null && this.recordList.resUserRecords != undefined) {
          var res = this.recordList.resUserRecords.find(x => x.courseResourceId == item.id)
          if (res == undefined) return 0  // 未开始
          else if (res.isComplete) return 2 //已完成
          else return 1 // 未完成
        } else {
          return 0  // 未开始
        }
      },
      findResDuration (item) { //资源已学时长
        if (this.recordList != null && this.recordList.resUserRecords != undefined) {
          var res = this.recordList.resUserRecords.find(x => x.courseResourceId == item.id)
          if (res == undefined) return null  // 未开始
          else return res.resLearnDuration //已完成 || 未完成
        } else {
          return null // 未开始
        }
      },
      clickRes (item) { //点击资源

        if (this.isExpire && !item.isTrial) {
          this.$message.info("当前课程学习已过期");
          return
        }
        if (this.$store.getters.token != null) {
          let data = {
            trainId: this.trainId, //培训ID
            courseId: this.courseId,
            lastLearnResourceId: item.id
          }
          if (this.freeModel == 0) {
            updateCourseRecord(data) // 收费 不更新
          }
          // 免费课程或者试看资源，方可打开
          if (this.freeModel == 0 || item.isTrial) {
            this.$router.push({ name: 'mobile', query: { id: item.id, courseId: this.courseId, trainId: this.trainId, trialUrl: item.trialUrl, token: this.$route.query.token } })
          }
        } else {
          // this.$store.dispatch("user/toggleloginbox", true);
        }

      }
    }
  }
</script>
<style>
  .wx-Directory-div .wx-item-dir .title {
    background-color: rgb(235, 238, 242);
    height: 2.8rem;
    color: #333;
    line-height: 2.8rem;
    text-indent: 1rem;
    font-size: 0.8rem;
  }

  .wx-Directory-div .wx-Directory-div .wx-item-dir .title {
    color: #999999;
    background: #fff;
    border-bottom: 1px solid #f0f0f0;
  }

  .wx-reslist .wx-resitem {
    font-size: 0.8rem;
    padding: 0.8rem 0.5rem 0.8rem 0;
    display: block;
    cursor: pointer;
    border-bottom: 1px solid #f0f0f0;
    position: relative;
    margin-left: 0.6rem;
  }

  /* .reslist a:hover{color: #ed570e;} */
  .wx-resitem.grey {
    color: #999 !important;
    cursor: text;
    /* padding: 0.6rem 1.8rem; */
  }

  .wx-reslist .wx-time {
    display: inline-block;
    vertical-align: middle;
  }

  .wx-reslist .wx-time1 {
    /* float: right; */
    margin-left: 0.5rem;
  }

  .wx-reslist .wx-state {
    vertical-align: middle;
    width: 1.2rem;
    height: 1.2rem;
    margin-top: -3px;
  }

  .wx-reslist .wx-state img {
    width: 0.6rem;
    height: 0.6rem;
  }

  .wx-reslist .wx-type-box {
    vertical-align: middle;
    width: 1.5rem;
    height: 1.5rpx;
    display: inline-block;
    margin-left: 0.6rem;
    margin-top: 3px;
  }

  .wx-reslist .wx-type-box img {
    width: 1.1rem;
  }

  .wx-reslist .wx-resname {
    margin-left: 0.6rem;
    display: inline-block;
    vertical-align: middle;
    width: 11.5rem;
    font-size: 0.8rem;
  }

  .wx-reslist .wx-shikan_btn {
    display: inline-block;
    color: #e14500;
    border-radius: 4px;
    background-color: rgb(255, 255, 255);
    width: 2.5rem;
    height: 1.2rem;
    line-height: 1.2rem;
    border: 1px solid #e14500;
    text-align: center;
    /* margin-right: 1.5rem; */
    vertical-align: middle;
    /* float: right; */
    font-size: 0.6rem;
  }

  .wx-reslist .top {
    display: inline-block;
    margin: 1.8rem auto
  }

  .wx-reslist .wx-fr {
    line-height: 1.6rem;
    overflow: hidden;
    display: inline-block;
    vertical-align: middle;
    /* margin-left: 1rem; */
    text-align: right;
    width: 6.8rem;
    float: right;
    margin-right: 0.5rem;
  }

  .wx-resitem.active {
    color: #ed570e;
  }

  .wx-resitem .wx-type-box .type2,
  .wx-resitem .wx-type-box .type3 {
    display: none;
  }

  .grey .wx-type-box .type1,
  .grey .wx-type-box .type3 {
    display: none;
  }

  .wx-resitem.active .wx-type-box .type1,
  .wx-resitem.active .wx-type-box .type2 {
    display: none;
  }

  .wx-resitem.active .wx-type-box .type3 {
    display: inline-block;
  }

  .wx-resitem.grey .wx-type-box .type2 {
    display: inline-block;
  }

  .wx-time2 {
    float: right;
  }

  .wx-top {
    display: inline-block;
  }
</style>
