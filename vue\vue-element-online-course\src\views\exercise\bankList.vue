<template>
    <div class="exercise-cnt exercise-home" v-if="tikuList.length > 0">
        <div class="exercise-box3">
            <!-- <div class="tiku-tab">
                <div>荟智题库 共 {{ params.total }} 个</div>
            </div> -->
            <div class="maps">
                <img src="../../assets/image/zan/map.png" />
                <span>考试中心</span>
                <img src="../../assets/image/zan/left.png" />
                <span>考前练习</span>
            </div>
            <div class="tk-list-box">
                <div class="ti-item" v-for="item in tikuList">
                    <img :src="item.thumbnailUrl||'/exercise/tk-icon.png'"
                    />
                    <div style="box-sizing: border-box;padding: 0 25px;">
                        <div class="title">{{ item.introduce }}</div>
                        <!-- <div class="fr-div">
                            <div
                                class="desc"
                                v-if="
                                    item.introduce != '' && item.introduce != null
                                "
                            >
                                {{ item.introduce | Substr(160) }}
                            </div>
                        </div> -->
                        <button
                            :disabled="!item.cloudLearn"
                            @click="EnterCenter(item)"
                        >
                            进入练习
                        </button>
                    </div>
                </div>

                <NoContent v-if="tikuList.length == 0"></NoContent>
                <el-pagination
                    v-if="params.total > params.MaxResultCount"
                    class="my_pagination"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="params.page"
                    :page-sizes="[10, 20, 40]"
                    :page-size="params.MaxResultCount"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="params.total"
                ></el-pagination>
            </div>
        </div>
    </div>
</template>
<script>
import { getbanks } from "@/api/exercise.js";
import NoContent from "@/components/NoContent";
export default {
    data() {
        return {
            tikuList: [],
            params: {
                SkipCount: 0,
                MaxResultCount: 10,
                cloudLearn: null,
                total: 0,
                page: 1,
            },
        };
    },
    components: {
        NoContent,
    },
    created() {
        this.getTikuList();
    },

    methods: {
        handleSizeChange(val) {
            this.params.MaxResultCount = val;
            this.getTikuList();
        },
        handleCurrentChange(val) {
            this.params.page = val;
            this.getTikuList();
        },
        EnterCenter(item) {
            this.$router.push({
                name: "ExerciseCenter",
                query: { id: item.id, name: item.name },
            });
        },
        getTikuList() {
            let data = {
                CourseCategoryId: this.params.CourseCategoryId,
                SkipCount: this.params.MaxResultCount * (this.params.page - 1),
                MaxResultCount: this.params.MaxResultCount,
            };
            getbanks(data).then((res) => {
                this.params.total = res.totalCount;
                if (res.items.length > 0) {
                    this.tikuList = res.items;
                }
            });
        },
    },
};
</script>

<style scoped>
.maps{
    margin-top: 20px;
    height: 30px;
    overflow: hidden;
}
.maps>span{
    font-size: 14px;
    color: #333333;
    vertical-align: middle;
}
.maps>img{
    vertical-align: middle;
    padding:0 10px;
}
</style>
