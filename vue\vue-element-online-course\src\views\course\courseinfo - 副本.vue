<template>
  <div>
    <div
      v-if="courseinfo!=undefined"
      class="course_info_box"
    >
      <div class="breadcrumb-box">
        <el-breadcrumb
          separator-class="el-icon-arrow-right"
          class="bread_con"
        >
          <el-breadcrumb-item :to="{ path: '/' }">
            <i class="el-icon-location-outline" />
          </el-breadcrumb-item>
          <el-breadcrumb-item :to="{ path: 'coursecenter' }">
            课程中心
          </el-breadcrumb-item>
          <el-breadcrumb-item>{{ courseinfo.name }}</el-breadcrumb-item>
        </el-breadcrumb>
      </div>
      <div class="courseInfo">
        <img
          v-if="courseinfo.coverUrl!=null"
          :src="courseinfo.coverUrl"
        >
        <div class="courseInfo_right">
          <span class="name">{{ courseinfo.name }}</span>
          <span class="lecturer">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              xmlns:xlink="http://www.w3.org/1999/xlink"
              width="16px"
              height="16px"
            >
              <path
                fill-rule="evenodd"
                fill="rgb(146, 146, 146)"
                d="M15.012,16.011 C14.469,16.011 14.031,15.563 14.025,15.010 L13.846,15.010 C12.923,12.152 8.112,10.038 8.009,9.993 C8.007,9.993 8.003,9.994 8.000,9.994 C7.997,9.994 7.994,9.993 7.991,9.993 C7.888,10.038 3.077,12.152 2.154,15.010 L1.975,15.010 C1.969,15.563 1.531,16.011 0.988,16.011 C0.443,16.011 0.000,15.558 0.000,15.000 C0.000,14.640 0.195,14.339 0.472,14.160 C1.475,12.065 3.938,10.292 5.777,9.195 C4.379,8.340 2.985,6.853 2.985,4.997 C2.985,2.025 5.008,-0.000 8.000,-0.000 C10.992,-0.000 13.015,2.025 13.015,4.997 C13.015,6.853 11.621,8.340 10.223,9.195 C12.062,10.292 14.525,12.065 15.528,14.160 C15.805,14.339 16.000,14.640 16.000,15.000 C16.000,15.558 15.557,16.011 15.012,16.011 ZM10.998,5.009 C10.998,3.168 9.845,2.009 8.000,2.009 C6.155,2.009 5.002,3.168 5.002,5.009 C5.002,6.850 7.012,8.037 8.000,8.009 C8.988,7.982 10.998,6.850 10.998,5.009 Z"
              />
            </svg>
            {{ courseinfo.lecturer }}</span>
          <div class="opr">
            <span v-if="userLogin&&record!=undefined&&record.id!=null">
              <span
                v-if="record.lastLearnTime==null"
                class="state unStarted"
              >未学习</span>
              <span
                v-else-if="record.isComplete==1"
                class="state isCompleted"
              >已完成</span>
              <span
                v-else
                class="state isStudying"
              >学习中</span>
            </span>
            <span
              v-if="(record==null || record.id==null)&&trainId==null&&courseinfo.freeModel==0&&courseinfo.learningPeriod>0"
              class="time"
            >加入后有效期：{{ courseinfo.learningPeriod }}天</span>
            <!-- <span v-if="(record==null || record.id==null)&&trainId==null" class="free">{{courseinfo.freeModel==0?'免费':'付费'}}</span> -->

            <button
              v-if="(record==null||record.id==null)&&courseinfo.freeModel==1"
              class="notFree_btn"
            >
              付费课
            </button>
            <button
              v-else-if="record!=null&&record.isExpire"
              class="isExpired_btn"
            >
              学习已到期
            </button>
            <button
              v-else-if="record==null || record.id==null"
              @click="study()"
            >
              立即学习
            </button>
            <button
              v-else-if="record.lastLearnTime!=null"
              @click="study()"
            >
              继续学习
            </button>
            <button
              v-else-if="record.isComplete"
              @click="study()"
            >
              学习回顾
            </button>
          </div>
        </div>
      </div>
      <div class="desc-box">
        <div class="tabslist">
          <a
            :class="tabIndex==0?'active':''"
            @click="tabIndex=0"
          >课程介绍</a>
          <a
            :class="tabIndex==1?'active':''"
            @click="tabIndex=1"
          >课程目录</a>
          <a
            :class="tabIndex==2?'active':''"
            @click="tabIndex=2"
          >问答评论</a>
          <div
            v-if="record!=null&&record.id!=null"
            class="switchbox"
          >
            <div class="">
              <span
                class="weui-switch"
                :class="{'weui-switch-on' : me_checked}"
                @click="toggle"
              /><span class="txt">只显示未学完</span>
            </div>
          </div>
        </div>
        <div
          v-show="tabIndex==0"
          class="box"
        >
          <div
            v-if="courseinfo.introduce!=null"
            class="desc-txt"
            v-html="courseinfo.introduce"
          />
          <div
            v-else
            class="desc-txt"
          >
            无
          </div>
        </div>
        <div
          v-show="tabIndex==1"
          class="box"
        >
          <Directory
            :directorylist="courseinfo.directoryResources"
            :record-list="record"
            :course-id="courseId"
            :train-id="trainId"
            :free-model="courseinfo.freeModel"
            :is-show-record="true"
            :is-expire="record!=null?record.isExpire:false"
            :un-show-finished="me_checked"
          />
        </div>
        <div
          v-if="tabIndex==2"
          class="box"
        >
          <Comment
            :course-id="courseId"
            :course-name="courseinfo.name"
          />
        </div>
      </div>
      <div class="recommand_course_box">
        <h2 class="box-title">
          <span class="line" /><span>热门课程</span>
        </h2>
        <div class="recommand_course_List">
          <div
            v-for="item1 in courseList"
            :key="item1.courseId"
            class="courseitem1"
            @click="prewCourse(item1.courseId)"
          >
            <span class="imgpan"> <img :src="item1.coverUrl"></span>
            <div class="rt">
              <span class="title">{{ item1.name | Substr(14) }}</span>
              <span class="teacher">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  xmlns:xlink="http://www.w3.org/1999/xlink"
                  width="16px"
                  height="16px"
                >
                  <path
                    fill-rule="evenodd"
                    fill="rgb(146, 146, 146)"
                    d="M15.012,16.010 C14.469,16.010 14.031,15.563 14.025,15.010 L13.846,15.010 C12.923,12.152 8.112,10.038 8.009,9.993 C8.006,9.993 8.003,9.994 8.000,9.994 C7.997,9.994 7.993,9.993 7.991,9.993 C7.888,10.038 3.077,12.152 2.154,15.010 L1.975,15.010 C1.969,15.563 1.531,16.010 0.988,16.010 C0.443,16.010 -0.000,15.558 -0.000,15.000 C-0.000,14.640 0.195,14.339 0.472,14.160 C1.475,12.065 3.938,10.293 5.777,9.195 C4.379,8.340 2.985,6.853 2.985,4.997 C2.985,2.025 5.008,-0.000 8.000,-0.000 C10.992,-0.000 13.015,2.025 13.015,4.997 C13.015,6.853 11.621,8.340 10.223,9.195 C12.062,10.293 14.525,12.065 15.528,14.160 C15.805,14.339 16.000,14.640 16.000,15.000 C16.000,15.558 15.557,16.010 15.012,16.010 ZM10.998,5.009 C10.998,3.168 9.845,2.009 8.000,2.009 C6.155,2.009 5.002,3.168 5.002,5.009 C5.002,6.850 7.012,8.037 8.000,8.009 C8.988,7.982 10.998,6.850 10.998,5.009 Z"
                  />
                </svg>
                {{ item1.lecturer }}</span>
              <!-- <span class="free" v-if="item1.freeModel==0">免费</span>
              <span class="free" v-else>付费</span> -->
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import {getCourseInfo,getRecommendCourses,getUserRecord,updateCourseRecord,updateViewCount,getHotCoursesList} from '@/api/course';
import Vue from 'vue'
import Directory from "@/components/directory"; //目录递归组件
import Comment from "@/components/comment";
export default {
  name: 'Index',
  components:{
    Directory,Comment
  },
  data() {
   return {
     userLogin:this.$store.getters.token!=null?true:false,
     courseinfo:null,
     courseList:[],
     record:null,
     ResPrewId:null,// 调转预览的资源Id,
     tabIndex:0 , // 切换tab
     courseId:this.$route.query.id,
     trainId:this.$route.query.trainId,
     me_checked:false
   }
  },
  watch: {
    '$route': 'getInfo',    // getTabelValueReset 是路由改变后触发的函数名称
    '$store.state.user.token': {
      handler: function (val, oldVal) {
       this.getInfo()
       this.userLogin = this.$store.getters.token!=null?true:false
      },
      // deep: true
    }
  },

  mounted(){
    //if (this.userLogin) await this.getRecord()
    this.getInfo()
    updateViewCount(this.$route.query.id) //更新查看次数
    //热门推荐课程
    getHotCoursesList().then(res=>{
      this.courseList = res.courses
    })
    // getRecommendCourses().then(res=>{
    //    var obj  =  res.items.find(x=>x.name=='热门推荐')
    //    if(obj!=undefined) this.courseList = obj.courses
    // })
  },
  methods: {
    async  getInfo(){
      await getCourseInfo(this.$route.query.id).then(res=>{
        this.courseinfo = res
        if(this.trainId!=null)
        {
          this.courseinfo.freeModel=0
        }
      })
      if (this.$store.getters.token!=null){ await this.getRecord()}
      await this.getNewResList(this.courseinfo.directoryResources)
    },
    async getRecord(){
      let data =   {courseId:this.$route.query.id,trainId:this.$route.query.trainId}
      await getUserRecord(data).then(res=>{
        this.record= res
        if(this.record.id!=null&&this.record.isExpire==false){
          this.courseinfo.freeModel=0
        }
      })
    },
    prewCourse(id){
      this.$router.push({name: 'CourseInfo', query:{id: id}})
    },
    async study(){
      let data = {
        trainId: this.$route.query.trainId, //培训ID
        courseId: this.$route.query.id,
        lastLearnResourceId:null
      }
      if (!this.userLogin)  this.$store.dispatch("user/toggleloginbox", true) // 未登录
      else {
        if(this.record.id==null || this.record.isComplete)  { // 开始学习 || 学习回顾  跳到 第一个资源
          this.getFirstRes(this.courseinfo.directoryResources) //
        } else if(!this.record.isExpire ) { // 继续学习 跳转到上次学习的资源
           this.ResPrewId  = this.record.lastLearnResourceId
        }

       // 当没有资源时
       if(this.ResPrewId==null )
          this.$message.info('该课程不包含可看资源')
       else{
          if(!this.record.isComplete) {
            data.lastLearnResourceId = this.ResPrewId
            await updateCourseRecord(data)  //学完不记录
            this.getRecord()
          }
         this.$router.push({name: 'ResourceInfo', query:{id: this.ResPrewId, courseId: this.$route.query.id,trainId:this.$route.query.trainId}})
       }
      }
    },

    getFirstRes(res){ //获取第一个资源
      //debugger
       for(var i = 0 ;i<res.length;i++){
         if(res[i].resources!=null&& res[i].resources.length>0 )  {
           this.ResPrewId  =  res[i].resources[0].id
           break
         }
         else if(this.ResPrewId==null){
          this.getFirstRes(res[i].directories)
         }
       }
    },
    getNewResList(res) { //获取资源集合 资源是否完成
				if (res!= null && res.length > 0) {
					for (var i = 0; i < res.length; i++) {
					  if (res[i].resources != null && res[i].resources.length > 0) {
              for(var j = 0; j<res[i].resources.length;j++){
                var index =  this.record==null || this.record.resUserRecords==null?-1:this.record.resUserRecords.findIndex(x=>x.courseResourceId== res[i].resources[j].id&& x.isComplete)
                res[i].resources[j].isFinished = index>=0?true:false
              }
					  }
					  else {
						  this.getNewResList(res[i].directories)
					  }
					}
				}
		},
    toggle(){
      this.me_checked = !this.me_checked
    }
  }
}
</script>

