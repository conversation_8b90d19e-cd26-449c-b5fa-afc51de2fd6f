<template>
    <div class="biaozhun">
        <div class="maps">
            <img src="../../assets/image/zan/map.png" />
            <span>培训中心</span>
            <img src="../../assets/image/zan/left.png" />
            <span>培训风采</span>
        </div>
        <img class="train-head" src="../../assets/image/zan/train.png" />
        <div class="train-detail">
            <img
                v-for="(item, index) in jsonObj"
                :src="item.imgUrl"
                :key="index"
                @click="handleClick(item)"
            />
        </div>
        <!-- <div class="card-box" style="margin-top:30px">
            <div class="card-item" v-for="(item, index) in jsonObj" :key="index" @click="handleClick(item)">
                <img class="card-img" :src="item.imgUrl" />
                <div class="card-teacher-box">
                    <p>{{item.title}}</p>
                </div>
            </div>

        </div> -->
        <el-dialog
            append-to-body
            :visible.sync="Dialog"
            width="800px"

            destroy-on-close
            title="培训风采"
        >
            <img :src="dialogImg" style="width: 100%;height:450px" />
        </el-dialog>

    </div>
</template>
<script>
import {
} from "@/api/newadd"
export default {
    data() {
        return {
            jsonObj:{},
            Dialog:false,
            dialogImg:''
        };
    },
    methods: {
        handleClick(item) {
            this.dialogImg = item.imgUrl;
            this.Dialog = true;
            // const url=this.$router.resolve({
            //     path: '/trainTrainDetail',
            // });
            // localStorage.setItem('trainTrainDetail',JSON.stringify(item.imgDetail));
            // window.open(url.href, '_blank');
        }
    },
    mounted() {

    },
    async created() {
        const response = await fetch(
            `${process.env.BASE_URL}train.json?${new Date().getTime()}`
        );
        this.jsonObj = await response.json();
    },
};
</script>
<style scoped>

</style>
