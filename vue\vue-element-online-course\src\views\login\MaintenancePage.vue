<template>
  <div class="login_container">
    <div class="left_container">
      <el-image class="login_bg" fit="cover" src="/images/login_bg.png" />
      <img class="login_icon" src="/images/login_icon.png">
      <img class="right_icon" src="/images/right_icon.png">
    </div>
    <div class="right_container">
      <div style="  position: relative;  width: 100%; text-align: center; line-height: 100%;">
        <img style=" position: absolute;  top: 50%; margin-top: -100px;left: 50%; margin-left: -360px;"  src="/images/Maintenance_img.png"/>
      </div>
    </div>
  </div>
</template>
<script>
</script>
<style lang="scss">
  html {
    min-width: auto !important;
  }

  $main_color: #003686;
  $secondary_color: #005dc2;
  $el-color: #606266;
  $border_color: #dcdfe6;
  $main_border_color: #003686;

  .login_container {
    display: flex;
    height: 100%;
  }

  .color_btn {
    background: linear-gradient(to right, $secondary_color, $main_color);
    border: none;
    color: white;
  }

  .color_btn:hover,
  .color_btn:focus {
    color: white;
    background: linear-gradient(to right, $secondary_color, $main_color);
  }


  .form_style {
    .el-form-item.is-error .el-input__inner {
      border-color: $border_color  !important;
    }

    .el-form-item.is-error .el-input__inner:focus,
    .el-form-item.is-error .el-input__inner:focus-within {
      border-color: $main_border_color  !important;
    }

    .el-form-item__error {
      padding-top: 8px;
    }

    .valid_form_item .el-form-item__content {
      display: flex;
    }

    .input_style:focus-within {
      border-color: $main_border_color  !important;

      .svg-icon {
        color: $main_color;
      }
    }
  }

  .left_container,
  .right_container {
    flex: 1;
    height: 100%;
    background-color: #fff;
    position: relative;
  }

  .login_bg {
    width: 100%;
    height: 100%;
  }

  .right_container {
    display: flex;
    background-color: white;
  }

  .login_icon {
    position: absolute;
    top: 44px;
    left: 55px;
    width: 180px;
    height: auto;
    z-index: 99;
  }

  // .login_content {
  //   display: flex;
  //   height: calc(100% - 70px);
  // }
  .right_icon {
    width: 100px;
    height: 100px;
    position: absolute;
    top: calc(50% - 25px);
    right: -50px;
    z-index: 99;
  }

  .login_form {
    align-self: center;
    max-width: 480px;
    margin: 0 auto;
    width: 85%;
  }

  .login_form .title {
    font-size: 38px;
    text-align: left;
    margin-bottom: 80px;
  }

  .login_form .form_label {
    margin-bottom: 20px;
    font-size: 14px;
    // color: $el-color;
  }

  .login_input .el-input__inner {
    padding-left: 40px;
  }

  .input_style .el-input__inner:focus {
    border-color: $main_border_color  !important;
  }

  .svg-container {
    font-size: 20px;
    vertical-align: middle;
    color: rgb(115, 116, 138);
    margin: 0 5px;
  }

  .login_user .el-input__inner:focus-within,
  .login_pass .el-input__inner:focus-within {
    border-color: $main_border_color  !important;
  }

  .login_user_item {
    margin-bottom: 40px;
  }

  .login_user_item .login_user:focus-within,
  .login_pass_item .login_pass:focus-within {
    border-color: $main_border_color  !important;

    .svg-icon {
      color: $main_color;
    }
  }

  .login_pass_item {
    margin-bottom: 20px;
  }

  .link_style:hover {
    color: $main_color  !important;
  }

  .login_btn {
    width: 100%;
    margin-top: 40px;
  }

  .footer {
    text-align: center;
    position: absolute;
    bottom: 10px;
    width: 100%;
    // height: 70px;
    // margin-bottom: 10px;
  }

  .footer_span {
    font-size: 12px;
    margin-bottom: 10px;
    color: #ccc;
  }

  .customDialog {
    border-radius: 15px;
    min-width: 300px;
    max-width: 350px;
  }

  .forget-form .el-form-item {
    margin-bottom: 30px;
  }

  .verificateCodeButton.is-disabled {
    background: linear-gradient(to right,
        $secondary_color,
        $main_color ) !important;
  }

  .loginRegistButton {
    width: 100%;
    margin-bottom: 20px;
  }

  .login_type {
    margin-bottom: 40px;

    .btn {
      width: 50%;
      height: 50px;
      font-weight: 700;
      display: inline-block;
      text-align: center;
      line-height: 50px;
      font-size: 20px;
      border-bottom: 1px solid #cdcdcd;
      cursor: pointer;
    }

    .btn.active {
      border-bottom: 2px solid $main_color;
    }
  }

  .find_title_item {
    display: flex;
    justify-content: center;
    align-self: center;
    margin-bottom: 40px;
  }

  .find_icon {
    height: 20px;
    width: 5px;
    background-color: $main_color;
  }

  .find_title {
    margin-left: 12px;
    font-size: 18px;
    font-weight: 600;
  }

  .find_item {
    margin: 40px auto;
    width: 320px;
    height: 80px;
    border: 1px solid #cdcdcd;
    border-radius: 10px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 16px;
    cursor: pointer;

    .svg-icon {
      color: $main_color;
    }

    &:hover {
      border-color: $main_border_color;
    }
  }

  .info_form {
    margin: 60px auto;
    width: 500px;
  }

  .info_form {
    .valid_form_item .el-form-item__content {
      display: flex;
    }

    .step_box {
      display: flex;
      margin-top: 40px;
      justify-content: space-around;
    }
  }
</style>
