<template>
    <section :class="[head_type!='ResourceInfo'?'':'res-app-main','app-main']" >
        <transition name="fade-transform" mode="out-in">
            <router-view  :key="key"/>
        </transition>
    </section>
</template>

<script>

export default {
  name: 'AppMain',
  data() {
    return{
      head_type: '',
    }
  },
  watch: {
      $route: {
        handler: function (route) {
          if (route.meta.type != undefined) this.head_type = route.meta.type
          else this.head_type = ''
        },
        immediate: true
      }
  },
  computed: {
    // cachedViews() {
    //   //return this.$store.state.tagsView.cachedViews
    // },
    key() {
      return this.$route.path
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
