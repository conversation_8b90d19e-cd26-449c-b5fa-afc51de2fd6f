<template>
	<div :class="['deepseekbox',{'is-open':show}]">
		<div class="pannel-content">
			<div class="panel-head">
                <div>
                    <img src="../assets/image/zan/head.png" />
                    <span>智慧学伴小景</span>
                </div>
				<img class="exit" src='../assets/image/zan/aiexit.png' @click="handleExit" />
			</div>
			<div class="panel-chat" ref="chatContainer">
				<div v-for="(item, index) in internalChatList" :key="index"
					:class="item.type == 'ai' ? 'pannel-copilot' : 'pannel-user'">
					<img v-if="item.type == 'ai'" src='../assets/image/zan/aipic.png' />
					<img v-else-if="item.type == 'user'" src='../assets/image/zan/userPic.png' />
					<div class="pannel_message">
						<markdown :source="item.message"></markdown>
					</div>
				</div>
			</div>

			<div class="pannel-bottom">
				<div class="bottom-container">
					<div class="input-container">
                        <textarea
                            v-model="userMessage"
                            placeholder="请输入你想问的问题"
                            @keyup.enter="sendUserMesage"
                            class="chat-textarea"
                        ></textarea>
						<!-- <input type="textarea" v-model="userMessage" placeholder="请输入你想问的问题"
							style="font-size: 16px;background: #ccc;height: 100%;" @keyup.enter="sendUserMesage"> -->
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import {voiceStart, voiceEnd} from '../utils/voiceToText';
import {deepseekApi} from '../api/deepseek'
import markdown from '../components/ua2-markdown/ua-markdown'
export default {
	props: {
		show: {
			type: Boolean,
			default: false
		}
	},
	components:{markdown},
	data() {
		return {
			scrollToView: 'bottom-anchor',
			userMessage: null,
			chatContainer: null,
			isLoading: false,
			inputType: true,
			isRecording: false,
			recorderManager: null,
			recordedChunks: [],
			conversationId: null,
			internalChatList: [{
				type: 'ai',
				message: '我是智慧学伴小景，有任何关于课程的学习疑问我们都可以一起探讨。',
				isLoading: false
			}],
            currentRequest:null,//存储当前请求
            quesShow:false,
            questionList:[
				{
					id:1,
					name:'如何拆卸驱动桥'
				}
			]
		}
	},
	watch: {
        show: {
            immediate: true,

        }
    },
    beforeDestroy() {
    },
	methods: {
        handleExit() {
            this.internalChatList = [{
                type: 'ai',
                message: '我是智慧学伴小景，有任何关于课程的学习疑问我们都可以一起探讨。',
                isLoading: false
            }];
            this.userMessage = '';

            this.conversationId = null;

            this.$emit('toggle');
        },
        more(){
			this.quesShow=!this.quesShow
		},

		// 用户发送消息
		async sendUserMesage() {
			// console.log('发送de消息:', this.userMessage);
			if (this.isLoading) return
			const result = await this.addMessageToChat('user', this.userMessage)
			// console.log('添加消息de结果:', result);
			if (result) {
				this.isLoading = true
				const data = {
					conversationId: this.conversationId,
					message: result.message,
				}
				this.addMessageToChat('ai', '思考中.......<br>', true)
				try {
					let thisObj=this;

					this.currentRequest = await deepseekApi(data,function(parsedData){
						if(parsedData){
							if (parsedData.conversation_id) {
								thisObj.conversationId = parsedData.conversation_id
							}
							// console.log('answer:',parsedData.answer)
							thisObj.updateChatList(parsedData.answer)
						}
					})
                    // 等待请求完成
                    await this.currentRequest.promise;
				} catch (error) {
					console.log(error)
					const errorMessage = error === '请求已取消'
                    ? '回答已终止'
                    : (error.message === '服务器返回数据格式错误'
                        ? '抱歉，服务器返回的数据格式有误，请稍后重试'
                        : '网络连接异常，请检查网络后重试')
					this.updateChatList(errorMessage)
				} finally {
					this.isLoading = false;
                    this.currentRequest = null;
					const lastMessage = this.internalChatList[this.internalChatList.length - 1]
					if (lastMessage && lastMessage.type === 'ai' ) {
						lastMessage.isLoading = false;
						this.$emit('update:chatList', [...this.internalChatList])
					}
				}
			}
		},
		// 暂停提问
		stopUserMesage() {
			if (this.currentRequest) {
                this.currentRequest.cancel()
                this.currentRequest = null
            }
            this.isLoading = false
            this.updateChatList('用户已终止回答')
		},
		// 更新ai聊天列表
		updateChatList(message) {
			const lastMessage = this.internalChatList[this.internalChatList.length - 1]
			if (lastMessage && lastMessage.type === 'ai' && lastMessage.isLoading) {
				lastMessage.message += message
				lastMessage.isLoading = true
			} else {
				this.internalChatList.push({
					type: 'ai',
					message: message,
					isLoading: true,
				})
			}
			this.$emit('update:chatList', [...this.internalChatList]);
			this.moveScroll();

		},
		// 消息添加到聊天列表
		async addMessageToChat(type, message, isLoading) {
			const isValidInput =
				typeof type === 'string' &&
				['user', 'ai'].includes(type) &&
				typeof message === 'string' &&
				message.trim() !== ''

			if (!isValidInput) {
				return null
			}
			const newMessage = type == 'user' ? { type, message } : { type, message, isLoading };
			this.internalChatList = [...this.internalChatList, newMessage];
			this.$emit('update:chatList', [...this.internalChatList]);

			this.userMessage = '';
			this.moveScroll();
			return newMessage;
		},
		togglePanel() {
			this.isToggle = !this.isToggle
			this.chatList = []
			this.chatList.push({ type: 'ai', message: '我是智慧学伴小景，有任何关于课程的学习疑问我们都可以一起探讨。', isLoading: false })
		},

		moveScroll() {
			this.$nextTick(() => {
				const container = this.$refs.chatContainer
				if (container) {
					container.scrollTop =container.scrollHeight;
				}
			})
		},
	},
	created(){
	}
}
</script>

<style scoped>
.question-box{
	width: 100%;
	min-height: 55px;
	position: absolute;
	left: 0;
	bottom:86px;
	box-sizing: border-box;
	padding:10px 100px;
	display: flex;
	justify-content: space-between;
	/* background: #fff; */
	overflow: visible;

}
.ques-more{
	width: 40px;
	height: 40px;
	margin-left: 30px;
    cursor: pointer;
}
.question{
	flex: 1;
	height: 100%;
	box-sizing: border-box;
	display: flex;
	flex-wrap: nowrap;
	gap: 30px;
	box-sizing: border-box;
	align-content: flex-start;
	transition: all 0.3s ease;
    /* background: red; */
	overflow: hidden;
}
.question-box.questionShow{
	height:300px;
	z-index: 1;
    border-top:1px solid #f1f1f1;
}
.question.questionShow2{
	overflow-y: auto;
	flex-wrap: wrap;
	z-index: 1;
}
.ques-item{
	flex-shrink: 0;
	background: #FFFFFF;
	height: 40px;
	line-height: 40px;
	border-radius: 30px;
	border: 1px solid #096DD9;
	padding:0 50px;
    cursor: pointer;
}
.anchor{
	min-height: 100px;
}
.deepseekbox {
	position: fixed;
	width: 380px;
    height: 640px;
    border-radius: 20px;
	background: #fff;
	transition: right 0.3s ease;
	object-fit: cover;
	box-sizing: border-box;
	/* padding: 20px; */
	bottom:170px;
    z-index: 99999;
    right: -100%;
        box-shadow: 0 5px 10px 0 rgba(0, 0, 0, 0.10);
    overflow: hidden;
}
.deepseekbox.is-open {
	right: 50px;
}
.panel-content {
	height: 100%;
}

.panel-head {
	width: 100%;
	height: 80px;
    background: #096dd9;
	position: relative;
	overflow: hidden;
}
.panel-head>div{
    margin-top: 20px;
    box-sizing: border-box;
    padding-left: 20px;
}
.panel-head>div>img{
    vertical-align: middle;
}
.panel-head>div>span{
    vertical-align: middle;
    font-size: 18px;
    color: #fff;
    padding-left: 14px;
}
.panel-head .exit {
	width: 40px;
	height:40px;
	border-radius: 50%;
	right: 20px;
	top: 20px;
	background: #3494f3;
	position: absolute;
    cursor: pointer;
}
.panel-chat {
	width: 100%;
	height: 425px;
	padding: 10px;
	box-sizing: border-box;
	overflow-y: auto;
    scroll-behavior: smooth;
    /* background: green; */
    overflow-x: hidden;
    z-index: 99;
}
.panel-chat::-webkit-scrollbar {
  width: 6px;
}

.panel-chat::-webkit-scrollbar-thumb {
  background-color: #fff;
  border-radius: 3px;
}

.panel-chat::-webkit-scrollbar-track {
  background-color: transparent;
}
.panel-chat .pannel-copilot,
.panel-chat .pannel-user {
	width: 100%;
	display: flex;
	align-items: flex-start;
	margin-top: 20px;
	padding-right: 10px;
}
.pannel-copilot>img{
	width: 30px;
	height: 30px;
	/* object-fit: cover; */
}
.pannel-user>img {
	width: 30px;
	height: 30px;
}
.pannel_message {
	position: relative;
	max-width: calc(100% - 120px);
	padding: 0 14px;
	border-radius: 10px;
	font-size: 16px;
	word-break: break-all;
}

.pannel_message::after {
	content: '';
	position: absolute;
}

.pannel-copilot {
	justify-content: flex-start;
}

.pannel-copilot img {
	margin-right: 20px;
}

.pannel-copilot .pannel_message {
	background-color: #f5f5f5 ;
	color: #333;
}

.pannel-copilot .pannel_message::after {
	top: 16px;
	left: -12px;
	border-top: 12px solid transparent;
	border-bottom: 12px solid transparent;
	border-right: 13px solid #EEF3FC;
}
.pannel-user .pannel_message {
	background-color: #096dd9;
	color: #fff;
}

.pannel-user .pannel_message::after {
	top: 16px;
	right: -12px;
	border-top: 12px solid transparent;
	border-bottom: 12px solid transparent;
	border-left: 13px solid #096dd9;
}
.pannel-copilot .recommDes_list {
	width: 100%;
	margin-top: 20px;
	text-align: left;
}

.pannel-copilot .recommDes_list text {
	display: inline-block;
	font-size: 24px;
}

.pannel-copilot .recommDes_list text text {
	color: #1a58bd;
}

.pannel-user {
	flex-direction: row-reverse;
}

.pannel-user img {
	margin-left: 20px;
}
.pannel-bottom {
	width: 100%;
	height: 130px;
	padding: 0 10px;
	box-sizing: border-box;
    border-top: 1px solid #ccc;
}
.bottom-container {
	width: 100%;
	height: 100%;
	display: flex;
	align-items: center;
	justify-content: space-between;
	box-sizing: border-box;
}
.voice {
	width: 50px;
	height: 50px;
}

.input-container {
    width: 100%;
	height: 100%;
    font-size: 16px;
	overflow: hidden;
}
.input-container .chat-textarea {
    width: 100%;
    height:100%;
    border: none;
    padding:10px 0;
    font-size: 16px;
    outline: none;
    color: #333;
    resize: none;
    box-sizing: border-box;
    line-height: 1.5;
    overflow-y: auto;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
}

/* Chrome, Safari and Opera */
.input-container .chat-textarea::-webkit-scrollbar {
    display: none;
}
.send {
	width: 50px;
	height: 50px;
}

.bottom-container img {
	width: 50px;
	height: 50px;
}

.voice_btn {
	width: 100%;
	height: 60px;
	text-align: center;
	background: #EEF3FC;
	font-size: 18px;
	line-height: 60px;
	border-radius: 40px;
}

.voice_btn img {
	width: 100%;
	height: 60px;
}

.input-container input {
	width: 100%;
	line-height: 60px;
	border-radius: 10px;
	border: none;
	font-size: 18px;
	background-color: #e7e7e7;
	outline: none;
	color: #333;
	padding: 0 20px;
    box-sizing: border-box;
}
</style>
