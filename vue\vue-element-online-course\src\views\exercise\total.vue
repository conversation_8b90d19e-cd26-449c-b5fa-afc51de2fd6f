<template>
	<div class="exercise-cnt1 ">
		<el-breadcrumb separator-class="el-icon-arrow-right" class="bread_con">
			<el-breadcrumb-item :to="{ path: '/' }">
			  <i class="el-icon-location-outline" />
			</el-breadcrumb-item>
			<el-breadcrumb-item :to="{ path: 'exerciseCenter' ,query:{ id:exerciseBankId,name:exerciseBank<PERSON>ame} }">
			  课程练习
			</el-breadcrumb-item>
			<el-breadcrumb-item>答题结果</el-breadcrumb-item>
		  </el-breadcrumb>
		<div class="h-title">答题结果</div>
		<div class="exercise-box exercise-total-box ">
			<div class="report-box">
				<radialindicator v-if="result != undefined && result.rightRate != undefined" class="percent-box"
					:percentNum="result.rightRate" txt="正确率"></radialindicator>
				<div class="right-box">
					<div class="item2" v-if="result.rightCount != undefined">
						<div class="num">{{ result.rightCount }}道题</div>
						<div class="txt">答对题数</div>
						<div class="blank"></div>
					</div>
					<div class="item2" v-if="result.exercisePaper!= undefined">
						<div class="num">{{ result.exercisePaper.questionNumber }}道题</div>
						<div class="txt">总题数</div>
						<div class="blank"></div>
					</div>
				</div>
				<div class="total-box">
					<div class="title">答题情况</div>
					<div class="item2" style="border: none" v-if="result.rightCount != undefined">
						<img src="/exercise/right.png" />
						<div class="num">{{ result.rightCount }}道题</div>
						<div class="txt">答对题数</div>
					</div>
					<div class="item2" v-if="result.errorCount != undefined">
						<img src="/exercise/wrong.png" />
						<div class="num">{{ result.errorCount }}道题</div>
						<div class="txt">答错题数</div>
					</div>
					<div class="item2" v-if="result.exercisePaper != undefined">
						<img src="/exercise/unanswered.png" />
						<div class="num">
							{{result.exercisePaper.questionNumber - result.quesCount}}道题
						</div>
						<div class="txt">未答题数</div>
					</div>
				</div>
				<div class="btn-analysis" @click="gotoresult()">查看解析</div>
			</div>
		</div>
	</div>
</template>
<script>
	import radialindicator from "./radialIndicator.vue";
	import { getPaperResult } from "@/api/exercise.js";
	export default {
		data () {
			return {
				isLoading: true,
				exerciseRecordId: "",
				result: {},
        exerciseBankId: '',
        exerciseBankName:''
			};
		},
		components: {
			radialindicator,
			// skeleton
		},
		created () {
      this.exerciseBankId = this.$route.query.tkid
      this.exerciseBankName = this.$route.query.tkName
			this.exerciseRecordId = this.$route.query.exerciseRecordId;
			this.getMyResult(this.exerciseRecordId);
		},
		methods: {
			gotoresult () {
				this.$router.push({
					name: "ResultExercise",
					query: {
						exerciseRecordId: this.exerciseRecordId,
            tkid: this.exerciseBankId,
            tkName:this.exerciseBankName
					},
				});
			},
			getMyResult (id) {
				getPaperResult(id).then((res) => {
					this.result = res;
				});
			},
		},
	};
</script>

<style >

</style>
