<template>
  <!-- <div style="width: 100%;height: 100%;box-sizing: border-box;padding: 20px 0;">
    <img
      style="width: 851px;height: 1198px;display: block;margin: 0 auto;"
      src="../../assets/image/static/jianli.png"
    >
  </div> -->
  <div v-html="detail.content" />
</template>
<script>
import { getResumeDetail } from "@/api/talent";
export default {
  data() {
    return {
      detail: {},
    };
  },
  mounted() {},
  created() {
    const id = this.$route.query.id;
    getResumeDetail(id).then((res) => {
      this.detail = res;
    });
  },
  methods: {},
};
</script>
