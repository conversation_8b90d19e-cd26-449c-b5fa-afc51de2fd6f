<template>
  <div>
    <div class="exam" v-if="exam!=undefined&& exam.questions!=undefined&& exam.questions.length>0" @click="getAnsweredNum" >

     <div class="exambody">
      <el-scrollbar  class="tihao-list" scroll-y="true">
		  <div class="tihao-list-box">
			<h2 class="tk-title">
			<svg
			xmlns="http://www.w3.org/2000/svg"
			xmlns:xlink="http://www.w3.org/1999/xlink"
			width="70px" height="4px">
			<path fill-rule="evenodd"  fill="rgb(0, 150, 255)"
			d="M67.875,4.000 C66.770,4.000 65.875,3.105 65.875,2.000 C65.875,0.895 66.770,-0.000 67.875,-0.000 C68.980,-0.000 69.875,0.895 69.875,2.000 C69.875,3.105 68.980,4.000 67.875,4.000 ZM2.875,4.000 C1.770,4.000 0.875,3.105 0.875,2.000 C0.875,0.895 1.770,-0.000 2.875,-0.000 C3.980,-0.000 4.875,0.895 4.875,2.000 C4.875,3.105 3.980,4.000 2.875,4.000 Z"/>
			</svg>
			答题卡
			</h2>
			<div v-for="(qt,j) in exam.questions" class="tk-item">
			<div v-if="qt.questionType==0" class="title" >单选题（每题{{qt.itemscore}}分，共{{qt!=undefined && qt.subList!=undefined?qt.subList.length:0}}题）</div>
			<div v-else-if="qt.questionType==1" class="title" >多选题（每题{{qt.itemscore}}分，共{{qt!=undefined && qt.subList!=undefined?qt.subList.length:0}}题）</div>
			<div v-else-if="qt.questionType==2"  class="title" >判断题（每题{{qt.itemscore}}分，共{{qt!=undefined && qt.subList!=undefined?qt.subList.length:0}}题）</div>
			<div v-else-if="qt.questionType==3"  class="title" >填空题（每题{{qt.itemscore}}分，共{{qt!=undefined && qt.subList!=undefined?qt.subList.length:0}}题）</div>
			<div v-else-if="qt.questionType==6"  class="title" >简答题（每题{{qt.itemscore}}分，共{{qt!=undefined && qt.subList!=undefined?qt.subList.length:0}}题）</div>

			<span
			v-for="(q,index) in qt.subList"
			:class="[q.answer2===undefined||q.answer2==='' || q.answer2.length===0 || (qt.questionType==3 && q.answer2.join('') ==='')  ?'':'blue',question_index==q.order?'active':'']"
			@click="changeIndex(qt.questionType,q.order)"
			>{{index+1}}
			</span>
			</div>
		</div>
     </el-scrollbar>
      <div  v-if="exam!=null && exam.questions!=null"  class="cnt" >
        <el-breadcrumb separator-class="el-icon-arrow-right" class="bread_con">
          <el-breadcrumb-item :to="{ path: '/' }">
            <i class="el-icon-location-outline" />
          </el-breadcrumb-item>
          <el-breadcrumb-item :to="{ path: 'exerciseCenter',query:{ id:exerciseBankId,name:exerciseBankName}  }">
            课程练习
          </el-breadcrumb-item>
          <el-breadcrumb-item>{{exam.name}}</el-breadcrumb-item>
        </el-breadcrumb>
        <div class="questions-list-box">
          <div class="" v-for="(qt,j) in exam.questions" >
            <div class="title" v-if="qt.questionType==0">单选题（每题{{qt.itemscore}}分，共{{qt!=undefined && qt.subList!=undefined?qt.subList.length:0}}题）</div>
            <div class="title" v-else-if="qt.questionType==1">多选题（每题{{qt.itemscore}}分，共{{qt!=undefined && qt.subList!=undefined?qt.subList.length:0}}题）</div>
            <div class="title" v-else-if="qt.questionType==2">判断题（每题{{qt.itemscore}}分，共{{qt!=undefined && qt.subList!=undefined?qt.subList.length:0}}题）</div>
            <div class="title" v-else-if="qt.questionType==3">填空题（每题{{qt.itemscore}}分，共{{qt!=undefined && qt.subList!=undefined?qt.subList.length:0}}题）</div>
            <div class="title" v-else-if="qt.questionType==6">简答题（每题{{qt.itemscore}}分，共{{qt!=undefined && qt.subList!=undefined?qt.subList.length:0}}题）</div>
            <!-- <div v-for="count in 3"> -->
              <!-- {{ _data[]}} -->
              <!-- {{$data['list'+(j*3 + count)]}} -->
              <div v-if="qt.questionType==0">
                <div class="test-item-div" v-for="(ex,index) in list1" :id="'anchor-'+ qt.questionType +'-'+ ex.order">
                  <!-- {{ex}} -->
                  <question :ex='ex' :index="index" @flushFun = 'flush'></question>
                </div>
                <div class="test-item-div" v-for="(ex,index) in list2" :id="'anchor-'+ qt.questionType +'-'+ ex.order">
                  <question :ex='ex' :index="list1.length + index"  @flushFun = 'flush'></question>
                </div>
                <div class="test-item-div" v-for="(ex,index) in list3" :id="'anchor-'+ qt.questionType +'-'+ ex.order">
                  <question :ex='ex' :index="list1.length + list2.length + index"  @flushFun = 'flush'></question>
                </div>
              </div>
              <div v-if="qt.questionType==1">
                <div class="test-item-div" v-for="(ex,index) in list4" :id="'anchor-'+ qt.questionType +'-'+ ex.order">
                  <question :ex='ex' :index="index"  @flushFun = 'flush'></question>
                </div>
                <div class="test-item-div" v-for="(ex,index) in list5" :id="'anchor-'+ qt.questionType +'-'+ ex.order">
                  <question :ex='ex' :index="list4.length + index"  @flushFun = 'flush'></question>
                </div>
                <div class="test-item-div" v-for="(ex,index) in list6" :id="'anchor-'+ qt.questionType +'-'+ ex.order">
                  <question :ex='ex'  :index=" list4.length + list5.length+ index"  @flushFun = 'flush'></question>
                </div>
              </div>
              <div v-if="qt.questionType==2">
                <div class="test-item-div" v-for="(ex,index) in list7" :id="'anchor-'+ qt.questionType +'-'+ ex.order">
                  <question :ex='ex' :index="index"  @flushFun = 'flush'></question>
                </div>
                <div class="test-item-div" v-for="(ex,index) in list8" :id="'anchor-'+ qt.questionType +'-'+ ex.order">
                  <question :ex='ex' :index="list7.length+ index"  @flushFun = 'flush'></question>
                </div>
                <div class="test-item-div" v-for="(ex,index) in list9" :id="'anchor-'+ qt.questionType +'-'+ ex.order">
                  <question :ex='ex' :index="list7.length +list8.length+ index"  @flushFun = 'flush'></question>
                </div>
              </div>
              <!-- 填空题 -->
              <div v-if="qt.questionType==3">
                <div class="test-item-div" v-for="(ex,index) in list10" :id="'anchor-'+ qt.questionType +'-'+ ex.order">
                  <question :ex='ex' :index="index"  @flush = 'flush'></question>
                </div>
                <div class="test-item-div" v-for="(ex,index) in list11" :id="'anchor-'+ qt.questionType +'-'+ ex.order">
                  <question :ex='ex' :index="list10.length+ index"  @flush = 'flush'></question>
                </div>
                <div class="test-item-div" v-for="(ex,index) in list12"  :id="'anchor-'+ qt.questionType +'-'+ ex.order">
                  <question :ex='ex' :index="list10.length +list11.length+ index"  @flush = 'flush'></question>
                </div>
              </div>
              <!--简答题-->
              <div v-if="qt.questionType==6">
                <div class="test-item-div" v-for="(ex,index) in list13" :id="'anchor-'+ qt.questionType +'-'+ ex.order">
                  <question :ex='ex' :index="index"  @flush = 'flush'></question>
                </div>
                <div class="test-item-div" v-for="(ex,index) in list14" :id="'anchor-'+ qt.questionType +'-'+ ex.order">
                  <question :ex='ex' :index="list13.length+ index"  @flush = 'flush'></question>
                </div>
                <div class="test-item-div" v-for="(ex,index) in list15" :id="'anchor-'+ qt.questionType +'-'+ ex.order">
                  <question :ex='ex' :index="list13.length +list14.length+ index"  @flush = 'flush'></question>
                </div>
              </div>
            <!-- </div> -->
          </div>
        </div>
      </div>
      <div class="right-box">
          <!--提交试卷-按钮-->
          <div class="submitPage-btn-box">
            <button square type="info" native-type="button" @click = "submitPage()"  class="submitPage-btn">提交试卷 </button>
          </div>
        <!--剩余时间及进度-->
          <!-- <div  class="lefttime">
            <span class="span1">
              剩余时间
              <span ref="lefttime" class="txt">{{time}}</span>
            </span>
            <span class="span2">
              当前进度
              <span class="txt">{{answeredNum}}/{{qlength}}</span>
              <span class="process-box"><span :style="{ width:answeredNum/qlength*100+'%' }"></span></span>
            </span>
          </div> -->
        </div>
      </div>
    <!--提交考卷-->

	 <el-dialog title="" :visible.sync="IsshowPopup2" class="submitpage" :show-close="false">
    <div class="submit-page-box">
        <div class="h_title">
          <div class="title">提交试卷</div>
          <a  class="close" @click="IsshowPopup2=false">&times;</a>
        </div>
        <div class="p">共<span>{{qlength}}</span>题，已答<span>{{answeredNum}}</span>题，未答<span style="color: red;">{{qlength-answeredNum}}</span>题;</div>
        <div class="p">交卷后无法再进行答题，您是否确认交卷。如确认交卷请输入下方验证码后确认交卷。</div>
        <div  class="code-box">
          <div class="col-span4">输入验证码</div>
          <div class="col-span4" >
            <input type="text" width="100%" class="codetxt" v-model="code2"/>
          </div>
          <div class="col-span8" >
            <canvas  ref="" id="mycanvas" width="80" height="25" @click="makeCode"></canvas>
            <br>
            <!-- <a @click="makeCode">换一张</a> -->
          </div>
        </div>
      </div>
      <div class="code-opr">
        <button type="info"  loading-text="提交试卷" block :disabled="code2!=code" @click="submitdata" class="submit_btn">提交试卷</button>
        <button type="type"  block @click="continueAnswer" class="continue_btn">继续答题</button>
      </div>
      <div class="loading"  v-if="submitLoading">
        <img src="/exercise/loading.gif" />
        <div class="txt" >5s后跳转到答题结束页</div>
      </div>
	 </el-dialog>
    </div>
    <div v-if="loading" class="q-loading">
       <img src="/exercise/icon_loading.png" />
       <div class="txt">题目加载中</div>
    </div>

  </div>

</template>

<script>
import {
	getPaper,
	SubmitPaper
}from '@/api/exercise.js'
// import { Notify,ImagePreview } from 'vant';
// import { getMyProfile } from "@/api/user";
import question from './question.vue'
// import screenfull from 'screenfull'
import moment from "moment";
export default {
  name: "exam",
   components:{
    question
  },
  data() {
    return {
      loading:true,
      // 验证码输入后提交试卷按钮loading防止重点
      submitLoading: false,
      question_index: 0, //题目序号，order
      exam: null,
      IsshowPopup:false, //提卡的popup
      IsshowPopup2:false,
      time:"",
      code:'',
      code2:'',
      isSubmitted :false ,//是否交卷成功
      timer2:null,  //定时5分钟提交过程答题记录
     // timer3:null, // 倒计时提交考试
      CanSubmit:false ,  //是否能够交卷
      answerOrder:['A','B','C','D','E','F','G','H','I','J','K','L','M','N'],
      answeredNum:0,
      qlength:0 , // 问题长度
      // userInfo:null,
      // showPopover:false,
      fullscreen: true,
      leaveCount:0,
      currentTime:'',
      tab_flag:false , //切换浏览器Tab
      // 题目分割成9份,每个题型分3份
      typeList:[0,1,2,3,6], //题型 单选题,多选题，判断题，填空题，简答题
      list1:[],
      list2:[],
      list3:[],

      list4:[],
      list5:[],
      list6:[],

      list7:[],
      list8:[],
      list9:[],

      list10:[],
      list11:[],
      list12:[],

      list13:[],
      list14:[],
      list15:[],
      exerciseBankId:'',
      exerciseBankName:''
    };
  },

  watch:{
    isSubmitted:function(val){
        if(this.isSubmitted) {
          clearInterval(this.timer2)
         // clearInterval(this.timer3)
        }
    },

  },

   async mounted() {
    // if (screenfull.isEnabled) { // 全屏
    //     screenfull.request() // 默认全屏显示
    // }
    this.exerciseBankId = this.$route.query.tkid
    this.exerciseBankName = this.$route.query.tkName
    window.addEventListener('resize',this.onresize) //切换全屏

    window.addEventListener('beforeunload',this.onbeforeunload) //是否关闭或刷新页面

    document.addEventListener('visibilitychange', this.onVisibilityChange) //切换Tab
    // await getCurrentTime().then(res=>{  // 获取当前时间
    //   this.currentTime = res
    // })
    this.getExamdata()
    //自动提交，每隔5分钟
    this.timer2 = setInterval(()=>{
      this.handleData(0)
    },60000) //600000
  },
  destroyed(){
    window.removeEventListener('resize',this.onresize)
    window.removeEventListener('beforeunload',this.onbeforeunload)
    document.removeEventListener('visibilitychange', this.onVisibilityChange);
    clearInterval(this.timer2)
    //clearInterval(this.timer3)
  },
  methods: {
    // onresize(){
    //     if(!this.fullscreen&&this.exam!=null) {
    //        this.leaveCount++
    //        Notify({ type: 'primary', message: '考试期间，禁止离开页面,你已经离开'+ this.leaveCount +'次' })
    //     } else{
    //       getCurrentTime().then(res=>{  // 获取当前时间
    //         this.currentTime = res
    //         this.tab_flag = true
    //       })
    //     }
    //      this.fullscreen = !this.fullscreen
    // },
	flush(){
		this.$forceUpdate()
	},
    onbeforeunload(e){
      var e = window.event||e;
      e.returnValue=("你正在进行考试，确定离开当前页面吗？");
    //   setTimeout(function(){
    //        getCurrentTime().then(res=>{  // 获取当前时间
    //         this.currentTime = res
    //         this.tab_flag = true
    //       })
    //   }, 50)
    },
    onVisibilityChange(){
      let _this = this
      if (document.visibilityState === 'visible'&&_this.exam!=null) {
          _this.leaveCount++
          //Notify({ type: 'primary', message: '考试期间，禁止离开页面,你已经离开'+ _this.leaveCount +'次' })
		    //this.$message.info('考试期间，禁止离开页面,你已经离开'+ _this.leaveCount +'次')
        //   getCurrentTime().then(res=>{  // 获取当前时间
        //     this.currentTime = res
        //     this.tab_flag = true
        //   })
      }
    },
    startTimer(){
      let nowTime = moment(this.exam.currentTime)
      let startTime  = moment(this.exam.startDate)
      let endTime = moment(this.exam.endDate)
      if(this.exam.allowRepeatSubmit||this.exam.examinationType===1) //允许重复考试或培训内的考试 考试开始时间为当前时间
      {
        startTime  = moment(this.exam.currentTime)
        endTime = moment(this.exam.currentTime).add(this.exam.timeLong,'m')
      }

      let leftTime = endTime - nowTime
    //   this.timer3 = setInterval(()=>{
    //       // 开考xx分钟后，允许交卷（不能大于考试时长）
    //       if(this.tab_flag) { //切换浏览器Tab后，重新赋值当前时间
    //         nowTime = moment(this.currentTime)
    //         if(endTime-nowTime >0)
    //         {
    //           leftTime=endTime - nowTime
    //         }
    //         else
    //         {
    //           leftTime=0
    //           this.flag = true
    //         }
    //       }
    //       this.tab_flag = false

    //       if(this.exam!=undefined&& this.exam.submitLimitTime !=undefined  &&　nowTime > (startTime + this.exam.submitLimitTime*60*1000))
    //       {
    //         this.CanSubmit = true
    //       }
    //       if(this.flag == true){
    //         clearInterval(this.timer2)
    //         clearInterval(this.timer3)
	// 		this.$message.info('答题时间已到，10秒后将自动提交')
    //         setTimeout(()=>{
    //           this.handleData(1)
    //         },10000)
    //       }
    //       leftTime = leftTime - 1000
    //     if(this.exam!=null)  this.timeDown(parseInt(leftTime/1000))
    //     nowTime = nowTime + 1000
    //   },1000)
    },
    getExamdata(){
      this.loading=true
      getPaper({
        exercisePaperId: this.$route.query.examId,
      }).then(
        res => {
          this.exam = res;
          this.qlength = this.exam.questions.length
          if (this.exam.replyContent != undefined && this.exam.replyContent != "" && this.exam.replyContent != null)
            this.exam.replyContent = JSON.parse(this.exam.replyContent); //之前保存的数据
          if (this.exam.questions.length > 0) {
            this.exam.questions.forEach(element => {

              if (this.exam.replyContent != null && this.exam.replyContent != "") {
                var q = this.exam.replyContent.find(item => item.Q == element.id)
                // console.log(q)
                if (q) {
                  if (element.questionType == 0) element.answer2 = q.O !== null ? q.O[0] : undefined
                  if (element.questionType == 1) element.answer2 = q.O !== null ? q.O : undefined
                  if (element.questionType == 2) element.answer2 = q.J !== null ? (q.J === 1 ? "正确" : "错误") : ''
                  if (element.questionType == 3) {
                    // for(let i =0;i++;i<q.ba.length){
                    //   element.answer.push(q.ba[i])
                    // }
                    element.answer2 = q.BA
                  }
                  if (element.questionType == 6) element.answer2 = q.RA!== null ? q.RA:''
                }
              }
              // 题目选项转json
              element.questionStem = JSON.parse(element.questionStem);
              //题干中的图片
              if (element.questionStem.Title_Imgs != null) element.questionStem.Title_Imgs = element.questionStem.Title_Imgs.split(",");

              //选项中的图片
              if (element.questionStem!=undefined &&  element.questionStem.Options!=undefined&& element.questionStem.Options.length > 0) {
                element.questionStem.Options.forEach(item => {
                  if (item.Images != null) item.Images = item.Images.split(",");
                })
              }
              // 填空题
              if(element.questionType == 3){
                //console.log(element)
               element.questionStem.TitleArr = element.questionStem.Title.split(/_+/)
              }
              // 多选题
              if(element.questionType == 1 && element.answer2==undefined){
                element.answer2 = []
              }

            });
          }


         this.exam.questions =  this.GroupByType(this.exam.questions)
         // console.log(this.exam.questions)
         this.exam.questions.forEach(element => {
           if (this.exam.isDisorder) { //是否乱序
              element.subList.sort(function() {
                return Math.random() - 0.5;
              });
            }
         })
         // console.log(this.exam.questions)
         // 分割题目
         for (let i = 0; i <this.typeList.length; i++) {
          this.sliceData(i)
         }

         // console.log(this.$data)

         this.startTimer() //开启倒计时
         this.loading = false
        },
        error => {
          //console.log(error);
          this.loading=false
          window.removeEventListener('resize',this.onresize)
          window.removeEventListener('beforeunload',this.onbeforeunload)
          document.removeEventListener('visibilitychange', this.onVisibilityChange);
          clearInterval(this.timer2)
          //clearInterval(this.timer3)
          setTimeout(()=>{
            let returnUrl=this.$route.query.returnUrl
            if(returnUrl){
              window.open(returnUrl,'_self')
              return
            }
            this.$router.push({ name: "home" });
          },5000)
          this.$message.error(error.error.message+'，5秒后自动返回首页');
          //screenfull.exit()
        }
      )
    },
    sliceData(num){ //type 题型
      let subGroupLength = 1
      var ob = this.exam.questions.find(x=>x.questionType==this.typeList[num])
      if(ob!=undefined && ob.subList !=undefined)
      {subGroupLength = Math.ceil(ob.subList.length/3)}
      else {return}
      let j = 0
      if(ob.subList.length<3){
        this.$data['list'+  (num*3+1)] = ob.subList
      } else {
        for (let i = 0; i <3 ; i++) { // 每个题型分三份
          // console.log(type*3+i+1)
          if(i!=2) {
            this.$data['list'+  (num*3+i+1)] =  ob.subList.slice(j, j += subGroupLength)
          } else {
            this.$data['list'+  (num*3+i+1)] =  ob.subList.slice(j)
          }
        }
      }
    },
    handleData(submitType){
      if(submitType==1) {
        clearInterval(this.timer2) //正式提交前 清除定时提交定时器 防止数据覆盖
      }
      let data = {
		    exerciseRecordId: this.exam.exerciseRecordId,
		    exercisePaperId: this.exam.id,
        submitType: submitType, //提交状态 0：系统每隔几分钟（5-10分钟）自动提交 1：学生答完提交
        replyContent:[],
        replyContentS:'',
        quitCount:this.leaveCount
      }
      let data_arr = []
      // this.exam.questions.forEach(item=>{
      //   data_arr = data_arr.concat(item.subList)
      // })

      for (let index = 0; index < this.typeList.length*3;index++) {
        //const element = array[index];
        data_arr = data_arr.concat(this.$data['list'+  (index+1)] )
      }

      // console.log(data_arr)
      data_arr.forEach(item=>{
        var itemarr =   {
          Q: item.id, //题目id
          B: item.questionBankId,
          O: null, //选择答案
          J: null, //判断答案
          BA:[], // 填空题答案
          RA:null // 简答题答案
        }
        // 未选择选项时
        if(item.answer2==undefined || item.answer2==''){
          itemarr.O = null
          itemarr.J = null
        }
        else if(item.questionType == 0) itemarr.O = [item.answer2]
        else if(item.questionType == 1) itemarr.O = item.answer2.sort((a,b)=>{ return a-b})
        else if(item.questionType == 2) itemarr.J = item.answer2=="正确"?1:0
        // 填空题
        else if(item.questionType == 3){
          itemarr.BA = item.answer2
          // var strList  = item.questionStem.Title.split(/_+/)
          // for(let i = 0 ;i<strList.length-1;i++){
          //   itemarr.ba.push(item.answer[i])
          // }
        }
        // 简答题
        else if(item.questionType == 6){
          itemarr.RA = item.answer2
        }
        data.replyContent.push(itemarr)
      })
      data.replyContentS = JSON.stringify(data.replyContent )
      //console.log(data)
      if(submitType==1) {data.replyContentS=""}
      if(submitType==0) {data.replyContent=[]}
       //console.log(data)
      SubmitPaper(data).then(()=>{
        if(submitType==1)
        {
		      this.$message.success('交卷成功,5s后跳转到答题结束页')
          this.isSubmitted = true
          // screenfull.exit()
          //判断是否存在returnUrl
          // let returnUrl=this.$route.query.returnUrl
          // if(returnUrl){
          //   window.removeEventListener('resize',this.onresize)
          //   window.removeEventListener('beforeunload',this.onbeforeunload)
          //   document.removeEventListener('visibilitychange', this.onVisibilityChange);
          //   // window.open(returnUrl,'_self')
          //   return
          // }
          setTimeout(() => {
            this.submitLoading = false;
            this.$router.push({
              name:"TotalExercise",
              query:{
                exerciseRecordId:this.exam.exerciseRecordId,
                tkid: this.exerciseBankId,
                tkName:this.exerciseBankName
              }
            })
					}, 5000)

        }
      },
      error => {
        this.submitLoading = false
        if(submitType==1)
        {

		  this.$message.error('操作失败，请稍后重试')
          this.code2 = ''
          this.submitPage()
        }
        //console.log("11")
      })
    },
    getAnsweredNum(){
      this.answeredNum = this.qlength
      this.exam.questions.forEach(element=>{
        let arr =  element.subList.filter(item => item.answer2===undefined || item.answer2==='' || item.answer2.length==0)
        this.answeredNum = this.answeredNum - arr.length
      })

    },
    showPopup(){ //打开题卡
      this.IsshowPopup = true
    },
    changeIndex(type,index){
      this.question_index = index
      this.IsshowPopup = false
      this.getAnsweredNum() //计算已答题数目
      this.goAnchor('#anchor-'+ type +'-'+(index))
    },
    goAnchor (selector) { //锚点跳转
      const anchor = this.$el.querySelector(selector)
      this.$nextTick(() => {
        window.scrollTo(0,anchor.offsetTop-65);
      })
    },
    submitPage(){
      // console.log( this.exam)
      this.IsshowPopup2 = true;
      this.getAnsweredNum()
      setTimeout(()=>{
          this.makeCode()
      },500)
    },
    submitdata(){
      this.submitLoading = true;
      this.handleData(1)
    },
    continueAnswer(){
      this.IsshowPopup = false
      this.IsshowPopup2 = false
    },
    makeCode(){  //生成验证码
      var num = 4
      this.code = ''
      var canvas = document.getElementById("mycanvas")

      var ctx = canvas.getContext("2d");

      ctx.clearRect(0,0,canvas.width,canvas.height);  //清除画布

      for(let i=0;i<num;i++){
       var f = this.randomNum(0,9)
       ctx.font="18px Georgia";
       ctx.fillText(f,15*i+10,15);
       ctx.fillStyle = this.randomColor(0, 255);
       this.code += f
      }
    },
    // timeDown (leftTime) {
    //    if(leftTime <= 0){
    //       this.flag = true
    //       this.$emit('time-end')
    //       this.time="00:00:00"
    //       return
    //     }
    //     let d = parseInt(leftTime/(24*60*60))
    //     let h = this.formate(parseInt(leftTime/(60*60)%24))
    //     let m = this.formate(parseInt(leftTime/60%60))
    //     let s = this.formate(parseInt(leftTime%60))
    //     if(d==0) this.time = `${h}:${m}:${s}`
    //     else this.time = `${d}天 ${h}:${m}:${s}`

    // },
    formate (time) {
        if(time>=10){
          return time
        }else{
          return `0${time}`
        }
    },
    randomNum(min, max) {
      return Math.floor(Math.random() * (max - min) + min);
    },
    randomColor(min, max) {
      let r = this.randomNum(min, max);
      let g = this.randomNum(min, max);
      let b = this.randomNum(min, max);
      return "rgb(" + r + "," + g + "," + b + ")";
    },
    GroupByType(items){ // 按照类型分组
      let newArr = [];
      items.forEach((item, i) => {
        let index = -1;
        //some用来查找数组中是否存在某个值
        let isExists = newArr.some((newItem, j) => {
          if (item.questionType == newItem.questionType) {
            index = j;
            return true;
          }
        })
        if (!isExists) {
          newArr.push({
            questionType: item.questionType,
            itemscore:item.score,
            // classGroupText:item.classGroup==null?'未分类':item.classGroup,
            subList: [item]
          })
        } else {
          newArr[index].subList.push(item);
        }

      })
      return newArr
    }
  }
};
</script>
<style lang="scss"  >

.q-loading{ text-align: center;padding: 80px 0;}
.q-loading img{ width: 30px;margin-bottom: 20px; animation:rotation 0.8s infinite;}
@keyframes rotation{
    from {transform: rotate(0deg);}
    to {transform: rotate(360deg);}
}
</style>

