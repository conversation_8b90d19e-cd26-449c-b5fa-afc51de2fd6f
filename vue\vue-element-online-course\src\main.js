import Vue from 'vue'

import 'normalize.css/normalize.css' // A modern alternative to CSS resets

import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
// import { set } from '../observer/index'
import './styles/index.css' // global css

import App from './App'
import store from './store'
import router from './router'
import axios from './axios'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'
import '@/icons'
import '@/permission'

NProgress.configure({ showSpinner: false })
Vue.use(require('vue-moment'))

import * as filters from '@/filters/index'
Object.keys(filters).forEach(key => {
    Vue.filter(key, filters[key])
})

NProgress.configure({
  easing: 'ease',  // 动画方式
  speed: 800,  // 递增进度条的速度
  showSpinner: false, // 是否显示加载ico
  trickleSpeed: 200, // 自动递增间隔
  minimum: 0.3 // 初始化时的最小百分比
})

router.beforeEach((to, from, next) => {
  NProgress.start();
  next();
});
  // 路由跳转后钩子函数中 - 执行进度条加载结束
router.afterEach(() => {
  NProgress.done();
});
Vue.prototype.$axios = axios
// set ElementUI lang to EN
// Vue.use(ElementUI, { locale })
// 如果想要中文版 element-ui，按如下方式声明
Vue.use(ElementUI)

Vue.config.productionTip = false

new Vue({
  el: '#app',
  router,
  store,
  render: h => h(App)
})
