<template>
    <div class="profile_info_con">
        <SideMenu />
        <div class="profile_info_box">
            <div class="tabs">
                <a class="active">我的题库</a>
            </div>
            <div class="myTikuList">
                <div class="ti-items" v-for="item in tikuList">
                    <div class="ti-temp">
                        <img :src="item.thumbnailUrl||'/exercise/tk-icon.png'" />
                        <span class="title">{{ item.introduce }}</span>
                    </div>

                    <button
                        :disabled="!item.cloudLearn"
                        @click="EnterCenter(item)"
                    >
                        进入题库
                    </button>
                </div>
                <NoContent v-if="tikuList.length == 0"></NoContent>
                <el-pagination
                    v-if="params.total > params.MaxResultCount"
                    class="my_pagination"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="params.page"
                    :page-sizes="[10, 20, 40]"
                    :page-size="params.MaxResultCount"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="params.total"
                ></el-pagination>
            </div>
        </div>
    </div>
</template>
<script>
import { getbanks } from "@/api/exercise.js";
import NoContent from "@/components/NoContent";
import SideMenu from "@/layout/SideMenu2.vue";
export default {
    data() {
        return {
            tikuList: [],
            params: {
                SkipCount: 0,
                MaxResultCount: 10,
                cloudLearn: true,
                total: 0,
                page: 1,
            },
        };
    },
    components: {
        NoContent,
        SideMenu,
    },
    created() {
        this.getTikuList();
    },

    methods: {
        handleSizeChange(val) {
            this.params.MaxResultCount = val;
            this.getTikuList();
        },
        handleCurrentChange(val) {
            this.params.page = val;
            this.getTikuList();
        },
        EnterCenter(item) {
            this.$router.push({
                name: "ExerciseCenter",
                query: { id: item.id, name: item.name },
            });
        },
        getTikuList() {
            let data = {
                CourseCategoryId: this.params.CourseCategoryId,
                SkipCount: this.params.MaxResultCount * (this.params.page - 1),
                MaxResultCount: this.params.MaxResultCount,
                cloudLearn: this.params.cloudLearn,
            };
            getbanks(data).then((res) => {
                this.params.total = res.totalCount;
                if (res.items.length > 0) {
                    this.tikuList = res.items;
                }
            });
        },
    },
};
</script>
<style scoped>
    .myTikuList{
        padding: 0 20px;
        box-sizing: border-box;
    }
    .myTikuList .ti-items{
        margin-top: 20px;
    }
    .ti-items{
        width: 100%;
        height: 170px;
        box-sizing: border-box;
        padding: 20px;
        background: #fff;overflow: hidden;
        display: flex;
        justify-content: space-between;
        border-bottom: 1px solid #ccc;
    }
    .ti-temp{
        width: 620px;
        display: flex;
    }
    .ti-temp img{height: 130px;width: 130px;object-fit: cover;display: inline-block;vertical-align: middle;}

    .ti-items .desc{line-height: 22px;
    font-size: 14px;
    color: #666;
    max-height: 64px;
    overflow: hidden;}
    .ti-temp .title{
        font-size: 16px;
        color: #333333;
        height:60px;
        line-height: 28px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        padding:0 20px;
        -webkit-line-clamp: 2;}
    .ti-items button{margin-top:28px;width: 180px;height: 40px;border: 2px solid #096DD9;border-radius: 20px;padding: 0;background: #fff;color: #096DD9;cursor: pointer;}
    .ti-items button:disabled{opacity: 0.5;}
</style>
