<template>
  <div class="profile_info">
    <span class="title">个人中心</span>
    <router-link
      :to="{ name: 'myTotal'}"
      :class="[routeName == 'myTotal'? 'menuItemActive' : 'menuItem']"
    >
      <span class="text">我的学分</span>
    </router-link>
    <router-link
      :to="{ name: 'myRecord'}"
      :class="[routeName == 'myRecord'? 'menuItemActive' : 'menuItem']"
    >
      <span class="text">学习记录</span>
    </router-link>
    <router-link
      :to="{ name: 'myComment'}"
      :class="[routeName == 'myComment'? 'menuItemActive' : 'menuItem']"
    >
      <span class="text">我的评论</span>
    </router-link>

    <router-link
      :to="{ name: 'profile' }"
      :class="[routeName == 'profile' ? 'menuItemActive' : 'menuItem']"
    >
      <span class="text">我的信息</span>
    </router-link>
    <router-link
      :to="{ name: 'changePassword' }"
      :class="[routeName == 'changePassword' ? 'menuItemActive' : 'menuItem']"
    >
      <span class="text">密码修改</span>
    </router-link>
  </div>
</template>
<script>
  export default {
    name: "SideMenu",
    data() {
      return {
        showList: 0,
        routeName: "Profile",
        query: ''
      };
    },
    computed: {},
    watch: {
      $route: {
        handler: function (route) {
          this.routeName = route.name;
          this.query = route.query
        },
        immediate: true
      }
    },
    methods: {
      showListClick(objc) {
        this.showList = objc;
      }
    }
  };
</script>
