import { login, logout, getInfo,getMyProfile, loginByPhone } from '@/api/user'
import { getToken, setToken, removeToken, getphoneValid, getpasswordValid, setpasswordValid, removepasswordValid, setphoneValid, removephoneValid,getIsNewUser,setIsNewUser,removeIsNewUser } from '@/utils/auth'
import { resetRouter } from '@/router'
// import { getCartList } from '@/api/vms'
import moment from 'moment'

const getDefaultState = () => {
  return {
    token: getToken(),
    name: '',
    given_name:'',
    avatar: '',
    login_flag:false,
    notice_flag:window.localStorage.getItem("notice_flag")==undefined?true:(window.localStorage.getItem('noticetime')== moment().format('YYYY-MM-DD')?false:true),
    role: '',
    sub:'',
    passwordValid: getpasswordValid()||false,
    phoneValid: getphoneValid()||false,
    isNew:getIsNewUser()||false,
    //badgeNum:0
  }
}

const state = getDefaultState()

const mutations = {
  SET_PASSWORDVALID: (state, passwordValid) => {
    state.passwordValid = passwordValid
  },
  SET_PHONEVALID: (state, phoneValid) => {
    state.phoneValid = phoneValid
  },
  RESET_STATE: (state) => {
    Object.assign(state, getDefaultState())
  },
  SET_TOKEN: (state, token) => {
    state.token = token
  },
  SET_NAME: (state, name) => {
    state.name = name
  },
  SET_GIVEN_NAME: (state, given_name) => {
    state.given_name = given_name
  },
  SET_SUB: (state, sub) => {
    state.sub = sub
  },
  SET_AVATAR: (state, avatar) => {
    state.avatar = avatar
  },
  TOGGLE_LOGIN_BOX:(state,login_flag) =>{
    state.login_flag = login_flag
  },
  TOGGLE_NOTICE_BOX:(state,notice_flag) =>{
    state.notice_flag = notice_flag
  },
  SET_ROLE: (state,roles) =>{
    state.role = roles
  },
  SET_ISNEW: (state,isNew) =>{
    state.isNew = isNew
  },
  // BADGE_NUM: (state, badgeNum) =>{
  //   state.badgeNum = badgeNum
  // },
}

const actions = {
  // user login
  login({ commit }, userInfo) {
    // const { username, password } = userInfo

    return new Promise( (resolve, reject) => {
      login(userInfo).then( response => {
        commit('SET_TOKEN', response.access_token)
        setToken(response.access_token)
        resolve()
      }).catch(error => {
        reject(error)
      })
    })

  },
  loginByPhone({ commit }, userInfo) {
    // const { username, password } = userInfo

    return new Promise( (resolve, reject) => {
      loginByPhone(userInfo).then( response => {
        if(JSON.parse(response.rawData).access_token) {
          commit('SET_TOKEN', JSON.parse(response.rawData).access_token)
          setToken(JSON.parse(response.rawData).access_token)
          resolve()
        }else {
          reject()
        }

      }).catch(error => {
        reject(error)
      })
    })

  },
  // get user info
  getInfo({ commit }) {
    return new Promise((resolve, reject) => {
      getMyProfile().then(response => {
        // console.log('getInfo',response)
        if (!response) {
          return reject('验证失败，请登录.')
        }
        // var roles = response.role.split(',')
        // var result = roles.find(obj => obj == 'admin')
        commit('SET_NAME', response.userName)
        commit('SET_GIVEN_NAME', response.userName)
        commit('SET_SUB',response.id)
        //localStorage.setItem('sub',response.sub)
        commit('SET_ROLE', response.extraProperties.Roles)
        commit('SET_PHONEVALID', response.isPhoneNumberAuth)
        setphoneValid(response.isPhoneNumberAuth)

        var isNewUser= false//response.extraProperties.IsNew==undefined?true:response.extraProperties.IsNew

        commit('SET_ISNEW', isNewUser)
        setIsNewUser(isNewUser)

        commit('SET_PASSWORDVALID', !isNewUser)
        setpasswordValid(!isNewUser)
        // console.log('isNewUser',isNewUser )
        localStorage.setItem("USER.SUB", response.id);

        resolve(response)
      }).catch(error => {
        reject(error)
      })
    })
  },
  settoken({commit},token){
    commit("SET_TOKEN",token)
    setToken(token)
  },
  // user logout
  logout({ commit, state }) {
    return new Promise((resolve, reject) => {
      logout(state.token).then(() => {
        removeToken() // must remove  token  first
        resetRouter()
        commit('RESET_STATE')
        localStorage.removeItem("USER.SUB");
        resolve()
      }).catch(error => {
        reject(error)
      })
    })
  },

  // remove token
  resetToken({ commit }) {
    return new Promise(resolve => {
      removeToken() // must remove  token  first
      removepasswordValid()
      removephoneValid()
      removeIsNewUser()
      commit('RESET_STATE')
      resolve()
    })
  },
  toggleloginbox({commit},flag){
    commit("TOGGLE_LOGIN_BOX",flag)
  },
  togglenoticebox({commit},flag){
    window.localStorage.setItem("notice_flag",flag)
    window.localStorage.setItem('noticetime',moment().format('YYYY-MM-DD'))
    commit("TOGGLE_NOTICE_BOX",flag)
  },
  passwordValid({commit},flag){
    //console.log('-----',flag)
    commit("SET_PASSWORDVALID",flag)
    setpasswordValid(flag)
  },
  phoneValid({commit},flag){
    commit("SET_PHONEVALID",flag)
    setphoneValid(flag)
  },
  isNew({commit},flag){
    commit("SET_ISNEW",flag)
    setIsNewUser(flag)
  },
  // badgeNum({ commit }) {
  //   return new Promise((resolve, reject) => {
  //     getCartList().then(response => {
  //       commit('BADGE_NUM', response.totalCount);
  //       resolve(response)
  //     }).catch(error => {
  //       reject(error)
  //     })
  //   })
  // }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}

