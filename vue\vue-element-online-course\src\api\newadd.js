import axios from '@/axios'
//名师
export function getTeachers(params) {
  return axios.gets('/api/cms/public/teachers',params)
}
//AI专家
export function getAI(params) {
    return axios.gets('/api/cms/AiAppInfos/web',params)
}
// 资讯
export function getNewsAll(params) {
    return axios.gets('/api/notice/announcements/public/system', params)
}
//名师详情
export function getTeacherDetail(id) {
    return axios.gets('/api/cms/public/teachers/get-by-id?id=' + id)
}
//PDF资讯详情
export function getNewsInfo4Pdf(data){
  return axios.gets('/api/notice/announcements/public/detail4Pdf',data)
}
