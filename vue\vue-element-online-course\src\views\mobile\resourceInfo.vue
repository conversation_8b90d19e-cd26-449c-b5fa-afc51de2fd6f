<template>
  <div
    v-loading="loading"
    element-loading-background="rgba(0, 0, 0, 0.8)"
    style="height: 100%; background: #3c3f4a"
  >
    <div
      v-if="currentRes != undefined"
      class="wx-Res-page"
    >
      <!--视频-->
      <div
        v-if="currentRes.fileType != undefined && currentRes.fileType == '.mp4'"
        class="wx-videoBox"
      >
        <!-- <video id="myPlayer" /> -->
        <video
          id="myPlayer"
          ref="videoRef"
          class="video-js vjs-default-skin vjs-big-play-centered"
          controls
          preload="auto"
        />
        <!-- <video id="my-video" class="video-js" controls preload="auto" width="960" height="400"
                  data-setup="{}"> -->
      </div>

      <!--pdf-->
      <div
        v-if="currentRes.fileType != undefined && currentRes.fileType == '.pdf'"
        v-loading="loadFileContent"
        class="wx-videoBox"
      >
        <iframe
          v-if="currentRes.url != '' && currentRes.url != null"
          style="width: 100%; height: 100%"
          :src="'./pdfjs/web/viewer.html?file=' + currentRes.url"
        />
      </div>
      <!--h5-->
      <div
        v-if="currentRes.fileType != undefined && currentRes.fileType == '.zip'&&currentRes.resType =='html'"
        class="wx-videoBox"
      >
        <iframe
          style="width: 100%; height: 100%"
          :src="currentRes.url"
          scrolling="no"
        />
      </div>
      <!--三方-->
      <div
        v-if="currentRes.resType == 'hundun-video'"
        class="wx-videoBox"
      >
        <iframe
          :src="'./js/hundun/video/index.html?' + currentRes.url"
          scrolling="no"
        />
      </div>
      <div
        v-if="currentRes.resType == 'geektime-video'"
        class="wx-videoBox"
      >
        <iframe
          v-if="currentRes.url != '' && currentRes.url != null"
          :src="'./js/geektime/video/index.html?' + currentRes.url"
          scrolling="no"
        />
      </div>
      <div
        v-if="currentRes.resType == 'geektime-html'"
        class="wx-videoBox"
      >
        <iframe
          v-if="currentRes.url != '' && currentRes.url != null"
          :src="'./js/geektime/html/index.html?' + currentRes.url"
          scrolling="no"
        />
      </div>
      <div
        v-if="currentRes.resType == 'ximalaya'"
        class="wx-videoBox"
      >
        <iframe
          :src="'./js/ximalaya/index.html?' + currentRes.url"
          scrolling="no"
        />
      </div>
      <div
        v-if="currentRes.resType == 'pdfh5'"
        class="wx-videoBox"
      >
        <iframe
          :src="'./js/pdfh5/index.html?' + currentRes.url"
          scrolling="no"
        />
      </div>
      <div
        v-if="currentRes.resType == 'videoh5'"
        class="wx-videoBox"
      >
        <iframe
          :src="'./js/videoh5/index.html?' + currentRes.url"
          scrolling="no"
        />
      </div>
      <!--图片-->
      <div
        v-if="currentRes.fileType != undefined && currentRes.fileType == '.png'"
        class="wx-videoBox"
      >
        <img
          :src="currentRes.url"
          class="pre-img"
        >
      </div>
      <div :class="['wx-botttom-pg', IsshowInfo ? 'showInfo' : '']">
        <div
          v-if="IsshowInfo1"
          class="wx-resinfo"
        >
          <span class="wx-Res-title">{{ currentRes.name }}</span>
          <span
            v-if="courseinfo.freeModel == 0&&currentRes.resType!='html'"
            class="wx-right-span"
          >
            <span
              class="wx-duration"
            >{{ currentRes.duration | timeFromte }}
            </span>
            <span class="wx-line1" />
            <span
              class="wx-learnedtime"
            >已学习 {{ learnedtime | timeFromte }}</span>
          </span>
        </div>
        <div class="wx-opr">
          <div class="opr1">
            <span
              v-if="courseinfo.freeModel == 0"
              class="wx-prev-box"
              @click="prevOne()"
            >
              <span class="icon-box">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  xmlns:xlink="http://www.w3.org/1999/xlink"
                  width="13px"
                  height="21px"
                >
                  <path
                    fill-rule="evenodd"
                    fill="rgb(54, 58, 72)"
                    d="M12.171,3.433 L5.604,10.000 L12.171,16.568 C12.960,17.356 12.960,18.633 12.171,19.421 C11.383,20.209 10.106,20.209 9.317,19.421 L1.329,11.433 C0.933,11.037 0.737,10.518 0.738,10.000 C0.737,9.482 0.933,8.963 1.329,8.567 L9.317,0.579 C10.106,-0.209 11.383,-0.209 12.171,0.579 C12.960,1.367 12.960,2.645 12.171,3.433 Z"
                  />
                </svg>
              </span>
              上一个
            </span>
            <span
              class="wx-shq_btn"
              @click="ShowDir()"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                xmlns:xlink="http://www.w3.org/1999/xlink"
                width="24px"
                height="23px"
              >
                <path
                  fill-rule="evenodd"
                  fill="rgb(174, 175, 179)"
                  d="M23.000,23.000 L9.000,23.000 C8.448,23.000 8.000,22.552 8.000,22.000 L8.000,21.000 C8.000,20.448 8.448,20.000 9.000,20.000 L23.000,20.000 C23.552,20.000 24.000,20.448 24.000,21.000 L24.000,22.000 C24.000,22.552 23.552,23.000 23.000,23.000 ZM23.000,13.000 L9.000,13.000 C8.448,13.000 8.000,12.552 8.000,12.000 L8.000,11.000 C8.000,10.448 8.448,10.000 9.000,10.000 L23.000,10.000 C23.552,10.000 24.000,10.448 24.000,11.000 L24.000,12.000 C24.000,12.552 23.552,13.000 23.000,13.000 ZM23.000,3.000 L9.000,3.000 C8.448,3.000 8.000,2.552 8.000,2.000 L8.000,1.000 C8.000,0.448 8.448,-0.000 9.000,-0.000 L23.000,-0.000 C23.552,-0.000 24.000,0.448 24.000,1.000 L24.000,2.000 C24.000,2.552 23.552,3.000 23.000,3.000 ZM3.000,23.000 L1.000,23.000 C0.448,23.000 0.000,22.552 0.000,22.000 L0.000,21.000 C0.000,20.448 0.448,20.000 1.000,20.000 L3.000,20.000 C3.552,20.000 4.000,20.448 4.000,21.000 L4.000,22.000 C4.000,22.552 3.552,23.000 3.000,23.000 ZM3.000,13.000 L1.000,13.000 C0.448,13.000 0.000,12.552 0.000,12.000 L0.000,11.000 C0.000,10.448 0.448,10.000 1.000,10.000 L3.000,10.000 C3.552,10.000 4.000,10.448 4.000,11.000 L4.000,12.000 C4.000,12.552 3.552,13.000 3.000,13.000 ZM3.000,3.000 L1.000,3.000 C0.448,3.000 0.000,2.552 0.000,2.000 L0.000,1.000 C0.000,0.448 0.448,-0.000 1.000,-0.000 L3.000,-0.000 C3.552,-0.000 4.000,0.448 4.000,1.000 L4.000,2.000 C4.000,2.552 3.552,3.000 3.000,3.000 Z"
                />
              </svg>
              课程目录
            </span>
            <span
              v-if="courseinfo.freeModel == 0"
              class="wx-next-box"
              @click="nextOne()"
            >
              下一个
              <span class="icon-box">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  xmlns:xlink="http://www.w3.org/1999/xlink"
                  width="13px"
                  height="21px"
                >
                  <path
                    fill-rule="evenodd"
                    fill="rgb(54, 58, 72)"
                    d="M11.671,11.433 L3.683,19.421 C2.894,20.209 1.617,20.209 0.829,19.421 C0.040,18.633 0.040,17.356 0.829,16.568 L7.396,10.000 L0.829,3.433 C0.040,2.645 0.040,1.367 0.829,0.579 C1.617,-0.209 2.894,-0.209 3.683,0.579 L11.671,8.567 C12.067,8.963 12.263,9.482 12.262,10.000 C12.263,10.518 12.067,11.037 11.671,11.433 Z"
                  />
                </svg>
              </span>
            </span>
          </div>
          <button
            v-if="courseinfo.freeModel == 0&&currentRes.resType!='html'"
            :class="isComplete ? 'disable complete_btn' : 'complete_btn'"
            :disabled="isComplete"
            @click="finishRes()"
          >
            已完成学习
          </button>
          <span
            class="wx-close"
            @click="closeinfo()"
          > 关闭 </span>
        </div>
      </div>

      <!-- <div v-if="currentRes!=undefined" :class="['left-box',IsShowDir?'ShowDir':'']">
      <div class="header-box">
        <span class="return_btn">
          <span class="svg-span-arrow">
            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="6px"
              height="10px">
              <path fill-rule="evenodd" fill="rgb(54, 58, 72)"
                d="M2.427,5.000 L5.711,8.284 C6.105,8.678 6.105,9.317 5.711,9.711 C5.317,10.105 4.678,10.105 4.284,9.711 L0.289,5.716 C0.092,5.519 -0.006,5.259 -0.006,5.000 C-0.006,4.741 0.092,4.481 0.289,4.284 L4.284,0.289 C4.678,-0.105 5.317,-0.105 5.711,0.289 C6.105,0.683 6.105,1.322 5.711,1.716 L2.427,5.000 Z" />
            </svg>
          </span>
          <a @click="returnToKC"> 返回课程</a></span>
      </div>
    </div> -->

      <!-- 课程目录 -->
      <div
        v-if="
          courseinfo != null &&
            courseinfo.directoryResources != null &&
            courseinfo.directoryResources.length > 0
        "
        :class="['right-box', IsShowDir ? 'ShowDir' : '']"
      >
        <div class="wx-title">
          <span class="txt"> 课程目录</span>
          <span
            class="wx-ft"
            @click="IsShowDir = false"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              xmlns:xlink="http://www.w3.org/1999/xlink"
              width="21px"
              height="25px"
            >
              <path
                fill-rule="evenodd"
                fill="rgb(51, 51, 51)"
                d="M19.421,3.433 L11.433,11.421 C11.037,11.817 10.518,12.013 10.000,12.011 C9.482,12.013 8.963,11.817 8.567,11.421 L0.579,3.433 C-0.209,2.645 -0.209,1.367 0.579,0.579 C1.367,-0.209 2.645,-0.209 3.433,0.579 L10.000,7.146 L16.567,0.579 C17.355,-0.209 18.633,-0.209 19.421,0.579 C20.209,1.367 20.209,2.645 19.421,3.433 ZM0.579,14.579 C1.367,13.791 2.645,13.791 3.433,14.579 L10.000,21.146 L16.567,14.579 C17.355,13.791 18.633,13.791 19.421,14.579 C20.209,15.367 20.209,16.645 19.421,17.433 L11.433,25.421 C11.037,25.817 10.518,26.013 10.000,26.011 C9.482,26.013 8.963,25.817 8.567,25.421 L0.579,17.433 C-0.209,16.645 -0.209,15.367 0.579,14.579 Z"
              />
            </svg>
            收起
          </span>
        </div>
        <div class="wx-directoryDiv">
          <Directory
            :directorylist="courseinfo.directoryResources"
            :record-list="record"
            :course-id="courseId"
            :resource-id="resourceId"
            :train-id="trainId"
            :free-model="courseinfo.freeModel"
            :is-show-record="false"
            :is-expire="record != null ? record.isExpire : false"
          />
        </div>
      </div>
      <!-- 按钮 -->
      <img
        v-if="!IsshowInfo"
        src="/images/btn-close.png"
        class="btn-float"
        @click="openinfo()"
      >
    </div>
  </div>
</template>
<script>
import {
  getCourseInfo,
  getUserRecord,
  updateCourseRecord,
  getResourceInfo,
  getResourceUrl,
  UpdateResUserRecord,
  getCourseResourceHtmlUrl,
  getUserRecordInfo,
  getPresignedUrl,
} from "@/api/course";
import {  getInfo } from '@/api/user'
import { getFileDownloadInfo, getFileContent,getBaoCloudResourceUrl } from "@/api/file";
import Directory from "@/components/directory2"; //目录递归组件
import { setToken } from "@/utils/auth";
import videojs from 'video.js'
import 'video.js/dist/video-js.css';
import wx from "weixin-js-sdk";
export default {
  name: "Index",
  components: {
    Directory,
  },
  data() {
    return {
      loading: true,
      // userLogin:this.$store.getters.token!=null?true:false,
      currentRes: null,
      courseinfo: null,
      record: null,
      ResPrewId: {}, // 跳转预览的资源Id,
      recordInfo: {},
      // trueUrl:'',
      courseId: this.$route.query.courseId,
      resourceId: this.$route.query.id,
      learnedtime: 0,
      player: null,
      totalTime: 0, //学习时长
      timer: null, //定时器
      timer2: null, //定时器
      trainId: this.$route.query.trainId,
      trialUrl: this.$route.query.trialUrl,
      IsShowDir: false, // 是否显示目录
      ResourceList: [], //上一个，下一个，资源的集合
      isTiming: false, //是否需要计时
      isComplete: false, //当前资源是否已学完
      isVideo: false,
      IsshowInfo: false,
      IsshowInfo1: false,
      loadFileContent: false,

      user_sub:''
    };
  },
  watch: {
    $route: {
      handler: function (route) {
        this.currentRes = null;
        clearInterval(this.timer);
        clearInterval(this.timer2);
        window.removeEventListener('message',this.updateH5ResRecord);
        if (this.player) {
          this.player.dispose()
        }
        this.init();
      },
      deep: true,
      immediate: true,
    },
  },
  beforeDestroy() {
    clearInterval(this.timer);
    clearInterval(this.timer2);
    window.removeEventListener('message',this.updateH5ResRecord);
    window.removeEventListener("popstate", this.goBack);
    if (this.player) {
      this.player.dispose()
    }

  },
  mounted() {
    //this.getInfo()
    this.pushHistory();
    window.addEventListener("popstate", this.goBack);
  },
  methods: {
    init() {
      //this.$store.dispatch("user/settoken",this.$route.query.token);
      setToken(this.$route.query.token);
      //this.$store.dispatch("user/getInfo");
      getInfo().then(response => {
        this.user_sub=response.sub
      })

      //每隔2分钟，向服务器提交播放时间 ，计时器要摧毁
      this.totalTime = 0;
      this.courseId = this.$route.query.courseId;
      this.resourceId = this.$route.query.id;
      this.trainId = this.$route.query.trainId;
      this.trialUrl = this.$route.query.trialUrl;

      this.getkcInfo();
      window.addEventListener('message',this.updateH5ResRecord)
    },
    closeinfo() {
      this.IsshowInfo1 = false;
      setTimeout(() => {
        this.IsshowInfo = false;
      }, 300);
    },
    openinfo() {
      this.IsshowInfo = true;
      setTimeout(() => {
        this.IsshowInfo1 = true;
      }, 300);
    },
    startTimer() {
      //每隔1分钟，向服务器提交播放时间
      this.timer = setInterval(() => {
        this.postResUserRecord();
      }, 3*60*1000);
      this.timer2 = setInterval(() => {
        this.totalTime += 1;
        this.learnedtime=this.learnedtime+1;
        if(this.currentRes&&this.isTiming&&!this.isComplete
          &&this.currentRes.fileType == ".pdf"&&this.learnedtime>=this.currentRes.duration){
            this.isComplete = true;
            this.postResUserRecord();
          }
      }, 1000);
    },
    endTimer() {
      clearInterval(this.timer);
      clearInterval(this.timer2);
    },
    async getkcInfo() {
      let resUrl = "";
      await getCourseInfo(this.courseId).then((res) => {
        this.courseinfo = res;
        if (this.trainId != "" && this.trainId != null) {
          this.courseinfo.freeModel = 0;
        }
      });
      if (this.$store.getters.token) {
        await this.getRecord();
      }
      await getResourceInfo(this.resourceId).then((res) => {
        this.currentRes = res;
      });
      this.loading = false;
      //'html',
      var restype_arr = ['hundun-video', 'geektime-video', 'geektime-html', 'ximalaya', 'pdfh5', 'videoh5']
      if (
        (this.courseinfo.freeModel == 0 ||
          (this.trainId != "" && this.trainId != null))
        //&& !this.isComplete
        && restype_arr.indexOf(this.currentRes.resType ) == -1
      ) {
        //免费课程 培训课程
        this.isTiming = true;
      }else{
        this.isTiming = false;
      }

      if (this.isTiming) {
        this.startTimer();
      }
      this.isVideo = false;
      if (
        this.currentRes != null &&
        this.currentRes.fileType != undefined &&
        this.currentRes.fileType == ".mp4"
      ) {
        this.isVideo = true;
      }
      //如果是收费课程并且可试看,获取签名Url
      if (this.courseinfo.freeModel == 1) {
        resUrl = this.trialUrl;
      } else {
        await getResourceUrl({ id: this.resourceId, isWeChat: true }).then(
          (res) => {
            resUrl = res;
          }
        );
      }
      resUrl = resUrl.replace("http:", window.location.protocol);
      if (this.currentRes.jobId == "local") {
        var fileinfo = await getFileDownloadInfo(resUrl);
        if (this.currentRes.resType == "html") {
          resUrl =await getCourseResourceHtmlUrl(fileinfo.downloadUrl)
          //获取上次学习记录详情
          var origin= window.location.protocol+'//'+window.location.host
          resUrl=resUrl+"?origin="+origin
          +"&trainId="+(this.trainId==undefined?'':this.trainId)
          +"&courseId="+this.courseId
          +"&resourceId="+this.resourceId
          +"&userId="+this.$store.getters.sub

          if(this.recordInfo!=undefined&&this.recordInfo!=null)
          {
            var h5_record= await getUserRecordInfo({recordId:this.recordInfo.recordId,trainId:this.trainId})
            resUrl=resUrl
            +"&progress="+  this.recordInfo.lastLearnProgress //h5_record.lastLearnProgress
            +"&detail="+h5_record.detail
            //console.log(resUrl)
          }
        } else {
          // if (this.isVideo) {
          resUrl = fileinfo.downloadUrl;
          // } else {
          //   this.loadFileContent=true
          //   var fileContent = await getFileContent(fileinfo.downloadUrl);
          //   this.loadFileContent=false
          //   var fileBlob = [];
          //   fileBlob.push(fileContent.data);
          //   resUrl = URL.createObjectURL(new Blob(fileBlob));
          // }
        }
      }
      else if(this.currentRes.jobId == "AWS")
      {
          resUrl =await getBaoCloudResourceUrl(resUrl);
          resUrl=resUrl.replace('https://pbktgf0006.s3-qos.baocloud.cn/', 'https://qcfile.ciep-pimp.com/pbktgf0006/')
          if (this.currentRes.fileType == ".pdf")
          {
            resUrl = encodeURIComponent(resUrl);
          }
      }
      else {
        if (this.currentRes.fileType == ".pdf") {
          await getPresignedUrl(resUrl).then((res) => {
            resUrl = encodeURIComponent(res);
          });
        }
        if (this.isVideo) {
          //百度云视频不能再手机播放加密M3U8
          var arr = resUrl.split("/");
          resUrl = resUrl
            .replace("/" + arr[arr.length - 1], ".mp4")
            .replace("public", "");
          await getPresignedUrl(resUrl).then((res) => {
            resUrl = res;
          });
        }

        var arr=['hundun-video', 'geektime-video', 'geektime-html', 'ximalaya', 'pdfh5', 'videoh5']
        if ( arr.indexOf(this.currentRes.resType) > -1 ) {
           //获取上次学习记录详情
          var sub=this.user_sub// localStorage.getItem("USER.SUB");
          //alert(sub)
          resUrl ='url='+ encodeURIComponent(resUrl);
          var origin= window.location.protocol+'//'+window.location.host
          resUrl=resUrl+"&origin="+origin
          +"&trainId="+(this.trainId==undefined?'':this.trainId)
          +"&courseId="+this.courseId
          +"&resourceId="+this.resourceId
          +"&userId="+ sub
          +"&duration="+this.currentRes.duration

          if(this.recordInfo!=undefined&&this.recordInfo!=null)
          {
            var h5_record= await getUserRecordInfo({recordId:this.recordInfo.recordId,trainId:this.trainId})
            resUrl=resUrl
            +"&progress="+this.recordInfo.lastLearnProgress // h5_record.lastLearnProgress
            +"&detail="+encodeURIComponent(h5_record.detail)
          }
          //alert(resUrl)
        }

      }
      this.currentRes.url = resUrl;
      // 视频播放
      if (this.isVideo) {
        setTimeout(() => {
          // 视频播放
          let start = 0;
          if (this.recordInfo != null && this.recordInfo.isComplete == false) {
           // start = this.recordInfo.lastPlayProgress;
           if(this.learnedtime>120) start=this.learnedtime-120
          }
          //this.videoOptions.sources.src = this.currentRes.url
          let that = this;
          let playRateList = [0.5, 1, 1.5, 2, 3]
          this.player = videojs('myPlayer', {
            autoplay: true,
            preload: 'auto',
            loop: false,
            //bigPlayButton : false,
            //muted: false,
            //fluid: true,
            playsinline: true,
            controls: true,
            controlBar: {
                volumePanel: {
                    inline: false //默认是true,横着的bai
                },
                timeDivider: true, // 时间分割线
                durationDisplay: false, // 总时间
                 progressControl: true, // 进度条
                // customControlSpacer: true, // 未知
                fullscreenToggle: true, // 全屏
                // liveDisplay: true,
                pictureInPictureToggle: false,
            },
            liveui: true,
            playbackRates: this.isComplete?playRateList:[], //严格大于0的数字数组，其中1表示常规速度（100％），0.5表示半速（50％），2表示倍速（200％），依此类推。如果指定，Video.js将显示一个控件（类别vjs-playback-rate）（允许用户从一系列选项中选择播放速度）。选择从下到上以指定顺序显示。
            userActions: { doubleClick: false },
            sources: [{
                  src: this.currentRes.url,
                  type: "video/mp4"
            }]
            }, function onPlayerReady() {
              // console.log('onPlayerReady', this);
              this.currentTime(start);
              if (that.isTiming) {
                this.on('ended', function() {
                  that.endTimer();
                  //console.log('onComplete:'+that.totalTime);
                  //that.totalTime = that.currentRes.duration // 资源时长
                  that.postResUserRecord();
                });
                this.on('play', function(event) {
                  // 播放
                   console.log("视频播放")
                  that.endTimer();
                  that.startTimer();
                })
                this.on("pause", function(){
                    console.log("视频暂停播放")
                    that.endTimer();
                });
              }
          })
          // if(!this.isComplete){ // 禁止拖拽
          //   document.getElementsByClassName("vjs-progress-control")[0].style.pointerEvents = 'none'
          // }

         // this.player = cyberplayer("myPlayer").setup({
          //   width: "100%",
          //   height: "40%",
          //   backcolor: "#FFFFFF",
          //   stretching: "uniform",
          //   file: this.currentRes.url,
          //   starttime: start,
          //   ak: "5a8cf05266684131a0bed321d0032848",
          //   playRate: false,
          //   autoStart: true,
          //   repeat: false,
          //   volume: 100,
          //   skin: {
          //     name: "bce",
          //     inactive: "#FFF", // 未激活时的颜色
          //     active: "#f34813", // 悬浮或激活的颜色
          //   },
          //   controls: "over",
          // });
        }, 10);
      }

      // 点击上一个，下一个所需资源列表
      if (
        this.courseinfo.directoryResources != null &&
        this.courseinfo.freeModel == 0
      ) {
        this.ResourceList = [];
        this.getResList(this.courseinfo.directoryResources);

      }

    },
    async getRecord() {
      let data = { courseId: this.courseId, trainId: this.trainId };
      await getUserRecord(data).then((res) => {
        this.record = res;
        if (this.record.isExpire && !this.trialUrl) {
          this.returnToKC();
        }
        if (this.record.id != null && this.record.isExpire == false) {
          this.courseinfo.freeModel = 0;
        }
        if (this.record.resUserRecords != null)
          this.recordInfo = this.record.resUserRecords.find(
            (x) => x.courseResourceId == this.$route.query.id
          ); // 用户观看该资源记录
        this.learnedtime =
          this.recordInfo != undefined ? this.recordInfo.resLearnDuration : 0; //
        this.isComplete =
          this.recordInfo != undefined ? this.recordInfo.isComplete : false;
      });
    },
    // prewCourse(id){
    //   this.$router.push({name: 'CourseInfo', query:{id: id}})
    // },
    postResUserRecord(_time,_progress,_detail,_resourceId,_courseId,_trainId) {
      // 更新资源学习记录
      if(_time==undefined)
      {
        _time= this.totalTime
        if(_time>55&&_time<=59){_time=60}
        if(_time>115&&_time<=119){_time=120}
        if(_time>175&&_time<=179){_time=180}
        if(_time>295&&_time<=299){_time=300}
      }
      if(_resourceId==undefined)
      {
        _resourceId= this.resourceId
      }
      if(_courseId==undefined)
      {
        _courseId= this.courseId
      }
      if(_trainId==undefined||_trainId=='')
      {
        _trainId= this.trainId
      }
      var data = {
        trainId: _trainId,
        courseId: _courseId,
        courseResourceId: _resourceId,
        isVideo: this.isVideo,
        resTotalDuration: this.currentRes.duration, //资源时长
        resLearnDuration: _time, //资源学习时长
        lastPlayProgress: 0, //视频资源播放进度
        lastLearnProgress:_progress, //H5资源学习进度
        learnDetail:_detail //H5资源学习详情记录
      };
      if (this.isVideo) {
        //视频
        //console.log(this.player.currentTime())
        data.lastPlayProgress = Math.floor(this.player.currentTime());
      }
      UpdateResUserRecord(data);
      this.totalTime=0;
    },
    // returnToKC() {
    //   this.$router.push({ name: 'CourseInfo', query: { id: this.courseId, trainId: this.trainId } })
    // },
    nextOne() {
      var index = this.ResourceList.findIndex(
        (x) => x.id == this.currentRes.id
      );
      //debugger
      //console.log(index)
      if (index < this.ResourceList.length - 1) {
        index++;
        let data = {
          trainId: this.trainId, //培训ID
          courseId: this.courseId,
          lastLearnResourceId: this.ResourceList[index].id,
        };
        updateCourseRecord(data);
        this.$router.push({
          name: "mobile",
          query: {
            id: this.ResourceList[index].id,
            courseId: this.courseId,
            trainId: this.trainId,
            token: this.$route.query.token,
          },
        });
      } else {
        this.$message.info("已经是最后一个资源");
      }
    },
    prevOne() {
      var index = this.ResourceList.findIndex(
        (x) => x.id == this.currentRes.id
      );

      if (index > 0) {
        index--;
        let data = {
          trainId: this.trainId, //培训ID
          courseId: this.courseId,
          lastLearnResourceId: this.ResourceList[index].id,
        };
        updateCourseRecord(data);
        this.$router.push({
          name: "mobile",
          query: {
            id: this.ResourceList[index].id,
            courseId: this.courseId,
            trainId: this.trainId,
            token: this.$route.query.token,
          },
        });
      } else {
        this.$message.info("已经是第一个资源");
      }
    },
    ShowDir() {
      // 关闭右侧目录
      this.IsShowDir = !this.IsShowDir;
    },
    finishRes() {
      // 已完成学习
      if (this.learnedtime + this.totalTime >= this.currentRes.duration) {
        // 已学完
        this.postResUserRecord();
        this.endTimer();
        this.isComplete = true;
       // this.learnedtime = this.currentRes.duration;
      } //未学完
      else {
        this.$message.info("学习未完成，请继续学习");
      }
    },
    getResList(res) {
      //获取资源集合
      for (var i = 0; i < res.length; i++) {
        if (res[i].resources != null && res[i].resources.length > 0) {
          this.ResourceList = this.ResourceList.concat(res[i].resources);
        } else {
          this.getResList(res[i].directories);
        }
      }
    },
    pushHistory() {
      var state = {
        title: "",
        url: "#",
      };
      window.history.pushState(state, state.title, state.url);
    },
    goBack() {
      wx.miniProgram.navigateBack();
      // wx.miniProgram.navigateTo({
      //   url: '/pagesA/pages/course/info?id=' + this.courseId + '&trainId=' + this.trainId
      // })
    },
    updateH5ResRecord(msg)
    {
      if(msg.data.eName=='HtmlResLearnUpdate')
        {
          var _data=msg.data.data
          this.postResUserRecord(
            _data.learnTime,
            _data.progress,
            _data.detail,
            _data.resourceId,
            _data.courseId,
            _data.trainId,
          );
        }
    }
  },
};
</script>
<style>
html {
  min-width: auto !important;
}
.layout-box {
    min-width: auto;
}
.head_blank {
  height: 0px !important;
}
.wx-Res-page {
  height: 100%;
  background: #3c3f4a;
  overflow: hidden;
}
.wx-videoBox {
  width: 100%;
  height: 100%;
  position: relative;
  z-index: 0;
}
.wx-resinfo .wx-Res-title {
  font-size: 1rem;
  line-height: 2rem;
  margin: 1rem;
  margin-left: 1rem;
  color: #fff;
  display: block;
}
.wx-right-span {
  font-size: 1rem;
  margin-left: 1rem;
  border-radius: 4px;
  display: inline-block;
  background: #4e515b;
  color: #fff;
  padding: 5px;
}
.wx-right-span span {
  vertical-align: middle;
}
.wx-botttom-pg .complete_btn {
  border-radius: 4px;
  background-image: -moz-linear-gradient(
    0deg,
    rgb(237, 114, 14) 0%,
    rgb(243, 72, 19) 100%
  );
  background-image: -webkit-linear-gradient(
    0deg,
    rgb(237, 114, 14) 0%,
    rgb(243, 72, 19) 100%
  );
  background-image: -ms-linear-gradient(
    0deg,
    rgb(237, 114, 14) 0%,
    rgb(243, 72, 19) 100%
  );
  background-image: linear-gradient(
    0deg,
    rgb(237, 114, 14) 0%,
    rgb(243, 72, 19) 100%
  );
  width: 95%;
  margin: auto;
  border: none;
  color: #fff;
  height: 2.5rem;
}
.wx-botttom-pg .complete_btn.disable {
  opacity: 0.6;
}
.wx-line1 {
  height: 0.8rem;
  width: 1px;
  background: #fff;
  margin: 0 10px;
  display: inline-block;
}
.wx-botttom-pg {
  position: absolute;
  top: 40%;
  width: 100%;
  height: 0;
  overflow: hidden;
  transition: 0.5s;
  opacity: 0;
  background: #3c3f4a;
}
.wx-close {
  color: #fff;
  opacity: 0.5;
  display: inline-block;
  margin-bottom: 2rem;
  height: 2rem;
  line-height: 2rem;
  margin-top: 1rem;
}
.wx-opr {
  text-align: center;
  font-size: 1rem;
  position: absolute;
  width: 100%;
  bottom: 0;
}
.wx-prev-box {
  height: 2.4rem;
  vertical-align: middle;
  display: inline-block;
  width: 26.4%;
  background: #363a48;
  line-height: 2.4rem;
  border-top: 1px solid #313441;
  border-bottom: 1px solid #313441;
  border-right: 1px solid #313441;
  border-radius: 0 1.3rem 1.3rem 0;
}
.wx-shq_btn {
  height: 2.4rem;
  vertical-align: middle;
  display: inline-block;
  width: 26.4%;
  margin: 0 9.5%;
  background: #363a48;
  line-height: 2.4rem;
  border-radius: 1.3rem;
  border: 1px solid #313441;
}
.wx-next-box {
  float: right;
  height: 2.4rem;
  vertical-align: middle;
  display: inline-block;
  width: 26.4%;
  background: #363a48;
  line-height: 2.4rem;
  border-top: 1px solid #313441;
  border-bottom: 1px solid #313441;
  border-left: 1px solid #313441;
  border-radius: 1.3rem 0 0 1.3rem;
}
.wx-opr .opr1 {
  margin-bottom: 2rem;
  color: #aeafb3;
  font-size: 0.8rem;
}
.wx-opr .opr1 svg {
  zoom: 0.5;
}
.wx-opr .opr1 .icon-box {
  width: 1.2rem;
  height: 1.2rem;
  background: #9b9da4;
  display: inline-block;
  line-height: 1.2rem;
  border-radius: 0.6rem;
  text-align: center;
  margin: 0 0.3rem;
}
.ShowDir {
  border-top: 1px solid #3c3f4a;
  height: 60%;
  position: absolute;
  top: 40%;
  overflow-y: auto;
  background: #fff;
  width: 100%;
}
.ShowDir .wx-title {
  font-size: 1rem;
  height: 3rem;
  line-height: 3rem;
  padding-right: 1rem;
}
.ShowDir .txt {
  border-left: 0.3rem solid #ed570e;
  padding-left: 1rem;
}
.wx-ft {
  float: right;
  font-size: 1rem;
}
.wx-ft svg {
  zoom: 0.5;
}
.showInfo {
  height: 60%;
  opacity: 1;
}
.btn-float {
  position: absolute;
  right: 1rem;
  bottom: 1rem;
  zoom: 0.6;
  z-index: 9;
}

/* video */
.myPlayer-dimensions {
    width: 100% !important;
    height: 300px;
}
.video-js {
    width: 100% !important;
    height: 300px;
}
.vjs-paused .vjs-big-play-button,
.vjs-paused.vjs-has-started .vjs-big-play-button {
    display: block;
}
.video-js .vjs-big-play-button{
    font-size: 2.5em;
    line-height: 2.3em;
    height: 2.5em;
    width: 2.5em;
    -webkit-border-radius: 2.5em;
    -moz-border-radius: 2.5em;
    border-radius: 2.5em;
    background-color: #73859f;
    background-color: rgba(115,133,159,.5);
    border-width: 0.15em;
    margin-top: -1.25em;
    margin-left: -1.75em;
}
/* 中间的播放箭头 */
.vjs-big-play-button .vjs-icon-placeholder {
    font-size: 1.63em;
}
/* 加载圆圈 */
.vjs-loading-spinner {
    font-size: 2.5em;
    width: 2em;
    height: 2em;
    border-radius: 1em;
    margin-top: -1em;
    margin-left: -1.5em;
}
.wx-videoBox iframe{
  width: 100%;
   height: 100%
}
</style>
