<template>
  <div class="biaozhun">
    <div class="maps">
      <img src="../../assets/image/zan/map.png">
      <span>人才中心</span>
      <img src="../../assets/image/zan/left.png">
      <span>企业岗位</span>
    </div>
    <div class="qiye-box">
      <div
        v-for="item in list"
        :key="item.id"
        class="qiye-item"
        @click="go(item.id)"
      >
        <div class="font1">
          <p>{{ item.title }}</p>
          <p>{{ item.salary }}</p>
        </div>
        <div class="font2">
          <p>{{ item.experience }}</p>
          <p>{{ item.qualifications }}</p>
        </div>
        <div class="font3">
          <p>{{ item.company }}</p>
          <p>{{ item.location }}</p>
        </div>
      </div>
    </div>
    <el-pagination
      v-if="parmas.totalCount > parmas.maxResultCount"
      class="my_pagination"
      :current-page="parmas.page"
      :page-sizes="[10, 20, 60]"
      :page-size="parmas.MaxResultCount"
      layout="total, sizes, prev, pager, next, jumper"
      :total="parmas.total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>
<script>
import {getPositions} from '@/api/talent';
export default {
    data() {
        return {
            parmas: {
                    SkipCount: 0,
                    MaxResultCount: 100,
                    Sorting: 'sort',
                    page:1,
                    total:0,
                    state:1
                },
            list:[]
        }
    },
   mounted(){
            this.getList();
        },
    methods: {
        handleSizeChange(val) {
                this.parmas.MaxResultCount = val;
                this.getList();
        },
         handleCurrentChange(val) {
             this.parmas.page = val;
             this.getList();
         },
        //  handleClick(url) {
        //      this.iframeUrl = url;
        //      this.Dialog = true;
        //  },
         getList(){
             this.parmas.SkipCount=this.parmas.MaxResultCount*(this.parmas.page-1)
             getPositions(this.parmas).then(res => {
                 this.list = res.items;
                 this.parmas.total = res.totalCount;
             });
         },
        go(_id) {

                const url = this.$router.resolve({
                    name: "qiyeDetail",
                    query: {
                    id: _id,
                    mapname:'0' },
                }).href;

                window.open(url, "_blank");


        }
    }
}
</script>
<style scoped>
.qiye-box{
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    margin-top: 20px;
}
.qiye-item{
    box-shadow: 0 5px 10px 0 rgba(0, 0, 0, 0.10);
    border-radius: 20px;
    width: 370px;
    height: 150px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    padding: 20px;
    background: #fff;
    margin-right: 55px;
    margin-bottom: 50px;
    cursor:  pointer;
}
.font1{
    display: flex;
    justify-content: space-between;
}
.font1>p{
    margin: 0 !important;
}
.font1>p:nth-child(1){font-size:16px;color: #5d5d5d;}
.font1>p:nth-child(2){font-size:18px;color: #096dd9;}
.font2{display: flex;}
.font2>p{
    background: #ebeef2;
    color: #a9aaab;
    padding:2px 6px;
    box-sizing: border-box;
    border-radius: 3px;
    margin-right: 10px;
    font-size: 14px;
}
.font3{
    display: flex;
    justify-content: space-between;
    border-top:1px solid #e6e6e6;
}
.font3>p{
    color: #5d5d5d;
}
</style>
