import axios from '@/axios'
export function voiceToText(data,headers={}) {
    return axios.posts2('/api/itm/public/nls/recognize2', data,headers)
}
export function deepseekApi(data,callback){
    const controller = new AbortController();
    const promise = axios.fetchs('/api/cms/ai/chat', data, callback, controller.signal);

    // 返回一个对象，包含 promise 和取消方法
    return {
        promise,
        cancel: () => controller.abort()
    };
    //return axios.fetchs('/api/itm/public/chat/dify', data,callback)
}
