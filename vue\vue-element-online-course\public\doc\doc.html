<html>

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
  <title>文档预览</title>
</head>
<body>
  <div id="reader" style="margin: auto;"></div>
  <script src="https://static.bcedocument.com/reader/v2/doc_reader_v2.js"></script>
  <script>
    (function () {

      function getQuery() {
        var queryString = location.search.substr(1);
        var queryArr = queryString.split('&');

        var query = {};
        for (var i = 0; i < queryArr.length; i++) {
          var key = queryArr[i].split('=')[0];
          var value = queryArr[i].split('=')[1];
          query[key] = value;
        }
        return query;
      }

      function init() {
        var query = getQuery();

        var option = {
          docId: query.docId,
          token: query.token,
          host: query.host,
          serverHost: 'https://doc.bj.baidubce.com',
          width: 964, // 文档容器宽度
          pn: 1,  // 定位到第几页，可选
          fontSize: 'big',
          toolbarConf: {
            page: true, // 上下翻页箭头图标
            pagenum: true, // 几分之几页
            full: false, // 是否显示全屏图标,点击后全屏
            copy: false, // 是否可以复制文档内容
            position: 'center', // 设置 toolbar中翻页和放大图标的位置(值有left/center)
          }, // 文档顶部工具条配置对象,必选
          enviroment: query.enviroment
        };
        new Document('reader', option);
      }
      init();
    })();
  </script>
</body>
</html>
