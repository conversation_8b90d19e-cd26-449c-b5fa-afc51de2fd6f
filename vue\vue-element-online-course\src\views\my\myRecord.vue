<template>
  <div class="profile_info_con">
    <SideMenu />
    <div class="profile_info_box">
      <div class="tabs">
        <a
          :class="{active:statue==0}"
          @click="changeState(0)"
        >我的培训记录</a>
        <a
          :class="{active:statue==1}"
          @click="changeState(1)"
        >直播培训记录</a>
        <a
          :class="{active:statue==2}"
          @click="changeState(2)"
        >自学课程记录</a>
      </div>
      <div
        v-if="statue==0"
        class="tabbox"
      >
        <!-- <div class="tishi">
          培训结束后才可查看完整的学习记录
        </div> -->
        <div class="table_default table_train">
          <div class="th_row">
            <div class="td1">

              培训名称
            </div>
            <div class="td2">
              培训日期
            </div>
            <div class="td3">
              学习进度
            </div>
            <div class="td4">
              已学时长
            </div>
            <div class="td5">
              学习状态
            </div>
            <div class="td6">
              操作
            </div>
          </div>
          <div
            v-for="item in trainRecord"
            class="row"
          >
            <div class="td1">
              <span
                class="name"
                :title="item.trainName"
              >{{ item.trainName }}</span>
            </div>
            <div class="td2">
              {{ item.startDate | DateFromte("YYYY-MM-DD") }} 至 {{ item.endDate | DateFromte("YYYY-MM-DD") }}
            </div>
            <div class="td3">
              {{ item.learnProgress.toFixed(2) }} %
            </div>
            <div class="td4">
              {{ item.learnDuration | timeFromte }}
            </div>
            <div class="td5">
              {{ item.learnProgress==100?'已学完':'未学完' }}
            </div>
            <div class="td6">
              <a @click="getRecordInfo(item.trainId)">查看</a>
            </div>
          </div>
        </div>
        <el-pagination
          v-if="train_query.total>train_query.MaxResultCount"
          class="my_pagination"
          :current-page="train_query.page"
          :page-sizes="[10, 20, 50]"
          :page-size="train_query.MaxResultCount"
          layout="total, sizes, prev, pager, next, jumper"
          :total="train_query.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
      <div
        v-if="statue==1"
        class="tabbox"
      >
        <div class="table_live">
          <div class="th_row">
            <div class="td1">
              直播封面/主题/内容介绍
            </div>
            <div class="td2">
              直播时长
            </div>
            <div class="td3">
              直播状态
            </div>
            <div class="td4">
              直播观看时长
            </div>
            <div class="td5">
              回放观看时长
            </div>
          </div>
          <div
            v-for="item in list"
            class="row"
          >
            <div class="td1">
              <img
                class="live_image"
                :src="item.liveStream.coverImage"
              >
              <div class="live_info">
                <h2 class="live_title">
                  {{ item.liveStream.title }}
                </h2>
                <span class="live_description">{{ item.liveStream.description }}</span>
              </div>
            </div>
            <div class="td2">
              {{ item.liveStream.timeLong }} 分钟
            </div>
            <div class="td3">
              <el-tag
                v-if="item.liveStream.liveStreamStatue === 0"
                type="primary"
                size="mini"
              >
                未开始
              </el-tag>
              <el-tag
                v-else-if="item.liveStream.liveStreamStatue === 1"
                type="info"
                class="tag-living"
                size="mini"
              >
                <img
                  src="data:image/gif;base64,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"
                  alt=""
                >
                直播中
              </el-tag>
              <el-tag
                v-else-if="item.liveStream.liveStreamStatue === 2"
                type="info"
                size="mini"
              >
                已结束
              </el-tag>
              <el-tag
                v-else
                type="danger"
                size="mini"
              >
                未知
              </el-tag>
            </div>
            <div class="td4">
              {{ item.liveViewDuration | timeFromte }}
            </div>
            <div class="td5">
              {{ item.backPlayDuration | timeFromte }}
            </div>
          </div>
        </div>
        <el-pagination
          v-if="listQuery.totalCount>listQuery.MaxResultCount"
          class="my_pagination"
          :current-page="listQuery.page"
          :page-sizes="[10, 20, 50]"
          :page-size="listQuery.MaxResultCount"
          layout="total, sizes, prev, pager, next, jumper"
          :total="listQuery.totalCount"
          @size-change="handleSizeChange1"
          @current-change="handleCurrentChange1"
        />
      </div>
      <div
        v-if="statue==2"
        class="tabbox"
      >
        <div class="table_default table_course">
          <div class="th_row">
            <div class="td1">
              课程名称
            </div>
            <div class="td2">
              学习进度
            </div>
            <div class="td3">
              已学时长
            </div>
            <div class="td4">
              最后学习时间
            </div>
          </div>
          <div
            v-for="item in courseRecord"
            class="row"
          >
            <div class="td1">
              <div class="name">
                {{ item.courseName }}
              </div>
            </div>
            <div class="td2">
              {{ item.courseLearnProgress.toFixed(2) }} %
            </div>
            <div class="td3">
              {{ item.courseLearnDuration | timeFromte }}
            </div>
            <div class="td4">
              {{ item.lastLearnTime | DateFromte }}
            </div>
          </div>
        </div>
        <el-pagination
          v-if="course_query.total>course_query.MaxResultCount"
          class="my_pagination"
          :current-page="course_query.page"
          :page-sizes="[10, 20, 50]"
          :page-size="course_query.MaxResultCount"
          layout="total, sizes, prev, pager, next, jumper"
          :total="course_query.total"
          @size-change="handleSizeChange2"
          @current-change="handleCurrentChange2"
        />
      </div>
      <div
        v-if="showbox"
        class="shadow_RecordInfo"
      >
        <h2>
          培训记录详情 <a @click="showbox=false">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              xmlns:xlink="http://www.w3.org/1999/xlink"
              width="16px"
              height="16px"
            >
              <defs>
                <filter id="Filter_0">
                  <feFlood
                    flood-color="rgb(155, 160, 171)"
                    flood-opacity="1"
                    result="floodOut"
                  />
                  <feComposite
                    operator="atop"
                    in="floodOut"
                    in2="SourceGraphic"
                    result="compOut"
                  />
                  <feBlend
                    mode="normal"
                    in="compOut"
                    in2="SourceGraphic"
                  />
                </filter>
              </defs>
              <g filter="url(#Filter_0)">
                <path
                  fill-rule="evenodd"
                  fill="rgb(255, 255, 255)"
                  d="M10.256,7.994 L15.530,13.268 C16.154,13.892 16.154,14.905 15.530,15.529 C14.905,16.153 13.894,16.153 13.270,15.529 L7.995,10.255 L2.721,15.529 C2.097,16.153 1.085,16.153 0.460,15.529 C-0.164,14.905 -0.164,13.892 0.460,13.268 L5.735,7.994 L0.460,2.720 C-0.164,2.096 -0.164,1.083 0.460,0.459 C1.085,-0.165 2.097,-0.165 2.721,0.459 L7.995,5.734 L13.270,0.459 C13.894,-0.165 14.905,-0.165 15.530,0.459 C16.154,1.083 16.154,2.096 15.530,2.720 L10.256,7.994 Z"
                />
              </g>
            </svg>
          </a>
        </h2>
        <div
          v-if="RecordInfo!=null"
          class="table_default table_course"
        >
          <div class="th_row">
            <div class="td1">
              课程名称
            </div>
            <div class="td2">
              学习进度
            </div>
            <div class="td3">
              已学时长
            </div>
            <div class="td4">
              最后学习时间
            </div>
          </div>
          <div class="list">
            <div
              v-for="item in RecordInfo.trainUserCourseRecords"
              class="row"
            >
              <div class="td1">
                <span class="name">{{ item.courseName }}</span>
              </div>
              <div class="td2">
                {{ item.courseLearnProgress.toFixed(2) }} %
              </div>
              <div class="td3">
                {{ item.courseLearnDuration | timeFromte }}
              </div>
              <div class="td4">
                {{ item.lastLearnTime | DateFromte }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
  import {
    getMytrainUserRecord,
    getMyOptionalCourseUserRecord,
    getTrainUserRecord
  } from '@/api/course';
  import {
    myLiveList
  } from '@/api/user';
  import SideMenu from "@/layout/SideMenu.vue";
  export default {
    name: 'MyCourse',
    components: {
      SideMenu
    },
    data() {
      return {
        statue: 0,
        courseRecord: [],
        trainRecord: [],
        list: [],
        train_query: {
          SkipCount: 0,
          MaxResultCount: 10,
          page: 1,
          total: 0
        },
        course_query: {
          SkipCount: 0,
          MaxResultCount: 10,
          page: 1,
          total: 0
        },
        listQuery: {
          Filter: '',
          LiveStreamStatue: null,
          SkipCount: 0,
          MaxResultCount: 10,
          page: 1,
          totalCount: 0
        },
        RecordInfo: null,
        showbox: false
      }
    },
    mounted() {
      if (this.$store.getters.token) {
        this.getmytrainRecord()
        this.getMyLiveList()
        this.getmycourseRecord()
      } else {
        this.$store.dispatch("user/toggleloginbox", true);
      }

    },
    methods: {
      // prewCourse(id){
      //   this.$router.push({name: 'CourseInfo', query:{id: id}})
      // },
      getmytrainRecord() {
        let data = {
          SkipCount: (this.train_query.page - 1) * this.train_query.MaxResultCount,
          MaxResultCount: this.train_query.MaxResultCount,
          page: this.train_query.page
        }
        getMytrainUserRecord(data).then(res => {
          this.trainRecord = res.items
          this.train_query.total = res.totalCount
        })
      },
      getmycourseRecord() {
        let data = {
          SkipCount: (this.course_query.page - 1) * this.course_query.MaxResultCount,
          MaxResultCount: this.course_query.MaxResultCount,
          page: this.course_query.page
        }
        getMyOptionalCourseUserRecord(data).then(res => {
          this.courseRecord = res.items
          this.course_query.total = res.totalCount
        })
      },
      getMyLiveList() {
        this.listQuery.SkipCount = (this.listQuery.page - 1) * this.listQuery.MaxResultCount
        myLiveList(this.listQuery).then(res => {
          this.list = res.items
          this.listQuery.totalCount = res.totalCount
        })
      },
      /*培训详情查看*/
      getRecordInfo(id) {
        this.showbox = true
        getTrainUserRecord(id).then(res => {
          this.RecordInfo = res
        })
      },
      changeState(statue) {
        this.statue = statue
        // if(statue == 0) {
        //   this.getmytrainRecord()
        // }else if(statue == 1) {
        //   this.getMyLiveList()
        // }else {
        //   this.getmycourseRecord()
        // }
      },
      handleSizeChange(val) {
        this.train_query.MaxResultCount = val
        this.getmytrainRecord()
      },
      handleCurrentChange(val) {
        this.train_query.page = val
        this.getmytrainRecord()
      },
      handleSizeChange1(val) {
        this.listQuery.MaxResultCount = val
        this.getMyLiveList()
      },
      handleCurrentChange1(val) {
        this.listQuery.page = val
        this.getMyLiveList()
      },
      handleSizeChange2(val) {
        this.course_query.MaxResultCount = val
        this.getmycourseRecord()
      },
      handleCurrentChange2(val) {
        this.course_query.page = val
        this.getmycourseRecord()
      },
    }
  }

</script>
<style scoped>
  .table_live {
    margin: 0 20px;
  }

  .table_live .th_row {
    background: #ebeef2;
    height: 50px;
    line-height: 50px;
    font-size: 14px;
  }

  .table_live .th_row div {
    display: inline-block;
  }

  .table_live .row {
    height: 100px;
    line-height: 100px;
    border-bottom: 1px solid #f1f1f1;
  }

  .table_live .row:hover {
    background-color: #f5f7fa;
  }

  .table_live .td1 {
    width: 38%;
    text-indent: 20px;
    display: inline-block;
  }

  .table_live .td2 {
    width: 12%;
    display: inline-block;
  }

  .table_live .td3 {
    width: 13%;
    display: inline-block;
  }

  .table_live .td4 {
    width: 15%;
    display: inline-block;
  }

  .table_live .td5 {
    width: 15%;
    display: inline-block;
  }

  .table_live .td6 {
    width: 7%;
    display: inline-block;
  }

  .table_live .td6 .live_view {
    background: url(/images/viewVideo.jpg);
    width: 28px;
    height: 28px;
    cursor: pointer;
    display: inline-block;
    margin: 0 10px;
    vertical-align: middle;
  }

  .live_box {
    vertical-align: middle;
    height: 100px;
    line-height: 100px;
    text-align: left;
    width: calc(100% - 30px);
    text-indent: 0px;
  }

  .live_image {
    width: 120px;
    max-height: 76px;
    border-radius: 5px;
    vertical-align: middle;
    border: 1px solid #eee;
  }

  .tag-living.el-tag.el-tag--info {
    background-color: #f35;
    color: #eee;
  }

  .tag-living img {
    width: 12px;
  }

  .live_info {
    vertical-align: middle;
    height: 100px;
    line-height: 100px;
    text-align: center;
    display: inline-block;
    width: 175px;
    float: none;
    margin-left: 10px;
  }

  .live_title {
    line-height: 20px;
    height: 20px;
    text-align: left;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    vertical-align: top;
    font-weight: normal;
    font-size: 100%;
    margin: 10px 0 0 0;
    padding: 0;
    text-indent: 0px;
  }

  .live_description {
    font-size: 14px;
    line-height: 20px;
    color: #999;
    margin-top: 10px;
    display: inline-block;
    text-align: left;
    height: 40px;
    overflow: hidden;
    width: 100%;
    text-indent: 0px;
  }

</style>
