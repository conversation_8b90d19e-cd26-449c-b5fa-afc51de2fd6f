<template>
  <!-- <div>
    <img
      style="width: 1920px;height: 1080px;display: block;margin: 0 auto;"
      src="../../assets/image/static/qiye.png"
    >
  </div> -->
  <div v-html="detail.content" />
</template>
<script>
import { getPositionDetail } from "@/api/talent";
export default {
  data() {
    return {
      detail: {},
    };
  },
  mounted() {},
  created() {
    const id = this.$route.query.id;
    getPositionDetail(id).then((res) => {
      this.detail = res;
    });
  },
  methods: {},
};
</script>
