<template>
  <div class="profile_info_con">

    <SideMenu></SideMenu>

    <div class="profile_info_box">
      <div class="tabs">
        <a class="active" style="cursor:auto">我的学分</a>
      </div>
      <div class="my-total-box">
        <div class="user-info-box">
          <img class="userimg" src="/images/user.png"/>
          <div class="fr">
            <div class="username" >{{userinfo!=null?userinfo.name:''}}</div>
            <div class="studentId">工号：<span>{{userinfo.studentIDNumber!=null?userinfo.studentIDNumber:''}} </span></div>
            <div class="OUName">部门：<span>{{userinfo.extraProperties!=undefined&&userinfo.extraProperties.OUName!=null? userinfo.extraProperties.OUName:''}}</span></div>
          </div>
        </div>
        <div class="total-div-item">
          <div class="title">累计学习数据</div>
          <div class="pr">
            学习时长超过 <span>{{myData.rank!=undefined?Math.round(myData.rank*10)/10:0}}% </span> 用户
          </div>
          <div class="item">
            <span class="span1">{{myData.faceCreditHour!=undefined&& myData.netCreditHour!=undefined?Math.round((myData.faceCreditHour + myData.netCreditHour)*10)/10:0}}</span>学时
            <div>已获得学时</div>
          </div>
          <div class="hr"></div>
          <div class="item" style="width: calc(50% - 30rpx);">
            <span  class="span1">{{myData.learnDuration!=undefined?Math.round(myData.learnDuration/60*10)/10:0}}</span>小时
            <div>已学习时长</div>
          </div>
        </div>
        <div class="total-div-item" style="margin-right:0">
          <div class="title" style="display: block;">课程学习数据</div>
          <div class="item">
            <span  class="span2">{{myData.faceCreditHour!=undefined?Math.round(myData.faceCreditHour*10)/10:0}}</span>学时
            <div>面授（直播课程）学时</div>
          </div>
          <div class="hr"></div>
          <div class="item"  style="width: calc(50% - 30rpx);">
            <span  class="span2">{{myData.netCreditHour!=undefined?Math.round(myData.netCreditHour*10)/10:0}}</span>学时
            <div>网络（培训课程）学时</div>
          </div>
        </div>
        <div class="total-div">
          <!-- <div class="item2">
            <div class="left">培训班</div>
            <div class="right">
              已参与 <span>{{TrainsData.trainCount!=undefined?TrainsData.trainCount:0}}</span> 个/已获得 <span>{{TrainsData.classHour!=undefined&&liveData.trainClassHour!=undefined? Math.round((TrainsData.classHour + liveData.trainClassHour)*10)/10:0}}</span> 学时
            </div>
          </div> -->
          <div class="item2" @click="IntoInfo(0)">
            <div class="left">面授学时</div>
            <div class="right">
              已参与 <span>{{myData.faceCourseCount!=undefined?myData.faceCourseCount:0}}</span> 场直播课程/已获得 <span>{{myData.faceCreditHour!=undefined?Math.round(myData.faceCreditHour*10)/10:0}}</span> 学时
            </div>
            <div class="opr">
              <span>查看详情</span>
              <img src="/images/arrow-more.png" />
            </div>
          </div>
          <div class="item2" @click="IntoInfo(1)">
            <div class="left">网络学时</div>
            <div class="right">
              已参与<span>{{myData.netCourseCount!=undefined?myData.netCourseCount:0}}</span> 个课程/已获得 <span>{{myData.netCreditHour!=undefined?Math.round(myData.netCreditHour*10)/10:0}}</span> 学时
            </div>
            <div class="opr">
              <span>查看详情</span>
              <img src="/images/arrow-more.png" />
            </div>
          </div>

        </div>
      </div>
    </div>
    <el-dialog  :visible.sync="typevisible"  class="day-schedule-dialog" :show-close="false">
			<div class="h_title">
				<div class="title">学时详情</div>
				<a  class="close" @click="typevisible=false">&times;</a>
			</div>
		  <div class="type-list">
        <div class="tabs-list">
          <a  :class="[query.CreditHourType==0?'active':'']" @click="changType(0)">面授学时</a>
          <a  :class="[query.CreditHourType==1?'active':'']" @click="changType(1)">网络学时</a>
        </div>
        <div class="txt" v-if="query.CreditHourType==1">
          已参与<span>{{myData.netCourseCount!=undefined?myData.netCourseCount:0}}</span> 个课程/已获得 <span>{{myData.netCreditHour!=undefined?Math.round(myData.netCreditHour*10)/10:0}}</span> 学时
        </div>
        <div class="txt" v-else>
          已参与 <span>{{myData.faceCourseCount!=undefined?myData.faceCourseCount:0}}</span> 场直播课程/已获得 <span>{{myData.faceCreditHour!=undefined?Math.round(myData.faceCreditHour*10)/10:0}}</span> 学时
        </div>
        <div class="item grey">
          <div class="title">课程名称</div>
          <div class="num">获得学时</div>
        </div>
        <div v-for="item in list" class="item">
          <div class="title">{{item.recordTitle}}</div>
          <div class="num">已获得 {{item.creditHour}} 学时</div>
        </div>
        <el-pagination
        v-if="query.total>query.MaxResultCount"
        class="my_pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="query.page"
        :page-sizes="[5, 10, 20]"
        :page-size="query.MaxResultCount"
        layout="total, sizes, prev, pager, next, jumper"
        :total="query.total"
      ></el-pagination>
      <NoContent v-if="list.length==0&&!loading"></NoContent>
      </div>
		</el-dialog>
  </div>
</template>
<script>

import { getMyHours,getSumCoursesHours,geSumTrainsHours,getSumLives ,getMyProfile,getMySumDetail} from '@/api/user'
import SideMenu from "@/layout/SideMenu.vue";
import NoContent from '@/components/NoContent'
export default {
  name: 'MyTotal',
  data() {
   return {
        userinfo:{},
				myData:{},
				//  CoursesData:{},
				// TrainsData:{},
				// liveData:{},
        typevisible:false,
        list:[],
        loading:false,
        query:{
				  CreditHourType: 0 ,//0面授 1：网络
				  SkipCount:0,
				  MaxResultCount:5,
				  page:1,
				  total:0
				 },
   }
  },
  components: {
      SideMenu,
      NoContent
  },
  mounted(){
    if (this.$store.getters.token) {
      getMyProfile().then((res) => {
        this.userinfo = res;
      });
      this.init()
    } else{
      this.$store.dispatch("user/toggleloginbox", true);
    }

  },
  methods: {
    init(){
				getMyHours().then(res=>{
					this.myData = res
				})
				// getSumCoursesHours().then(res=>{
				// 	this.CoursesData = res
				// })

				// geSumTrainsHours().then(res=>{
				// 	this.TrainsData = res
				// })

				// getSumLives().then(res=>{
				// 	this.liveData = res
				// })
			},
      IntoInfo(type){
        this.typevisible = true
        this.changType(type)
      },
      getList(){
				let data = {
					CreditHourType:this.query.CreditHourType,
					SkipCount:(this.query.page-1)*this.query.MaxResultCount,
					MaxResultCount:this.query.MaxResultCount,
				}
        this.loading=true
				getMySumDetail(data).then(res=>{
					this.query.total = res.totalCount
					this.list = res.items
          this.loading=false
				}).catch(()=>{
          this.loading=false
				})
			},
      changType(index){
        this.query.CreditHourType = index
        this.query.page = 1
        this.list = []
        this.getList()
      },
      handleSizeChange(val) {
        this.query.MaxResultCount = val
        this.getList()
      },
      handleCurrentChange(val) {
        this.query.page = val
        this.getList()
      },
  }
}
</script>

