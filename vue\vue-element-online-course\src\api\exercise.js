import axios from '@/axios'

export function getbanks(data) { // 我的题库
    return axios.gets('/api/exams/exercises/public/bank',data)
}
export function getMyReport(id) { // 首页 报表信息
    return axios.gets('/api/exams/exercises/records/my-report?exerciseBankId='+ id)
}
export function getMyRecord(data) { // 我的答题记录
    return axios.gets('/api/exams/exercises/records/my', data)
}
export function getKnowledges (id) { // 获取题库下的知识点
    return axios.gets('/api/exams/exercises/paper/knowledges?bankId='+ id)
}
export function getQuestions (data) { // 自主组卷使用，查询多个知识点下的题目数量
    return axios.gets('/api/exams/exercises/paper/questions',data)
}
export function createDailyPaper (id) { // 每日练习，生成练习卷 获取试卷Id
    return axios.posts('/api/exams/exercises/paper/daily?exerciseBankId='+ id)
}
export function createPaper (data) { // 自主练习，生成练习卷
    return axios.posts('/api/exams/exercises/paper/autonomy',data)
}
export function getPaperKnowledge(data) { // 知识点练习，获取练习卷
    return axios.posts('/api/exams/exercises/paper/knowledge',data)
}
export function getPaper(data) { // 开始答题 获取试卷内容
    return axios.posts('/api/exams/exercises/records/start',data)
}
export function SubmitPaper(data) { // 提交练习
    return axios.posts('/api/exams/exercises/records/submit',data)
}
export function getPaperResult(id) { // 提交练习
    return axios.gets('/api/exams/exercises/records/result?exerciseRecordId='+id)
}
