// // 开发环境
export default {
  base: {
    // ip: 'http://localhost',
    // authip: 'http://localhost',
    // auth_port: ':44369',
    // identity_port: ':21001',
    // backend_port: ':21001',
    // file_ip: 'http://localhost:44327'

    ip: 'http://qc.ciep-pimp.com',
    authip: 'http://qc.ciep-pimp.com',
    auth_port: '',
    identity_port: '',
    backend_port: '',
    file_ip: 'http://qc.ciep-pimp.com'

    // ip: 'https://baogang.91yunlifang.com',
    // authip: 'https://baogang.91yunlifang.com',
    // auth_port: ':44398',
    // identity_port: ':42378',
    // backend_port: ':42378',
    // file_ip:'https://baogang.91yunlifang.com:42378',

  },
  client: {
    client_id: 'course-public-web-client',
    client_secret: '1q2w3e*',
    grant_type: 'password'
  },

  // returnIP: 'https://baogang.91yunlifang.com', // 当前网站
  // adminIp: 'http://**************:10529', //机构后台管理
  // examIP: "https://bgexam.91yunlifang.com" //考核系统

  // returnIP: 'http://localhost:7537', // 当前网站
  // adminIp: 'http:/localhost:10527', //机构后台管理
  // examIP: "http://bgexam.91yunlifang.com" //考核系统

  returnIP: 'http://qc.ciep-pimp.com', // 当前网站
  adminIp: 'http://qcadmin.ciep-pimp.com', //机构后台管理
  examIP: "http://qcexam.ciep-pimp.com" //考核系统

}
