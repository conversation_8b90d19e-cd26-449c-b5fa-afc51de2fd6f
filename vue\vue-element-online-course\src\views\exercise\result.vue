<template>
  <div class="exam" v-if="exam!=undefined&& exam.exercisePaper.questions!=undefined&& exam.exercisePaper.questions.length>0"  >
     <div class="exambody">
      <el-scrollbar  class="tihao-list" scroll-y="true">
        <h2 class="tk-title">
          <svg
          xmlns="http://www.w3.org/2000/svg"
          xmlns:xlink="http://www.w3.org/1999/xlink"
          width="70px" height="4px">
          <path fill-rule="evenodd"  fill="rgb(0, 150, 255)"
          d="M67.875,4.000 C66.770,4.000 65.875,3.105 65.875,2.000 C65.875,0.895 66.770,-0.000 67.875,-0.000 C68.980,-0.000 69.875,0.895 69.875,2.000 C69.875,3.105 68.980,4.000 67.875,4.000 ZM2.875,4.000 C1.770,4.000 0.875,3.105 0.875,2.000 C0.875,0.895 1.770,-0.000 2.875,-0.000 C3.980,-0.000 4.875,0.895 4.875,2.000 C4.875,3.105 3.980,4.000 2.875,4.000 Z"/>
          </svg>
          答题卡
        </h2>
        <div v-for="qt in exam.exercisePaper.questions" class="tk-item">
        <div v-if="qt.questionType==0" class="title" >单选题（每题{{qt.itemscore}}分，共{{qt!=undefined && qt.subList!=undefined?qt.subList.length:0}}题）</div>
        <div v-else-if="qt.questionType==1" class="title" >多选题（每题{{qt.itemscore}}分，共{{qt!=undefined && qt.subList!=undefined?qt.subList.length:0}}题）</div>
        <div v-else-if="qt.questionType==2" class="title" >判断题（每题{{qt.itemscore}}分，共{{qt!=undefined && qt.subList!=undefined?qt.subList.length:0}}题）</div>
        <div v-else-if="qt.questionType==3" class="title" >填空题（每题{{qt.itemscore}}分，共{{qt!=undefined && qt.subList!=undefined?qt.subList.length:0}}题）</div>
        <div v-else-if="qt.questionType==6" class="title" >简答题（每题{{qt.itemscore}}分，共{{qt!=undefined && qt.subList!=undefined?qt.subList.length:0}}题）</div>
        <span
        v-for="(q,index) in qt.subList"
        :class="[q.questionType==3 || q.questionType==6? 'dark': q.answer2===undefined||q.answer2==='' ||q.answer2===null || q.answer2.length==0?'grey': q.IsRight?'green':'red']"
        @click="changeIndex(q.questionType,q.order)"
        >{{index+1}}
        </span>
        </div>
      </el-scrollbar>
      <div  v-if="exam!=null && exam.exercisePaper.questions!=null"  class="cnt" >
        <el-breadcrumb separator-class="el-icon-arrow-right" class="bread_con">
          <el-breadcrumb-item :to="{ path: '/' }">
            <i class="el-icon-location-outline" />
          </el-breadcrumb-item>
          <el-breadcrumb-item :to="{ path: 'exerciseCenter',query:{ id:exerciseBankId,name:exerciseBankName} }">
            课程练习
          </el-breadcrumb-item>
          <el-breadcrumb-item>{{exam.exercisePaper.name}}</el-breadcrumb-item>
        </el-breadcrumb>
        <div class="questions-list-box">
          <div class="" v-for="qt in exam.exercisePaper.questions" >
            <div class="title" v-if="qt.questionType==0">单选题（每题{{qt.itemscore}}分，共{{qt!=undefined && qt.subList!=undefined?qt.subList.length:0}}题）</div>
            <div class="title" v-else-if="qt.questionType==1">多选题（每题{{qt.itemscore}}分，共{{qt!=undefined && qt.subList!=undefined?qt.subList.length:0}}题）</div>
            <div class="title" v-else-if="qt.questionType==2">判断题（每题{{qt.itemscore}}分，共{{qt!=undefined && qt.subList!=undefined?qt.subList.length:0}}题）</div>
            <div class="title" v-else-if="qt.questionType==3">填空题（每题{{qt.itemscore}}分，共{{qt!=undefined && qt.subList!=undefined?qt.subList.length:0}}题）</div>
            <div class="title" v-else-if="qt.questionType==6">简答题（每题{{qt.itemscore}}分，共{{qt!=undefined && qt.subList!=undefined?qt.subList.length:0}}题）</div>
            <div class="test-item-div" v-for="(ex,index) in qt.subList" :id="'anchor-'+ ex.questionType +'-'+ ex.order">
                <!-- 单选 -->
              <div v-if="ex.questionType==0"  class="danxuan-item">
                  <span class="tigan">{{index+1}} . {{ex.questionStem.Title}}（{{ex.score}}分） </span>
                  <div v-if="ex.questionStem.Title_Imgs!=null" class="tigan-imgs">
                      <img
                          fit="contain"
                          :src="image1"
                          :key="'img'+ ex.questionBankId + index1"
                          v-show="image1!=''"
                          v-for="(image1,index1) in ex.questionStem.Title_Imgs"

                          />
                  </div>
                  <el-radio-group v-model="ex.answer2"  disabled>
                      <el-radio style="margin-bottom:20px"   :label="option.Order" v-for="(option,index) in ex.questionStem.Options" :key="ex.questionBankId + option.Order">
                          {{answerOrder[option.Order-1]}}.{{option.Title}}
                          <div  v-if="option.Images!=null" class="xuanxiang-imgs">
                              <img
                                  fit="contain"
                                  :src="oimage"
                                  :key="'img'+ ex.questionBankId + option.Order+ index2"
                                  v-show="oimage!=''"
                                  v-for="(oimage,index2) in option.Images"

                                  />
                          </div>
                      </el-radio>

                  </el-radio-group>

              </div>
              <!-- 多选 -->
              <div v-if="ex.questionType==1"  class="duoxuan-item">
                  <span class="tigan"> {{index+1}} . {{ex.questionStem.Title}} （{{ex.score}}分） </span>
                  <div v-if="ex.questionStem.Title_Imgs!=null" class="tigan-imgs">
                      <img
                          fit="contain"
                          :src="image"
                          :key="'img'+ ex.questionBankId + index1"
                          v-show="image!=''"
                          v-for="(image,index1) in ex.questionStem.Title_Imgs"

                          />
                  </div>
                  <el-checkbox-group v-model="ex.answer2" disabled >
                      <el-checkbox   shape="square" style="margin-bottom:20px"    :label="option.Order" v-for="(option,index) in ex.questionStem.Options" :key="ex.questionBankId + option.Order">
                          {{answerOrder[option.Order-1]}}.{{option.Title}}
                          <div  v-if="option.Images!=null"  class="xuanxiang-imgs">
                              <img
                                  :src="oimage"
                                  :key="'img'+ ex.questionBankId + option.Order+ index2"
                                  v-show="oimage!=''"
                                  v-for="(oimage,index2) in option.Images"

                                  />
                          </div>
                      </el-checkbox>
                  </el-checkbox-group>
              </div>
              <!-- 判断 -->
              <div v-if="ex.questionType==2" class="panduan-item" >
                  <span class="tigan"> {{index+1}} . {{ex.questionStem.Title}} ({{ex.score}}分) </span>
                  <div v-if="ex.questionStem.Title_Imgs!=null" class="tigan-imgs">
                      <img
                          :src="image"
                          :key="'img'+ ex.questionBankId + index1"
                          v-show="image!=''"
                          v-for="(image,index1) in ex.questionStem.Title_Imgs"

                          />
                  </div>

                  <el-radio-group v-model="ex.answer2" direction="horizontal" disabled>
                    <el-radio label="正确">A.正确</el-radio>
                    <el-radio label="错误">B.错误</el-radio>
                  </el-radio-group>
              </div>
              <!-- 填空题 -->
              <div v-if="ex.questionType==3" class="blank-item" >
                  <span class="tigan"> {{index+1}} .
                  <span v-html="ex.questionStem.Title"></span>
                  <!-- <span v-for="(str,i) in ex.questionStem.Title.split(/_+/)">
                      {{str}}
                      <span v-if="i<ex.questionStem.Title.split(/_+/).length-1" class="blank_answer">{{ex.answer3.length>i+1?ex.answer3[i]:''}}</span>
                  </span>  -->
                  ({{ex.score}}分) </span>
                  <div v-if="ex.questionStem.Title_Imgs!=null" class="tigan-imgs">
                      <img
                          :src="image"
                          :key="'img'+ ex.questionBankId + index1"
                          v-show="image!=''"
                          v-for="(image,index1) in ex.questionStem.Title_Imgs"

                          />
                  </div>
              </div>
              <!-- 简答题 -->
              <div v-if="ex.questionType==6" class="expand-item" >
                  <span class="tigan"> {{index+1}} . {{ex.questionStem.Title}} ({{ex.score}}分) </span>
                  <div v-if="ex.questionStem.Title_Imgs!=null" class="tigan-imgs">
                      <img
                          :src="image"
                          :key="'img'+ ex.questionBankId + index1"
                          v-show="image!=''"
                          v-for="(image,index1) in ex.questionStem.Title_Imgs"
                          />
                  </div>
                <span class="expand-answer">{{ex.answer2}}</span>
              </div>
              <!--解析-->
              <div class="test-analysis-box" >
                <div v-if="ex.IsRight&&ex.hasRightAnswer" class="isright-p">
                  <span class="svg-span right">
                      <svg
                      xmlns="http://www.w3.org/2000/svg"
                      xmlns:xlink="http://www.w3.org/1999/xlink"
                      width="16px" height="12px">
                      <path fill-rule="evenodd"  fill="rgb(255, 255, 255)"
                      d="M15.600,3.283 L7.284,11.591 C6.936,11.938 6.450,12.035 6.004,11.929 C5.557,12.035 5.070,11.938 4.721,11.591 L0.380,7.269 C-0.154,6.737 -0.154,5.874 0.380,5.342 L1.349,4.378 C1.884,3.846 2.751,3.846 3.286,4.378 L6.005,7.085 L12.705,0.392 C13.238,-0.140 14.102,-0.140 14.635,0.392 L15.600,1.356 C16.133,1.888 16.133,2.751 15.600,3.283 Z"/>
                      </svg>
                    </span>
                  <span class="svg-txt">答对了</span>
                </div>
                <div v-else-if="ex.IsRight==false&&ex.hasRightAnswer"   class="isright-p">
                  <span class="svg-span wrong">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    xmlns:xlink="http://www.w3.org/1999/xlink"
                    width="13px" height="13px">
                    <path fill-rule="evenodd"  fill="rgb(255, 255, 255)"
                    d="M8.903,6.978 L11.612,9.691 C12.145,10.225 12.145,11.090 11.612,11.623 L10.647,12.589 C10.114,13.123 9.249,13.123 8.716,12.589 L6.004,9.873 L3.284,12.591 C2.751,13.123 1.886,13.123 1.353,12.591 L0.388,11.627 C-0.145,11.095 -0.145,10.232 0.388,9.700 L3.113,6.978 L0.400,4.260 C-0.133,3.727 -0.133,2.861 0.400,2.328 L1.365,1.362 C1.898,0.828 2.762,0.828 3.295,1.362 L6.012,4.082 L8.705,1.392 C9.238,0.860 10.102,0.860 10.635,1.392 L11.600,2.356 C12.133,2.888 12.133,3.751 11.600,4.283 L8.903,6.978 Z"/>
                    </svg>
                  </span>
                  <span class="svg-txt">答错了</span>
                </div>
                <div v-if="(ex.questionType==3 || ex.questionType==6)">学员得分：{{ex.userScore }} 分</div>
                <div v-if="ex.questionType!=3 && (ex.answer2==undefined || ex.answer2== '')">学员答案：无</div>
                <div v-else-if="ex.questionType==0&& ex.answer2!=undefined"> 学员答案：<span>{{answerOrder[ex.answer2-1]}} </span></div>
                <div v-else-if="ex.questionType==1&& ex.answer2!=undefined"> 学员答案：<span v-for="b in ex.answer2">{{answerOrder[b-1]}} </span></div>
                <div v-else-if="ex.questionType!=3&& ex.answer2!=undefined" > 学员答案：<span>{{ex.answer2}}</span></div>

                <div  v-if="ex.questionType==0||ex.questionType==1">
                  <div >
                    <span>正确答案：</span>
                    <span v-for="a in ex.answer.OptionAnswers">{{answerOrder[a.Order-1]}} </span>
                  </div>
                </div>
                <div  v-else-if="ex.questionType==2">
                  <span>正确答案：</span>
                  <span >{{ex.answer.JudgeAnswer==1?'正确':'错误'}}</span>
                </div>
                <div >
                  <span>答案解析：</span>
                  <span >{{ex.analysis==''|| ex.analysis==null?"无":ex.analysis}}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="right-box">
          <!--提交试卷-按钮-->
          <!-- <div class="submitPage-btn-box">

            <button square type="info" native-type="button" @click="returnToList" class="submitPage-btn">返回 </button>
          </div> -->
        <!--剩余时间及进度-->
          <div  class="lefttime">
            <span class="span1"  >
              考试成绩
             <span ref="lefttime" class="txt">{{exam.totalScore}}分</span>
            </span>
            <!-- <span class="span2">
              答题时长
              <span class="timelong">{{timeLong}}</span>
            </span> -->
            <span class="span3" >
             <div class="title">答题情况</div>
              <div class="itemscore">
                <span>判断题:</span>
                <span>{{sumData.panduan.score}}分</span>
              </div>
              <div  class="itemscore">
                <span>单选题:</span>
                <span>{{sumData.danxuan.score}}分</span>
              </div>
              <div  class="itemscore">
                <span>多选题:</span>
                <span>{{sumData.duoxuan.score}}分</span>
              </div>
              <!-- <div  class="itemscore">
                <span>填空题:</span>
                <span>{{sumData.blank.score}}分</span>
              </div>
              <div  class="itemscore">
                <span>简答题:</span>
                <span>{{sumData.expand.score}}分</span>
              </div>   -->
            </span>
          </div>
        </div>
      </div>

  </div>

</template>

<script>
import { getPaperResult } from "@/api/exercise";
export default {
  name: "result",
  components:{
  },
  data() {
    return {
      question_index: 0,
      IsshowPopup:false, //提卡的popup
      exam: null,
      answerOrder:['A','B','C','D','E','F','G','H','I','J','K','L','M','N'],
      unansweredNum:0, //未答数
      rightNum:0,//正确数
      wrongNum:0 ,//错误数
      subjectiveNum:0,
      // userInfo:null,
      // showPopover:false,
      sumData:{
        danxuan:{ // 单选
          count:0,
          itemScore:0,
          score:0
        },
        duoxuan:{ // 多选
          count:0,
          itemScore:0,
          score:0
        },
        panduan:{  //判断
          count:0,
          itemScore:0,
          score:0
        },
        blank:{ // 填空
          count:0,
          itemScore:0,
          score:0
        },
        expand:{ // 简答
          count:0,
          itemScore:0,
          score:0
        }
        // showPopover:false
      },
      timeLong:'0', //答题时长
      exerciseBankId:'',
      exerciseBankName:''
    };
  },

  mounted() {
    this.exerciseBankId = this.$route.query.tkid
      this.exerciseBankName = this.$route.query.tkName
	  this.getResult(this.$route.query.exerciseRecordId)
  },
  methods: {
    getResult(id){
      getPaperResult(id).then(
        res => {
          this.exam = res;
          this.exam.replyContent = JSON.parse(this.exam.replyContent); //之前保存的数据
          // if(this.exam.examination.answerViewTiming===1){ this.exam.examination.isPublishScore=1}
          this.exam.exercisePaper.questions.forEach(element => {
            //题目选项转json
            element.questionStem = JSON.parse(element.questionStem);
			      element.answer = JSON.parse(element.answer);
            //题干中的图片
            if (element.questionStem.Title_Imgs != null)  element.questionStem.Title_Imgs = element.questionStem.Title_Imgs.split(",");
            //选项中的图片ss
           //  console.log(element)
           if(element!=undefined && element.questionStem!=undefined && element.questionStem.Options!=undefined &&element.questionStem.Options.length>0)
            element.questionStem.Options.forEach(item => {
             if(item.Images!=null) item.Images = item.Images.split(",");
            })
            // 填空题
            if (element.questionType == 3) {
                //console.log(element)
                var reg = /_{2,}/g
                element.questionStem.Title = element.questionStem.Title.replaceAll(reg, "____")
            }
            //判断题，每题分值，数目，得分
            element.userScore=0
            element.hasRightAnswer=true
            if(this.exam.replyContent!=null){  //提交记录
              var q =  this.exam.replyContent.find(item=>item.Q== element.id)  //answer2 用户选项
               if(q) {
                 if(q.S==undefined)
                 {
                   q.S=0
                 }
                  if(element.questionType == 0){ //单选
                    element.answer2 = q.O!==null? q.O[0]:undefined
                    this.sumData.danxuan.count++
                    this.sumData.danxuan.score += q.S
                  }
                  if(element.questionType == 1) { // 多选
                    element.answer2 = q.O!==null? q.O:undefined
                    this.sumData.duoxuan.count++
                    this.sumData.duoxuan.score += q.S
                  }
                  if(element.questionType == 2) { //判断
                    element.answer2 = q.J!==null?(q.J===1?"正确":"错误"):''
                    this.sumData.panduan.count++
                    this.sumData.panduan.score += q.S
                  }
                  if(element.questionType == 3){ // 填空题
                    element.hasRightAnswer=false
                    // element.answer3 = q.ba'
                    var strList =  element.questionStem.Title.split(/_+/)
                    var str = ''
                    var str1 = ''
                    for(let i = 0;i<strList.length-1;i++){
                      // console.log(q)
                      str += strList[i]
                      str1 = q.BA[i]!=null && q.BA[i]!=undefined?q.BA[i]:''
                      str += '<span class="blank_answer">'+ str1 + '</span>'
                    }
                    if(q.BA.length>0)  element.answer2 = 1 // 题卡的颜色变化
                    element.questionStem.Title = str
                    this.sumData.blank.count++
                    this.sumData.blank.score += q.S
                  }
                  if(element.questionType == 6){ // 简答题
                    element.hasRightAnswer=false
                    element.answer2 = q.RA
                    this.sumData.expand.count++
                    this.sumData.expand.score += q.S
                  }
                  element.IsRight= q.R
                  element.userScore=q.S
               }
               if(element.questionType== 3 ||  element.questionType == 6) this.subjectiveNum++
               else{
                if(element.answer2===undefined||element.answer2===''|| element.answer2===null || element.answer2.length==0) this.unansweredNum++ //未答
                if(element.IsRight && element.questionType!= 3 && element.questionType != 6) this.rightNum++ //答题正确
               }
            }
            else{ //未提交结果
              if(element.questionType == 3){ // 填空题
                    element.hasRightAnswer=false
                    // element.answer3 = q.ba'
                    var strList =  element.questionStem.Title.split(/_+/)
                    var str = ''
                    var str1 = ''
                    for(let i = 0;i<strList.length-1;i++){
                      // console.log(q)
                      str += strList[i]
                      // str1 = q.BA[i]!=null && q.BA[i]!=undefined?q.BA[i]:''
                      str += '<span class="blank_answer"></span>'
                    }
                    //  if(q.BA.length>0)  element.answer2 = 1 // 题卡的颜色变化
                    element.questionStem.Title = str
                  }
            }
          });
           if(this.exam.replyContent==null){ this.unansweredNum =this.exam.exercisePaper.questions.length }
          this.wrongNum = this.exam.exercisePaper.questions.length - this.unansweredNum - this.rightNum - this.subjectiveNum // 答错数
          //   if(this.exam.startDate!=null&&this.exam.submitDate!=null)
          //   { this.timeLong =  this.getTimeLong(this.exam.startDate,this.exam.submitDate)  }//时长
		      //  console.log(this.exam.exercisePaper.questions)
          this.exam.exercisePaper.questions =  this.GroupByType(this.exam.exercisePaper.questions)

        },
        error => {
          console.log(error);
        }
      );

    },
    // previewImg(imgs){
    //   //console.log
    //   ImagePreview(imgs);
    // },
    showPopup(){ //打开题卡
      this.IsshowPopup = true
    },
    changeIndex(type,index){
      this.question_index = index
      this.goAnchor('#anchor-'+ type +'-'+(index))
      this.IsshowPopup = false
    },
    returnToList(){

      this.$router.push({ name: "ExerciseCenter" ,query:{
        tkid: this.exerciseBankId,
        tkName:this.exerciseBankName
      }})
    },
    goAnchor (selector) { //锚点跳转
      const anchor = this.$el.querySelector(selector);
      this.$nextTick(() => {
          window.scrollTo(0,anchor.offsetTop-65);
      })
    },
    GroupByType(items){ // 按照类型分组
        let newArr = [];
        items.forEach((item, i) => {
          let index = -1;
          //some用来查找数组中是否存在某个值
          let isExists = newArr.some((newItem, j) => {
            if (item.questionType == newItem.questionType) {
              index = j;
              return true;
            }
          })
          if (!isExists) {
            newArr.push({
              questionType: item.questionType,
              itemscore:item.score,
              // classGroupText:item.classGroup==null?'未分类':item.classGroup,
              subList: [item]
            })
          } else {
            newArr[index].subList.push(item);
          }

        })
        return newArr
    },
    // getTimeLong(start, end) {
    //   var dateDiff = new Date(end).getTime() - new Date(start).getTime();//时间差的毫秒数
    //   var dayDiff = Math.floor(dateDiff / (24 * 3600 * 1000)); //计算出相差天数
    //   var leave1 = dateDiff % (24 * 3600 * 1000);     //计算天数后剩余的毫秒数
    //   var hours = Math.floor(leave1 / (3600 * 1000)); //计算出小时数
    //   //计算相差分钟数
    //   var leave2 = leave1 % (3600 * 1000);   //计算小时数后剩余的毫秒数
    //   var minutes = Math.floor(leave2 / (60 * 1000)); //计算相差分钟数
    //   //计算相差秒数
    //   var leave3 = leave2 % (60 * 1000);     //计算分钟数后剩余的毫秒数
    //   var seconds = Math.round(leave3 / 1000);
    //   var str= '--'
    //   if(seconds<0) return str
    //   if(dayDiff == 0) str = (hours>=10?hours:'0'+ hours) + ":" + (minutes>=10?minutes:'0'+ minutes) + ":" + (seconds>=10?seconds:'0'+ seconds)
    //   else str =dayDiff + "天 " + (hours>=10?hours:'0'+ hours) + ":" + (minutes>=10?minutes:'0'+ minutes) + ":" + (seconds>=10?seconds:'0'+ seconds)
    //   return str
    // }
 },


};
</script>
<style  >
</style>
