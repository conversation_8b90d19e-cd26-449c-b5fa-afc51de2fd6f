<template>
    <div class="profile_info_con">
        <div class="maps">
            <img src="../../assets/image/zan/map.png" />
            <span>考试中心</span>
            <img src="../../assets/image/zan/left.png" />
            <span>考试评价</span>
        </div>
        <div class="mytrains">
            <div v-for="item in exams" class="titem" style="cursor: auto">
                <div class="evalute-item">
                    <div class="evalute-item-top">
                        <img src="/images/icon-exam.png" />
                        <div class="evalute-info">
                            <div class="titles">{{ item.name }}</div>
                            <div class="con-state">
                                <span v-if="item.status == 1" class="state unstarted">未开始</span>
                                <span v-if="item.status == 2" class="state isTraining">进行中</span>
                                <span v-else-if="item.status == 3" class="state isfinished">已结束</span>
                                <button v-if="item.status != 3" class="btn start" @click="intoExam(item)">
                                    进入考试
                                </button>
                                <button
                                    v-if="
                                        (item.status == 3 ||
                                            (item.answerViewTiming == 1 &&
                                                item.submitTimes > 0)) &&
                                        item.examUrl == null"
                                    class="btn start"
                                    @click="viewExam(item)">
                                    查看结果
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="times">
                        <img src="../../assets/image/zan/icon-time.png" />
                        <span>
                            考核时间：{{item.startDate| DateFromte("YYYY-MM-DD HH:mm")}}
                        至 {{item.endDate| DateFromte("YYYY-MM-DD HH:mm")}}
                        </span>
                    </div>
                </div>
            </div>
        </div>
        <div
            class="moreData"
            @click="getMore()"
            v-if="exams.length < examparams.total"
        >
            加载更多
        </div>
        <NoContent v-if="exams.length == 0"></NoContent>
    </div>
</template>
<script>
import { getExams, getCurrentTime } from "@/api/exam";
import config from "@/config";
import moment from "moment";
import NoContent from "@/components/NoContent";
export default {
    name: "TrainCenter",
    data() {
        return {
            examparams: {
                Status: 0,
                IsSubmit: null,
                Sorting: "startDate desc",
                SkipCount: 0,
                MaxResultCount: 8,
                page: 0,
            },
            exams: [],
        };
    },
    components: {
        NoContent,
    },
    mounted() {
        if (this.$store.getters.token) {
            this.getList();
        } else {
            this.$store.dispatch("user/toggleloginbox", true);
        }
    },
    methods: {
        async intoExam(item) {
            var returnUrl = config.returnIP + "/#/examcenter";

            await getCurrentTime().then((res) => {
                // 获取当前时间
                this.currentTime = res;
            });
            // if(item.status==1)
            // {
            //   this.$message.info('考试还没有开始，请耐心等待')
            //   return
            // }
            if (
                item.status == 1 || //未开始 //进行中并且允许重复提交
                (item.status == 2 &&
                    item.allowRepeatSubmit == true &&
                    item.allowSubmitTimes == 0) || //进行中并且允许重复提交 有提交次数限制
                (item.status == 2 &&
                    item.allowRepeatSubmit == true &&
                    item.allowSubmitTimes > 0 &&
                    item.allowSubmitTimes > item.submitTimes) || //进行中且未提交且（开考时间没有超时或没有时间限制）
                (item.status == 2 &&
                    item.submitTimes == 0 &&
                    (item.startLimitTime == 0 ||
                        item.isIgnoreStartLimitTime == 1 ||
                        moment(this.currentTime) <=
                            moment(item.startDate) +
                                item.startLimitTime * 60 * 1000))
            ) {
                //开始考试
                var url1 =
                    "/start?examId=" +
                    item.examinationId +
                    "&starttime=" +
                    item.startDate +
                    "&submitLimitTime=" +
                    item.submitLimitTime +
                    "&examName=" +
                    item.name +
                    "&timeLong=" +
                    item.timeLong +
                    "&returnUrl=" +
                    encodeURIComponent(returnUrl);
                if (item.examUrl != null)
                    url1 +=
                        "&weburl=" +
                        encodeURIComponent(
                            item.examUrl +
                                "?sojumpparm=" +
                                encodeURIComponent(
                                    this.$store.getters.name +
                                        ";" +
                                        this.$store.getters.givenname +
                                        ";" +
                                        item.examinationId
                                )
                        );
                var urlencode = encodeURIComponent(url1);
                var url =
                    config.examIP +
                    "/#/auth-redirect?redirect=" +
                    urlencode +
                    "&token=" +
                    this.$store.getters.token;
                console.log(url);
                window.open(url, "_blank");
            } else if (item.submitTimes > 0 && item.status == 2) {
                //进行中 已提交
                this.$message.info("考试已提交,耐心等待结束后再查看结果");
                return;
            }
            // else if(item.status==2 && item.isAnswered==1) //无法重复进入考核
            // {
            //    this.$message.info('您已进入过本场考试，无法重复进入')
            //    return
            // }
            else if (
                item.status == 2 &&
                item.submitTimes == 0 &&
                item.isIgnoreStartLimitTime == 0 &&
                moment(this.currentTime) >
                    moment(item.startDate) + item.startLimitTime * 60 * 1000
            ) {
                //开考时间超时
                this.$message.info(
                    "考试开始" + item.startLimitTime + "分钟后，禁止进入考试"
                );
                return;
            } else if (
                (item.status == 3 || item.submitTimes > 0) &&
                item.examUrl == null
            ) {
                // 查看结果
                var url1 =
                    "/result?examId=" +
                    item.examinationId +
                    "&returnUrl=" +
                    encodeURIComponent(returnUrl);
                var urlencode = encodeURIComponent(url1);
                var url =
                    config.examIP +
                    "/#/auth-redirect?redirect=" +
                    urlencode +
                    "&token=" +
                    this.$store.getters.token;
                window.open(url, "_blank");
            }
        },
        viewExam(item) {
            var returnUrl = config.returnIP + "/#/examcenter";
            var url1 =
                "/result?examId=" +
                item.examinationId +
                "&returnUrl=" +
                encodeURIComponent(returnUrl);
            var urlencode = encodeURIComponent(url1);
            var url =
                config.examIP +
                "/#/auth-redirect?redirect=" +
                urlencode +
                "&token=" +
                this.$store.getters.token;
            window.open(url, "_blank");
        },
        //改变状态
        changeState(IsSubmit) {
            this.examparams.page = 0;
            this.examparams.IsSubmit = IsSubmit;
            this.exams = [];
            this.getList();
        },
        getList() {
            let data = {
                IsSubmit: this.examparams.IsSubmit, // 0 未开始,1 进行中,2 已结束
                SkipCount:
                    this.examparams.page * this.examparams.MaxResultCount,
                MaxResultCount: this.examparams.MaxResultCount,
            };
            getExams(data).then((res) => {
                this.exams = this.exams.concat(res.items);
                this.examparams.total = res.totalCount;
            });
        },
        getMore() {
            this.examparams.page++;
            //console.log(this.examparams)
            this.getList();
        },
    },
};
</script>
<style scoped>

.mytrains {
    padding-bottom: 50px;
    position: relative;
    display: flex;
    flex-wrap: wrap;
    overflow: hidden;
}
.mytrains .titem {
    margin-right: 30px;
    margin-bottom: 50px;
}
.mytrains .titem:nth-child(3n+3) {
    margin-right: 0;
}
.mytrains .titem .evalute-item {
    position: relative;
    background: #FFFFFF;
    box-shadow: 0 5px 10px 0 rgba(0,0,0,0.10);
    border-radius: 20px;
    box-sizing: border-box;
    padding: 20px;
    width: 380px;
    height: 190px;
}
.evalute-item-top{
    display: flex;
    border-bottom:1px solid #ccc;
    padding-bottom: 20px;
    box-sizing: border-box;
}
.evalute-item-top>img{width: 90px;height: 90px;}
.evalute-info{
    width: 227px;
    height: 90px;
    margin-left: 20px;}
.evalute-info .titles{
    font-size: 16px;
    color: #333333;
    line-height: 24px;
    height: 45px;
}
.con-state{
    display: flex;
    justify-content: space-between;
    margin-top: 15px;
}
.state {
    border-radius: 6px;
    width: 70px;
    height: 28px;
    text-align: center;
    line-height: 28px;
    font-size: 14px;
}
.state.unstarted {
    background: #dffadf;
    color: #018b3e;
}
.state.isfinished {
    background: #eaeaea;
    color: #666;
}
.state.isTraining {
    background: #fbe3d9;
    color: #ed570e;
}
.btn {
    width: 100px;
    height: 32px;
    text-align: center;
    line-height: 30px;
    font-size: 14px;
    border-radius: 20px;
    border: 1px solid;
    background: none;
    vertical-align: middle;
    cursor: pointer;
}
.btn.start {
    color: #ed570e;
    border-color: #ed570e;
}
.btn.end {
    color: #999;
    border-color: #999;
}
.times{
    height: 50px;
    line-height: 50px;
    text-align: center;
}
.times>img{
    vertical-align: middle;
}
.times>span{
    vertical-align: middle;
    font-size: 13.5px;
    color: #999999;
}
</style>
