<template>
  <div
    class="notice-cent"
    style="margin-top: 40px;"
  >
    <div class="maps">
      <img src="../../assets/image/zan/map.png">
      <span>新闻资讯</span>
      <img src="../../assets/image/zan/left.png">
      <span>{{ mapname }}</span>
    </div>
    <div class="notice-box">
      <div class="notice-info-box">
        <div class="notice-title">
          {{ NoticeInfo.title }}
        </div>
        <div class="notice-time">
          {{
            NoticeInfo.publishDate
              | DateFromte("YYYY-MM-DD HH:mm:ss")
          }}
        </div>
        <div
          class="notice-content"
          v-html="NoticeInfo.content"
        />

        <div v-if="isPdf">
          <a :href="'./pdfjs/web/viewer.html?file='+pdfUrl" target="_blank">
            {{ pdfUrl }}
          </a>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { getNoticeInfo,getNewsInfo4Pdf } from "@/api/course";
import {resourcePath,} from "@/api/file";
// import NoContent from "@/components/NoContent";
export default {
    name: "Index",
    // components: {
    //     NoContent,
    // },
    data() {
        return {
            NoticeInfo: {},
            mapname:'',
            isPdf:'',
            pdfUrl:''
        };
    },
    watch: {
        $route: {
            handler: function (route) {
                this.getInfo();
            },
            immediate: true,
        },
    },
    created() {
        this.mapname = this.$route.query.mapname==='0'?'标准解读':this.$route.query.mapname==='1'?'行业资讯':this.$route.query.mapname==='2'?'培训资讯':this.$route.query.mapname==='3'?'课程资讯':'--';
        this.isPdf = this.$route.query.isPdf;
        console.log("this.isPdf111",this.isPdf)
    },
    methods: {
        getInfo() {
            // this.isPdf = this.$route.query.id
            console.log("this.isPdf222",this.isPdf)
            if(this.isPdf){
                getNewsInfo4Pdf({ id: this.$route.query.id }).then((res) => {
                    this.NoticeInfo = res;
                    resourcePath({
                    url: res.announcementAttachments[0].url
                    })
                    .then((result) => {
                        this.pdfUrl = encodeURIComponent(result)
                    })
                    .catch(() => {
                        this.$message.error('获取资源地址失败')
                    })
                });
                return
            }
            getNoticeInfo({ id: this.$route.query.id }).then((res) => {
                this.NoticeInfo = res;
            });
        },
    },
};
</script>
