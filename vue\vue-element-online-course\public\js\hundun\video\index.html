<!--
 * @Author: <PERSON><PERSON> (Email:<EMAIL> QQ:245803627)
 * @Date: 2022-04-08 17:26:02
 * @LastEditTime: 2022-07-01 14:10:55
 * @LastEditors: <PERSON><PERSON>
 * @Description: 
 * @FilePath: \public\hundunvideoh5\index.html
 * 
-->
<!DOCTYPE html>
<html lang="zh">

<head>
  <meta charset="utf-8" />
  <link rel="icon" href="./favicon.ico" />
  <meta name="author" content="http://www.jingge.com/" />
  <meta name="developer" content="PengXiang   email: <EMAIL>" />
  <meta name="theme-color" content="#000000" />
  <meta name="renderer" content="webkit" />
  <meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0, minimal-ui" />
  <meta http-equiv="Content-Security-Policy" content="upgrade-insecure-requests">
  <meta name="keywords" content="jingge,h5" />
  <meta name="description" content="h5 page" />
  <link rel="apple-touch-icon" href="./logo192.png" />
  <!--
      manifest.json provides metadata used when your web app is installed on a
      user's mobile device or desktop. See https://developers.google.com/web/fundamentals/web-app-manifest/
    -->
  <link rel="manifest" href="./manifest.json" />
  <title> </title>
  <link rel="stylesheet" href="./static/js/videoh5.min.css" />
  <style>
    @font-face {
      font-family: 'CustomVideoJS';
      src: url('static/font/vjs.eot');
      src: url('static/font/vjs.eot?#iefix') format('embedded-opentype'), url('static/font/vjs.woff') format('woff'), url('static/font/vjs.ttf') format('truetype');
      font-weight: normal;
      font-style: normal;
    }
  </style>
</head>

<body>
  <noscript>You need to enable JavaScript to run this app.</noscript>
  <div id="root"></div>
  <script type="text/javascript" src="./static/js/videoh5.min.js"></script>
</body>

</html>