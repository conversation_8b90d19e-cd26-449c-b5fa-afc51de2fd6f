import axios from '@/axios'

export function getCategory() { // 获取类别
    return axios.gets('/api/cms/public/knowledges/category')
}

export function getAuthors() { // 获取作者
    return axios.gets('/api/cms/public/knowledges/resources/authors')
}

export function getVideoInfo(id) { // 获取详情
	return axios.gets('/api/cms/public/knowledges/resources/'+ id)
}
export function getVideoList(data){
	return axios.gets('/api/cms/public/knowledges/resources',data)
}
export function getVideolike(data){// 点赞
	return axios.gets('/api/cms/public/knowledges/resources/like',data)
}
export function getVideoview (data){ //查看
	return axios.gets('/api/cms/public/knowledges/resources/view',data)
}
export function collectVideo(data){ // 收藏
	return axios.posts('/api/cms/public/knowledges/collection',data)
}

export function getMycollectVideo(data){ // 获取我的收藏
	return axios.gets('/api/cms/public/knowledges/collection/my',data)
}
export function agreeComment(data){ // 评论点赞
	return axios.posts('/api/cms/public/knowledges/comment/agree?id='+ data.id +'&isAgree='+ data.isAgree)
}
export function addComment(data){ // 添加评论
	return axios.posts('/api/cms/public/knowledges/comment',data)
}

export function getCommentList(data){ // 获取评论
	return axios.gets('/api/cms/public/knowledges/comment',data)
}
// /api/cms/public/knowledges/comment

export function getFileDownloadInfo(id) { // 获取资源地址
  return axios.gets(`/api/file-management/file/${id}/download-info`)
}