<template>
  <div class="profile_info_box live-cent">
    <div class="live-statue-list">
      <a @click="changeState()" :class="{active:train_query.TrainState==undefined}">全部</a>
        <a @click="changeState(0)" :class="{active:train_query.TrainState==0}">未开始</a>
        <a @click="changeState(1)" :class="{active:train_query.TrainState==1}">培训中</a>
        <a @click="changeState(2)" :class="{active:train_query.TrainState==2}">已结束</a>
    </div>
    <div class="mytrains">
      <div v-for="item in trains" @click="prewTraining(item.id)" class="titem">
        <div class="left">
          <span class="year">{{ item.startDate | DateFromte("YYYY年") }}</span>
          <span class="day">{{ item.startDate | DateFromte("MM-DD") }}</span>
        </div>
        <div class="line1">
          <hr width="0" />
          <span></span>
        </div>
        <div class="right">
          <img src="/images/icon-train.png" />
          <div class="con">
            <div class="title">{{ item.name }}</div>
            <div class="time">
              培训时间：{{ item.startDate | DateFromte("YYYY-MM-DD") }} 至
              {{ item.endDate | DateFromte("YYYY-MM-DD") }}
            </div>
            <div class="num">参与人数：{{ item.userCount }}人</div>
            <span v-if="item.trainState == 0" class="state unstarted"
              >未开始</span
            >
            <span v-if="item.trainState == 1" class="state isTraining"
              >培训中</span
            >
            <span v-if="item.trainState == 2" class="state isfinished"
              >已结束</span
            >
          </div>
        </div>
      </div>
      <div
        class="moreData"
        @click="getMore()"
        v-if="trains.length < train_query.total"
      >
        加载更多
      </div>
      <NoContent v-if="trains.length == 0"></NoContent>
    </div>
  </div>
</template>
<script>
import {getMyTrains} from '@/api/course';
import SideMenu from "@/layout/SideMenu.vue";
import NoContent from '@/components/NoContent'
export default {
  name: 'TrainCenter',
  data() {
   return {
     train_query:{
      TrainState:null, // 0 未开始,1 进行中,2 已结束
      SkipCount:0,
      MaxResultCount:5,
      page:1,
      total:0
     },
    trains:[]
   }
  },
  components: {
      SideMenu,
      NoContent
  },
  mounted(){
    if (this.$store.getters.token) {
      this.getList()
    } else{
      this.$store.dispatch("user/toggleloginbox", true);
    }
  },
  methods: {
    prewTraining(id){
      this.$router.push({name: 'TrainingInfo', query:{id: id}})
    },
    //改变状态
    changeState(state){
      this.train_query.page = 1
      this.train_query.TrainState = state
      this.trains = []
      this.getList()
    },
    getList(){
      let data = {
        TrainState:this.train_query.TrainState, // 0 未开始,1 进行中,2 已结束
        SkipCount:(this.train_query.page-1)*this.train_query.MaxResultCount,
        MaxResultCount:this.train_query.MaxResultCount,
      }
      getMyTrains(data).then(res=>{
        this.trains = this.trains.concat(res.items)
        this.train_query.total = res.totalCount
      })
    },
    getMore(){
      this.train_query.page++
      //console.log(this.train_query)
      this.getList()
    }
  }
}
</script>
<style scoped>

/* .moreData{
  position: static;
} */
.mytrains{
   background: #fff;
    padding-bottom: 150px;
    position: relative;
}
.live-cent {
    width: 1200px;
    margin: 0 auto;
    padding: 15px 0;
    display: block;
    min-height: 0;
    background: none
}
.mytrains .titem {
  background: #fff;
  margin: 0;
}
</style>