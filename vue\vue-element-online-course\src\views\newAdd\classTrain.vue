<template>
    <div class="biaozhun">
        <div class="maps">
            <img src="../../assets/image/zan/map.png" />
            <span>培训中心</span>
            <img src="../../assets/image/zan/left.png" />
            <span>培训班级</span>
        </div>
        <div class="card-box">
            <div class="card-item" v-for="(item, index) in trains" :key="index" @click="prewTraining(item.id)">
                <img class="card-img" :src="item.imgUrl" />
                <div class="card-class-box">
                    <p class="card-class-title">{{item.name}}</p>
                    <div class="card-class-info">
                        <div class="card-class-info-item">
                            <img src="../../assets/image/zan/class-num.png" />
                            <span>{{ item.userCount }}</span>
                        </div>
                        <!-- <div class="card-class-info-item">
                            <img src="../../assets/image/zan/class-type.png" />
                            <span>骨干教师</span>
                        </div> -->
                        <div class="card-class-info-item">
                            <img src="../../assets/image/zan/class-time.png" />
                            <span>
                                {{
                                    item.startDate | DateFromte("YYYY-MM-DD")
                                }}
                                至 {{ item.endDate | DateFromte("YYYY-MM-DD") }}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <el-pagination
            v-if="parmas.totalCount > parmas.maxResultCount"
            class="my_pagination"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="parmas.page"
            :page-sizes="[10, 20, 60]"
            :page-size="parmas.MaxResultCount"
            layout="total, sizes, prev, pager, next, jumper"
            :total="parmas.total"
            ></el-pagination>
    </div>
</template>
<script>
import { getMyTrains } from "@/api/course";
export default {
    data() {
        return {
            parmas: {
                SkipCount: 0,
                MaxResultCount: 10,
                page: 1,
                total: 0,
            },
            trains: [],
        };
    },
    methods: {
        prewTraining(id) {
            const url = this.$router.resolve({
                name: "TrainingInfo",
                query: {
                    id: id },
            }).href;

            window.open(url, "_blank");
        },
        handleSizeChange(val) {
            this.parmas.MaxResultCount = val;
            this.getList();
        },
        handleCurrentChange(val) {
            this.parmas.page = val;
            this.getList();
        },
        getList(){
            this.parmas.SkipCount = this.parmas.MaxResultCount * (this.parmas.page - 1);
            getMyTrains(this.parmas).then((res) => {
                this.trains = res.items;
                this.parmas.total = res.totalCount;
            });
        }
    },
    mounted() {
        this.getList();
    }
};
</script>
<style scoped>

</style>
