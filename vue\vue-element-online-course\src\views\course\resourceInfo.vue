<template>
  <div class="Res-page">
    <div
      v-if="currentRes != undefined"
      :class="['left-box', IsCloseRight ? 'CloseRight' : '']"
    >
      <div class="header-box">
        <span class="return_btn">
          <span class="svg-span-arrow">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              xmlns:xlink="http://www.w3.org/1999/xlink"
              width="6px"
              height="10px"
            >
              <path
                fill-rule="evenodd"
                fill="rgb(54, 58, 72)"
                d="M2.427,5.000 L5.711,8.284 C6.105,8.678 6.105,9.317 5.711,9.711 C5.317,10.105 4.678,10.105 4.284,9.711 L0.289,5.716 C0.092,5.519 -0.006,5.259 -0.006,5.000 C-0.006,4.741 0.092,4.481 0.289,4.284 L4.284,0.289 C4.678,-0.105 5.317,-0.105 5.711,0.289 C6.105,0.683 6.105,1.322 5.711,1.716 L2.427,5.000 Z"
              />
            </svg>
          </span>
          <a @click="returnToKC"> 返回课程</a></span>
        <span
          v-if="courseinfo.freeModel == 0"
          class="prev-box"
          @click="prevOne()"
        >
          <a>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              xmlns:xlink="http://www.w3.org/1999/xlink"
              width="16px"
              height="9px"
            >
              <path
                fill-rule="evenodd"
                fill="rgb(255, 255, 255)"
                d="M15.711,8.711 C15.317,9.105 14.678,9.105 14.284,8.711 L8.000,2.427 L1.716,8.711 C1.322,9.105 0.683,9.105 0.289,8.711 C-0.105,8.317 -0.105,7.678 0.289,7.284 L7.284,0.289 C7.481,0.092 7.741,-0.006 8.000,-0.006 C8.259,-0.006 8.519,0.092 8.716,0.289 L15.711,7.284 C16.105,7.678 16.105,8.317 15.711,8.711 Z"
              />
            </svg>
          </a>
          <span class="line" />
        </span>

        <span class="Res-title">{{ currentRes.name }}</span>
        <!-- <span
          v-if="courseinfo.freeModel == 0&&currentRes.resType!='html'"
          class="right-span"
        >
          <span class="duration" >{{ currentRes.duration | timeFromte }} </span>
          <span class="learnedtime">已学习{{ learnedtime | timeFromte }}</span>
          <button
            :class="isComplete ? 'disable' : ''"
            :disabled="isComplete"
            @click="finishRes()"
          >
            已完成学习
          </button>
        </span> -->
        <span
          class="shq_btn"
          @click="closeRight()"
        >
          <svg
            v-if="!IsCloseRight"
            xmlns="http://www.w3.org/2000/svg"
            xmlns:xlink="http://www.w3.org/1999/xlink"
            width="13px"
            height="10px"
          >
            <path
              fill-rule="evenodd"
              fill="rgb(255, 255, 255)"
              d="M12.711,5.716 L8.716,9.711 C8.322,10.105 7.683,10.105 7.289,9.711 C6.895,9.317 6.895,8.678 7.289,8.284 L10.573,5.000 L7.289,1.716 C6.895,1.322 6.895,0.683 7.289,0.289 C7.683,-0.105 8.322,-0.105 8.716,0.289 L12.711,4.284 C12.908,4.481 13.006,4.741 13.006,5.000 C13.006,5.259 12.908,5.519 12.711,5.716 ZM5.711,5.716 L1.716,9.711 C1.322,10.105 0.683,10.105 0.289,9.711 C-0.105,9.317 -0.105,8.678 0.289,8.284 L3.573,5.000 L0.289,1.716 C-0.105,1.322 -0.105,0.683 0.289,0.289 C0.683,-0.105 1.322,-0.105 1.716,0.289 L5.711,4.284 C5.908,4.481 6.006,4.741 6.006,5.000 C6.006,5.259 5.908,5.519 5.711,5.716 Z"
            />
          </svg>
          <svg
            v-else
            xmlns="http://www.w3.org/2000/svg"
            xmlns:xlink="http://www.w3.org/1999/xlink"
            width="13px"
            height="10px"
          >
            <path
              fill-rule="evenodd"
              fill="rgb(255, 255, 255)"
              d="M9.427,5.000 L12.711,8.284 C13.105,8.678 13.105,9.317 12.711,9.711 C12.317,10.105 11.678,10.105 11.284,9.711 L7.289,5.716 C7.092,5.519 6.994,5.259 6.994,5.000 C6.994,4.741 7.092,4.481 7.289,4.284 L11.284,0.289 C11.678,-0.105 12.317,-0.105 12.711,0.289 C13.105,0.683 13.105,1.322 12.711,1.716 L9.427,5.000 ZM5.711,9.711 C5.317,10.105 4.678,10.105 4.284,9.711 L0.289,5.716 C0.092,5.519 -0.006,5.259 -0.006,5.000 C-0.006,4.741 0.092,4.481 0.289,4.284 L4.284,0.289 C4.678,-0.105 5.317,-0.105 5.711,0.289 C6.105,0.683 6.105,1.322 5.711,1.716 L2.427,5.000 L5.711,8.284 C6.105,8.678 6.105,9.317 5.711,9.711 Z"
            />
          </svg>
        </span>
      </div>
      <!--视频-->
      <div
        v-if="
          currentRes.fileType != undefined &&
            currentRes.fileType == '.mp4'
        "
        class="videoBox"
      >
        <video id="myPlayer" />
      </div>
      <!--pdf-->
      <div
        v-if="
          currentRes.fileType != undefined &&
            currentRes.fileType == '.pdf'
        "
        v-loading="loadFileContent"
        class="videoBox"
        style="position: relative"
      >
        <iframe
          v-if="currentRes.url != '' && currentRes.url != null"
          style="width: 100%; height: 100%"
          :src="'./pdfjs/web/viewer.html?file=' + currentRes.url"
        />
        <a
          v-if="currentRes.url != '' && currentRes.url != null"
          class="fullscreen_btn"
          @click="
            fullscreen(
              './pdfjs/web/viewer.html?file=' + currentRes.url
            )
          "
        >
          <img src="/images/icon-fullsrceen.png">
        </a>
        <!-- <iframe :src="'./doc/doc.html?docId='+ currentRes.url +'&token=TOKEN&host=BCEDOC&enviroment=online'" /> -->
      </div>
      <!--h5-->
      <div
        v-if="
          currentRes.fileType != undefined &&
            currentRes.fileType == '.zip'
        "
        class="videoBox"
      >
        <iframe
          allow="microphone;camera;midi;encrypted-media;"
          :src="currentRes.url"
          scrolling="no"
        />
      </div>
      <!--三方-->
      <div
        v-if="currentRes.resType == 'hundun-video'"
        class="videoBox"
      >
        <iframe
          :src="'./js/hundun/video/index.html?' + currentRes.url"
          scrolling="no"
        />
      </div>
      <div
        v-if="currentRes.resType == 'geektime-video'"
        class="videoBox"
      >
        <iframe
          :src="'./js/geektime/video/index.html?' + currentRes.url"
          scrolling="no"
        />
      </div>
      <div
        v-if="currentRes.resType == 'geektime-html'"
        class="videoBox"
      >
        <iframe
          :src="'./js/geektime/html/index.html?' + currentRes.url"
          scrolling="no"
        />
      </div>
      <div
        v-if="currentRes.resType == 'ximalaya'"
        class="videoBox"
      >
        <iframe
          :src="'./js/ximalaya/index.html?' + currentRes.url"
          scrolling="no"
        />
      </div>
      <div
        v-if="currentRes.resType == 'pdfh5'"
        class="videoBox"
      >
        <iframe
          :src="'./js/pdfh5/index.html?' + currentRes.url"
          scrolling="no"
        />
      </div>
      <div
        v-if="currentRes.resType == 'videoh5'"
        class="videoBox"
      >
        <iframe
          :src="'./js/videoh5/index.html?' + currentRes.url"
          scrolling="no"
        />
      </div>
      <!--图片-->
      <div
        v-if="
          currentRes.fileType != undefined &&
            currentRes.fileType == '.png'
        "
        class="videoBox"
      >
        <img
          :src="currentRes.url"
          class="pre-img"
        >
      </div>
      <span
        v-if="courseinfo.freeModel == 0"
        class="next-box"
        @click="nextOne()"
      >
        <a>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            xmlns:xlink="http://www.w3.org/1999/xlink"
            width="16px"
            height="9px"
          >
            <path
              fill-rule="evenodd"
              fill="rgb(255, 255, 255)"
              d="M15.711,1.716 L8.716,8.711 C8.519,8.908 8.259,9.006 8.000,9.006 C7.741,9.006 7.481,8.908 7.284,8.711 L0.289,1.716 C-0.105,1.322 -0.105,0.683 0.289,0.289 C0.683,-0.105 1.322,-0.105 1.716,0.289 L8.000,6.573 L14.284,0.289 C14.678,-0.105 15.317,-0.105 15.711,0.289 C16.105,0.683 16.105,1.322 15.711,1.716 Z"
            />
          </svg>
        </a>
        <span class="line" />
      </span>
    </div>
    <div
      v-if="
        courseinfo != null &&
          courseinfo.directoryResources != null &&
          courseinfo.directoryResources.length > 0
      "
      :class="['right-box', IsCloseRight ? 'CloseRight' : '']"
    >
      <div
        v-if="courseinfo != undefined"
        class="courseinfo-box2"
      >
        <img
          v-if="courseinfo.coverUrl != null"
          :src="courseinfo.coverUrl"
        >
        <span class="name">{{ courseinfo.name }}</span>
      </div>
      <div class="directoryDiv">
        <Directory
          :directorylist="courseinfo.directoryResources"
          :record-list="record"
          :course-id="courseId"
          :resource-id="resourceId"
          :train-id="trainId"
          :free-model="courseinfo.freeModel"
          :is-show-record="false"
          :is-expire="record != null ? record.isExpire : false"
          :cloud-learn="true"
        />
      </div>
    </div>
    <img
      src="../../assets/image/zan/AI.png"
      class="AIPoP"
      @click="togglePanel"
    >
    <deekseek
      :chat-list.sync="chatList"
      :show="showDeepseek"
      @toggle="togglePanel"
    />
  </div>
</template>
<script>
import {
    getCourseInfo,
    getUserRecord,
    updateCourseRecord,
    getResourceInfo,
    getResourceUrl,
    getCourseResourceHtmlUrl,
    getUserRecordInfo,
    UpdateResUserRecord,
    getPresignedUrl,
} from "@/api/course";
import Directory from "@/components/directory"; //目录递归组件
import {
    getFileDownloadInfo,
    getFileContent,
    getBaoCloudResourceUrl,
} from "@/api/file";
import { HTML5_FMT } from "vue-moment";
import deekseek from "@/components/deepseek";
export default {
    name: "Index",
    components: {
        Directory,
        deekseek,
    },
    data() {
        return {
            chatList: [],
            showDeepseek: false,
            // userLogin:this.$store.getters.token!=null?true:false,
            currentRes: null,
            courseinfo: null,
            record: null,
            ResPrewId: {}, // 跳转预览的资源Id,
            recordInfo: {},
            // trueUrl:'',
            courseId: this.$route.query.courseId,
            resourceId: this.$route.query.id,
            learnedtime: 0,
            player: null,
            totalTime: 0, //学习时长
            timer: null, //定时器
            timer2: null, //定时器
            trainId: this.$route.query.trainId,
            trialUrl: this.$route.query.trialUrl,
            IsCloseRight: false,
            ResourceList: [], //上一个，下一个，资源的集合
            isTiming: false, //是否需要计时
            isComplete: false, //当前资源是否已学完
            isVideo: false,
            loadFileContent: false,
        };
    },
    watch: {
        $route: {
            handler: function (route) {
                this.currentRes = null;
                clearInterval(this.timer);
                clearInterval(this.timer2);
                window.removeEventListener("message", this.updateH5ResRecord);
                this.init();
            },
            deep: true,
            immediate: true,
        },
    },
    beforeDestroy() {
        clearInterval(this.timer);
        clearInterval(this.timer2);
        window.removeEventListener("message", this.updateH5ResRecord);
    },
    mounted() {
        //h5资源提交学习记录
        // window.addEventListener('message',this.updateH5ResRecord)
    },
    methods: {
        togglePanel() {
            this.showDeepseek = !this.showDeepseek;
        },
        init() {
            //每隔2分钟，向服务器提交播放时间 ，计时器要摧毁
            this.totalTime = 0;
            this.courseId = this.$route.query.courseId;
            this.resourceId = this.$route.query.id;
            this.trainId = this.$route.query.trainId;
            this.trialUrl = this.$route.query.trialUrl;
            this.currentRes = null;
            this.getkcInfo();
            window.addEventListener("message", this.updateH5ResRecord);
        },
        startTimer() {
            // if(this.timer) clearInterval(this.timer);
            // if(this.timer2) clearInterval(this.timer2);
            //每隔1分钟，向服务器提交播放时间
            this.timer = setInterval(() => {
                //this.totalTime += 120
                this.postResUserRecord();
            }, 3 * 60 * 1000);
            this.timer2 = setInterval(() => {
                this.totalTime += 1;
                this.learnedtime = this.learnedtime + 1;
                if (
                    this.currentRes &&
                    this.isTiming &&
                    !this.isComplete &&
                    this.currentRes.fileType == ".pdf" &&
                    this.learnedtime > this.currentRes.duration
                ) {
                    this.isComplete = true;
                    this.postResUserRecord();
                }
            }, 1000);
        },
        endTimer() {
            clearInterval(this.timer);
            clearInterval(this.timer2);
        },
        async getkcInfo() {
            var resUrl = "";
            this.isVideo = false;
            await getCourseInfo(this.courseId).then((res) => {
                this.courseinfo = res;
                if (this.trainId != null) {
                    this.courseinfo.freeModel = 0;
                }
            });
            if (this.$store.getters.token) {
                await this.getRecord();
            }

            await getResourceInfo(this.resourceId).then((res) => {
                this.currentRes = res;
            });

            if (this.courseinfo.freeModel == 1) {
                //如果是收费课程并且可试看,获取签名Url mp4
                await getPresignedUrl(this.trialUrl).then((res) => {
                    resUrl = res;
                });
            } else {
                resUrl = await getResourceUrl({
                    id: this.resourceId,
                    isWeChat: true,
                });
            }

            //已学完 不记录观看时长'html',
            var restype_arr = [
                "hundun-video",
                "geektime-video",
                "geektime-html",
                "ximalaya",
                "pdfh5",
                "videoh5",
            ];
            if (
                (this.courseinfo.freeModel == 0 || this.trainId != null) &&
                restype_arr.indexOf(this.currentRes.resType) == -1
            ) {
                //免费课程 培训课程 不是H5资源
                this.isTiming = true;
            } else {
                this.isTiming = false;
            }
            if (this.isTiming) {
                this.startTimer();
            }
            if (
                this.currentRes != null &&
                this.currentRes.fileType != undefined &&
                this.currentRes.fileType == ".mp4"
            ) {
                this.isVideo = true;
            }
            resUrl = resUrl.replace("http:", window.location.protocol);

            //判断是否云端资源
            if (this.currentRes.jobId == "local") {
                var fileinfo = await getFileDownloadInfo(resUrl);
                if (this.currentRes.resType == "html") {
                    resUrl = await getCourseResourceHtmlUrl(
                        fileinfo.downloadUrl
                    );
                    //获取上次学习记录详情
                    var origin =
                        window.location.protocol + "//" + window.location.host;
                    resUrl =
                        resUrl +
                        "?origin=" +
                        origin +
                        "&trainId=" +
                        (this.trainId == undefined ? "" : this.trainId) +
                        "&courseId=" +
                        this.courseId +
                        "&resourceId=" +
                        this.resourceId +
                        "&userId=" +
                        this.$store.getters.sub;

                    if (
                        this.recordInfo != undefined &&
                        this.recordInfo != null
                    ) {
                        var h5_record = await getUserRecordInfo({
                            recordId: this.recordInfo.recordId,
                            trainId: this.trainId,
                        });
                        resUrl =
                            resUrl +
                            "&progress=" +
                            this.recordInfo.lastLearnProgress + //h5_record.lastLearnProgress
                            "&detail=" +
                            encodeURIComponent(h5_record.detail);
                        //console.log(resUrl)
                    }
                } else {
                    resUrl = fileinfo.downloadUrl;
                }
            } else if (this.currentRes.jobId == "AWS") {
                resUrl = await getBaoCloudResourceUrl(resUrl);
                // resUrl=resUrl.replace('https://pbktgf0006.s3-qos.baocloud.cn/', 'https://s3-qos.baocloud.cn/pbktgf0006/')
                resUrl = resUrl.replace(
                    "https://pbktgf0006.s3-qos.baocloud.cn/",
                    "http://qcfilehz.ciep-pimp.com/pbktgf0006/"
                );
                if (this.currentRes.fileType == ".pdf") {
                    resUrl = encodeURIComponent(resUrl);
                }
            } else {
                await getPresignedUrl(resUrl).then((res) => {
                    resUrl = res;
                    if (this.currentRes.fileType == ".pdf") {
                        {
                            resUrl = encodeURIComponent(res);
                        }
                    }
                });
                let arr = [
                    "hundun-video",
                    "geektime-video",
                    "geektime-html",
                    "ximalaya",
                    "pdfh5",
                    "videoh5",
                ];
                if (arr.indexOf(this.currentRes.resType) > -1) {
                    //获取上次学习记录详情
                    resUrl = "url=" + encodeURIComponent(resUrl);
                    var origin =
                        window.location.protocol + "//" + window.location.host;
                    var sub = localStorage.getItem("USER.SUB");
                    resUrl =
                        resUrl +
                        "&origin=" +
                        origin +
                        "&trainId=" +
                        (this.trainId == undefined ? "" : this.trainId) +
                        "&courseId=" +
                        this.courseId +
                        "&resourceId=" +
                        this.resourceId +
                        "&userId=" +
                        sub +
                        "&duration=" +
                        this.currentRes.duration;

                    if (
                        this.recordInfo != undefined &&
                        this.recordInfo != null
                    ) {
                        var h5_record = await getUserRecordInfo({
                            recordId: this.recordInfo.recordId,
                            trainId: this.trainId,
                        });
                        resUrl =
                            resUrl +
                            "&progress=" +
                            this.recordInfo.lastLearnProgress + // h5_record.lastLearnProgress
                            "&detail=" +
                            encodeURIComponent(h5_record.detail);
                    }
                }
            }

            this.currentRes.url = resUrl;
            // 视频播放
            if (this.isVideo) {
                setTimeout(() => {
                    // 视频播放
                    let start = 0;
                    if (
                        this.recordInfo != null &&
                        this.recordInfo.isComplete == false
                    ) {
                        //start = this.recordInfo.lastPlayProgress;
                        if (this.learnedtime > 120)
                            start = this.learnedtime - 120;
                    }
                    let playRateList = [
                        { label: "×1" },
                        { label: "×1.25" },
                        { label: "×1.5" },
                        { label: "×2" },
                    ]; // 倍速
                    this.player = cyberplayer("myPlayer").setup({
                        width: "100%",
                        height: "100%",
                        backcolor: "#FFFFFF",
                        stretching: "uniform",
                        file: this.currentRes.url,
                        starttime: start,
                        ak: "5a8cf05266684131a0bed321d0032848",
                        playRate: true, // 默认h5播放器有倍速功能，如不需要，可以设置为false
                        // 可配置倍速值数组，格式如下所示
                        playRateConfig: this.isComplete ? playRateList : [],
                        autoStart: true,
                        repeat: false,
                        volume: 100,
                        skin: {
                            name: "bce",
                            inactive: "#FFF", // 未激活时的颜色
                            active: "#f34813", // 悬浮或激活的颜色
                        },
                        controls: "over",
                        controlbar: {
                            canDrag: true,
                            // canDrag: this.isComplete?true:false, // 进度条是否允许拖动,  已看完可以拖动
                        },
                    });
                    let that = this;

                    if (this.isTiming) {
                        this.player.onComplete(function (event) {
                            //播放完成
                            that.endTimer();
                            //console.log('onComplete:'+that.totalTime);
                            //that.totalTime = that.currentRes.duration // 资源时长
                            that.postResUserRecord();
                        });

                        // this.player.onTime(function(event){ //设置播放器播放进度变化事件监听函数
                        //     //console.log(that.totalTime);
                        // });

                        this.player.onPlay(function (event) {
                            //播放
                            //console.log("onPlay:"+that.totalTime)
                            that.endTimer();
                            that.startTimer();
                        });
                        this.player.onPause(function (event) {
                            //暂停
                            //console.log("onPause:"+that.totalTime)
                            that.endTimer();
                            that.postResUserRecord();
                        });
                        this.player.onError(function (event) {
                            //console.log(event)
                            that.endTimer();
                        });
                        this.player.onSetupError(function (event) {
                            //console.log(event)
                            that.endTimer();
                        });
                    }
                }, 450);
            }

            // 点击上一个，下一个所需资源列表
            if (
                this.courseinfo.directoryResources != null &&
                this.courseinfo.freeModel == 0
            ) {
                this.ResourceList = [];
                this.getResList(this.courseinfo.directoryResources);
                //  console.log(this.ResourceList)
            }

            // else if(this.currentRes!=null&& this.currentRes.fileType!=undefined&&this.currentRes.fileType=='.pdf'){
            //if(this.player!=null) this.player.remove();
            // setTimeout(() => { // pdf播放
            //   this.$nextTick(() => {
            //     var option = {
            //       docId: this.currentRes.url,
            //       token: 'TOKEN',
            //       host: 'BCEDOC',
            //       serverHost: 'http://doc.bj.baidubce.com',
            //       width: 800, //文档容器宽度
            //       zoom: false, //是否显示放大缩小按钮
            //       zoomStepWidth: 200,
            //       pn: 1, //定位到第几页，可选
            //       fontSize: 'big',
            //       toolbarConf: {
            //         page: true, //上下翻页箭头图标
            //         pagenum: true, //几分之几页
            //         full: false, //是否显示全屏图标,点击后全屏
            //         copy: false, //是否可以复制文档内容
            //         position: 'center', // 设置 toolbar中翻页和放大图标的位置(值有left/center)
            //       } ,//文档顶部工具条配置对象,必选
            //       //enviroment: query.enviroment
            //     };
            //     new Document('reader', option);
            //   });

            // }, 450)
            //}
        },
        async getRecord() {
            let data = { courseId: this.courseId, trainId: this.trainId };
            await getUserRecord(data).then((res) => {
                this.record = res;
                if (this.record.isExpire && !this.trialUrl) {
                    this.returnToKC();
                }
                if (this.record.id != null && this.record.isExpire == false) {
                    this.courseinfo.freeModel = 0;
                }
                if (this.record.resUserRecords != null)
                    this.recordInfo = this.record.resUserRecords.find(
                        (x) => x.courseResourceId == this.$route.query.id
                    ); // 用户观看该资源记录
                this.learnedtime =
                    this.recordInfo != undefined
                        ? this.recordInfo.resLearnDuration
                        : 0; //
                this.isComplete =
                    this.recordInfo != undefined
                        ? this.recordInfo.isComplete
                        : false;
            });
        },
        // prewCourse(id){
        //   this.$router.push({name: 'CourseInfo', query:{id: id}})
        // },
        fullscreen(url) {
            window.open(url, "_blank");
        },
        postResUserRecord(
            _time,
            _progress,
            _detail,
            _resourceId,
            _courseId,
            _trainId
        ) {
            if (_time == undefined) {
                _time = this.totalTime;
                if (_time > 55 && _time <= 59) {
                    _time = 60;
                }
                if (_time > 115 && _time <= 119) {
                    _time = 120;
                }
                if (_time > 175 && _time <= 179) {
                    _time = 180;
                }
                if (_time > 295 && _time <= 299) {
                    _time = 300;
                }
            }

            if (_resourceId == undefined) {
                _resourceId = this.resourceId;
            }
            if (_courseId == undefined) {
                _courseId = this.courseId;
            }
            if (_trainId == undefined || _trainId == "") {
                _trainId = this.trainId;
            }
            // 更新资源学习记录
            var data = {
                trainId: _trainId,
                courseId: _courseId,
                courseResourceId: _resourceId,
                isVideo: this.isVideo,
                resTotalDuration: this.currentRes.duration, //资源时长
                resLearnDuration: _time, //资源学习时长
                lastPlayProgress: 0, //视频资源播放进度
                lastLearnProgress: _progress, //H5资源学习进度
                learnDetail: _detail, //H5资源学习详情记录
            };

            if (this.isVideo) {
                //视频
                data.lastPlayProgress = Math.floor(this.player.getPosition());
            }

            UpdateResUserRecord(data);
            this.totalTime = 0;
        },
        returnToKC() {
            this.$router.push({
                name: "CourseInfo",
                query: { id: this.courseId, trainId: this.trainId },
            });
        },
        nextOne() {
            var index = this.ResourceList.findIndex(
                (x) => x.id == this.currentRes.id
            );
            //debugger
            //console.log(index)
            if (index < this.ResourceList.length - 1) {
                index++;
                this.$router.push({
                    name: "ResourceInfo",
                    query: {
                        id: this.ResourceList[index].id,
                        courseId: this.courseId,
                        trainId: this.trainId,
                    },
                });
                let data = {
                    trainId: this.trainId, //培训ID
                    courseId: this.courseId,
                    lastLearnResourceId: this.ResourceList[index].id,
                };
                updateCourseRecord(data);
            } else {
                this.$message.info("已经是最后一个资源");
            }
        },
        prevOne() {
            var index = this.ResourceList.findIndex(
                (x) => x.id == this.currentRes.id
            );

            if (index > 0) {
                index--;
                this.$router.push({
                    name: "ResourceInfo",
                    query: {
                        id: this.ResourceList[index].id,
                        courseId: this.courseId,
                        trainId: this.trainId,
                    },
                });
                let data = {
                    trainId: this.trainId, //培训ID
                    courseId: this.courseId,
                    lastLearnResourceId: this.ResourceList[index].id,
                };
                updateCourseRecord(data);
            } else {
                this.$message.info("已经是第一个资源");
            }
        },
        closeRight() {
            // 关闭右侧目录
            this.IsCloseRight = !this.IsCloseRight;
        },
        finishRes() {
            // 已完成学习
            if (this.learnedtime + this.totalTime >= this.currentRes.duration) {
                // 已学完
                this.postResUserRecord();
                this.endTimer();
                this.isComplete = true;
                //this.learnedtime = this.currentRes.duration;
            } //未学完
            else {
                this.$message.info("学习未完成，请继续学习");
            }
        },
        getResList(res) {
            //获取资源集合
            for (var i = 0; i < res.length; i++) {
                if (res[i].resources != null && res[i].resources.length > 0) {
                    this.ResourceList = this.ResourceList.concat(
                        res[i].resources
                    );
                } else {
                    this.getResList(res[i].directories);
                }
            }
        },
        updateH5ResRecord(msg) {
            if (msg.data.eName == "HtmlResLearnUpdate") {
                var _data = msg.data.data;
                this.postResUserRecord(
                    _data.learnTime,
                    _data.progress,
                    _data.detail,
                    _data.resourceId,
                    _data.courseId,
                    _data.trainId
                );
            }
        },
    },
};
</script>
<style scoped>
.AIPoP {
    position: fixed;
    bottom: 40px;
    right: 40px;
    width: 100px;
    height: 106px;
    z-index: 9999;
    cursor: pointer;
}
</style>
