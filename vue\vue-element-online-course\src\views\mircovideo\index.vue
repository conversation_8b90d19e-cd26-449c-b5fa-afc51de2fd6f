<template>
  <div>
    <div class="catlist kc-cat-list">
      <div>
        <span class="title">知识分类:</span>
        <div class="list">
          <a @click="changeCatId()" :class="{'active':null==params.KnowledgeCategoryId }">全部</a>
          <a v-for="item in cates" @click="changeCatId(item.id)" :class="{'active':item.id==params.KnowledgeCategoryId}">{{item.name}}</a>
        </div>
      </div>
      <div>
        <span class="title">作者分类:</span>
        <div class="list">
          <a @click="changeAuthor()" :class="{'active':null==params.Author}">全部</a>
          <a v-for="item in Authors" @click="changeAuthor(item)" :class="{'active':item ==params.Author}">{{item}}</a>
        </div>
      </div>
      <span class="ft" ref="ft" >
        <span @click="isShowNode=1" class="con">
            <span class="content"> {{params.Sorting=="ViewCount desc"?'最热':'最新'}} </span>
            <svg
            xmlns="http://www.w3.org/2000/svg"
            xmlns:xlink="http://www.w3.org/1999/xlink"
            width="17px" height="9px">
            <path fill-rule="evenodd"  fill="rgb(74, 75, 79)"
            d="M16.094,1.691 L9.137,8.647 C9.120,8.668 9.113,8.694 9.094,8.713 C8.897,8.910 8.638,9.005 8.380,9.002 C8.122,9.005 7.864,8.910 7.667,8.713 C7.647,8.694 7.640,8.668 7.623,8.647 L0.667,1.691 C0.279,1.303 0.279,0.674 0.667,0.287 C1.055,-0.101 1.683,-0.101 2.071,0.287 L8.380,6.595 L14.689,0.287 C15.077,-0.101 15.706,-0.101 16.094,0.287 C16.481,0.674 16.481,1.303 16.094,1.691 Z"/>
            </svg>
        </span>
        <span class="node" v-if="isShowNode" >
          <a @click="changeSort()" :class="{'cur':params.Sorting!='ViewCount desc'}">最新</a>
          <a @click="changeSort('ViewCount desc')" :class="{'cur':params.Sorting=='ViewCount desc'}">最热</a>
        </span>
      </span>
    </div>
    <div class="CourseList">
      <div class="courseitem video-item" @click="view(item)" v-for="item in videoList">
        <span class="imgpan">
          <img :src="item.thumbnailUrl" />
          <span class="bg-op06"></span>
        </span>
        <div class="infopan">
          <div class="p-div1">
            <span class="teacher-name">
              <img  v-if="item.profilePhoto==null" src="/images/user.png" class="userImg"/>
              <img  v-else :src="item.profilePhoto" class="userImg"/>
              <span>{{item.author}}</span>
            </span>
            <span class="time">{{item.duration | timeFromte2}}</span>
          </div>
          <div class="p-div">
            <span class="title"  :title="item.name">{{item.name }}</span>
            <span class="count">
              <img  src="/microVideo/icon_like_pc_n.png" />
              <span>{{item.likeCount | showNum }}</span>
            </span>
            <span class="count">
              <img src="/microVideo/icon_person_pc.png" />
              <span>{{item.viewCount | showNum}}</span>
            </span>
            <span class="count">
              <img  src="/microVideo/icon_comment_pc.png" />
              <span>{{item.commentCount | showNum}} </span>
            </span>

          </div>
        </div>
        <img class="icon_play" src="/microVideo/icon_play2_n.png" />
      </div>

      <NoContent v-if="videoList.length==0"></NoContent>
    </div>
    <el-pagination
      v-if="params.total>params.MaxResultCount"
      class="my_pagination"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="params.page"
      :page-sizes="[8, 16, 32]"
      :page-size="params.MaxResultCount"
      layout="total, sizes, prev, pager, next, jumper"
      :total="params.total"
    ></el-pagination>
</div>
</template>
<script>
import {getCategory,getAuthors,getVideoList} from '@/api/microVideo';
import NoContent from '@/components/NoContent'
export default {
  name: 'index',
  components:{
    NoContent
  },
  data() {
   return {
     isShowNode:0,
     cates:[],
     Authors: <AUTHORS>
     videoList:[],
     params: {
					Filter: '',
					KnowledgeCategoryId: null,
					Author:null,
					SkipCount: 0,
					MaxResultCount:8,
					page: 1,
					total: 0,
					Sorting: '' //ViewCount 热度
			},
   }
  },
  watch:{
    // '$route':{
    //   handler: function (val, oldVal) {
    //     this.params.Filter = this.$route.query.filter
    //     this.getCourseList()
    //   },
    //   immediate:true
    // }
  },
  beforeDestroy(){
    document.removeEventListener('click',this.clickFun)
  },
  mounted(){
    document.addEventListener('click',this.clickFun)
    getCategory().then(res=>{
      this.cates = res.items
    })
    getAuthors().then(res=>{
      this.Authors = res.items
    })
    this.getVideoList()

  },
  methods: {
    view(item){
      this.$router.push({name: 'mircovideoinfo', query:{id: item.id}})
    },
    clickFun(e){
      //debugger;
      let _this = this
      if(!!_this.$refs.ft!=undefined&& e.target!=undefined&&_this.$refs.ft.contains(e.target)) return;
      _this.isShowNode = false
    },
    // 点击类别
    changeCatId(id){
      this.params.page = 1
      this.params.KnowledgeCategoryId = id
      this.getVideoList()
    },
    changeAuthor(item){
    this.params.page = 1
      this.params.Author = item
      this.getVideoList()
    },
    //改变排序方式
    changeSort(str){
      this.isShowNode = false
      this.params.Sorting = str
      this.params.page = 1
      this.getVideoList()
    },
    handleSizeChange(val) {
      this.params.MaxResultCount = val
      this.getVideoList()
    },
    handleCurrentChange(val) {
      this.params.page = val
      this.getVideoList()
    },
    getVideoList(){
      this.params.SkipCount = (this.params.page-1)*this.params.MaxResultCount
      getVideoList(this.params).then(res=>{
        this.videoList = res.items
        this.params.total = res.totalCount
      })
    },
  }
}
</script>
<style scoped>
.agree{
    position: absolute;
    top: 30px;
    height: 32px;
    line-height: 32px;
    background: rgb(0,0,0,0.5);
    width: 90px;
    right: 0;
    color: #fff;
    border-radius: 16px 0 0 16px;
}
</style>

