<template>
    <div class="notice-cent">
        <div class="breadcrumb-box">
            <el-breadcrumb
                separator-class="el-icon-arrow-right"
                class="bread_con"
            >
                <el-breadcrumb-item :to="{ path: '/' }">
                    <i class="el-icon-location-outline" />
                </el-breadcrumb-item>
                <el-breadcrumb-item>通知公告</el-breadcrumb-item>
            </el-breadcrumb>
        </div>
        <div class="notice-box">
            <div class="title">通知公告</div>
            <div class="list">
                <div
                    class="item-news"
                    v-for="(item, index) in NoticeList"
                    @click="gotoNoticeInfo(item)"
                >
                    <span class="dot"></span>
                    <img src="/images/top.png" v-if="item.order == 1" />
                    <span class="name"
                        ><span v-if="item.order == 0"
                            >{{ index - TopNum + 1 }}. </span
                        >{{ item.title | Substr(50) }}</span
                    >
                    <span class="time">{{
                        item.publishDate | DateFromte("YYYY年MM月DD日")
                    }}</span>
                </div>
            </div>
            <NoContent v-if="NoticeList.length == 0"></NoContent>
            <el-pagination
                v-if="query.total > query.MaxResultCount"
                class="my_pagination"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="query.page"
                :page-sizes="[15, 30, 60]"
                :page-size="query.MaxResultCount"
                layout="total, sizes, prev, pager, next, jumper"
                :total="query.total"
            ></el-pagination>
        </div>
    </div>
</template>
<script>
import { getNotices } from "@/api/course";
import NoContent from "@/components/NoContent";
export default {
    name: "index",
    components: {
        NoContent,
    },
    data() {
        return {
            NoticeList: [],
            TopNum: 0, // 置顶个数
            query: {
                SkipCount: 0,
                MaxResultCount: 15,
                page: 1,
                total: 0,
                Sorting: "", //ViewCount 热度
            },
        };
    },
    created() {
        this.getList();
    },
    methods: {
        gotoNoticeInfo(item) {
            this.$router.push({ name: "NoticeInfo", query: { id: item.id } });
        },

        handleSizeChange(val) {
            this.query.MaxResultCount = val;
            this.getList();
        },
        handleCurrentChange(val) {
            this.query.page = val;
            this.getList();
        },
        getList() {
            let data = {
                SkipCount: this.query.MaxResultCount * (this.query.page - 1),
                MaxResultCount: this.query.MaxResultCount,
                Sorting: this.query.Sorting,
            };
            getNotices(data).then((res) => {
                this.NoticeList = res.items;
                this.query.total = res.totalCount;
                var arr = this.NoticeList.filter((item) => item.order == 1);
                this.TopNum = arr.length;
            });
        },
    },
};
</script>
