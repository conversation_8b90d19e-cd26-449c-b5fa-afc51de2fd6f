<template>
  <div class="profile_info_con">
    <SideMenu />
    <div class="profile_info_box">
      <div class="tabs">
        <a class="active">我的锦囊</a>
      </div>
      <div class="CourseList myVideoList">
        <div v-for="item in knowledgeList" :key="item.id" class="courseitem video-item" @click="view(item)">
          <img v-if="item.isCollection"  class="icon-shoucang" src="/images/icon-shoucang.png" />
          <span class="imgpan">
            <img :src="item.thumbnailUrl" />
            <span class="bg-op06" />
          </span>
          <div class="infopan">
            <div class="p-div1">
              <span class="teacher-name">
                <img  v-if="item.profilePhoto==null" src="/images/user.png" class="userImg"/>
                <img  v-else :src="item.profilePhoto" class="userImg"/>
                <span>{{ item.author }}</span>
              </span>
              <span class="time">{{
                item.duration | timeFromte2
                }}</span>
            </div>
            <div class="p-div">
              <span class="title" style="height: auto" :title="item.name">{{
                item.name }}</span>
              <span class="count">
                <img src="/microVideo/icon_like_pc_n.png">
                <span>{{ item.likeCount }}</span>
              </span>
              <span class="count">
                <img src="/microVideo/icon_person_pc.png">
                <span>{{ item.viewCount }} </span>
              </span>
              <span class="count">
                <img src="/microVideo/icon_comment_pc.png">
                <span>{{ item.commentCount }} </span>
              </span>
            </div>
          </div>
          <img class="icon_play" src="/microVideo/icon_play2_n.png">
        </div>
        <!-- <NoContent v-if="knowledgeList.length==0" /> -->
      </div>
      <el-pagination v-if="knowledgeListQuery.totalCount > knowledgeListQuery.MaxResultCount" class="my_pagination"
        :current-page="knowledgeListQuery.page" :page-sizes="[8, 32, 64]"
        :page-size="knowledgeListQuery.MaxResultCount" layout="total, sizes, prev, pager, next, jumper"
        :total="knowledgeListQuery.totalCount" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </div>
  </div>
</template>
<script>
  // import { collectionKnowledgeList } from "@/api/user";
  import { getVideoList } from '@/api/microVideo';
  import SideMenu from "@/layout/SideMenu2.vue";
  // import NoContent from '@/components/NoContent'
  export default {
    name: "MyCollection",
    components: {
      SideMenu,
    },
    data () {
      return {
        knowledgeList: [],
        knowledgeListQuery: {
          Filter: "",
          Sorting: "CreationTime desc",
          SkipCount: 0,
          MaxResultCount: 8,
          page: 1,
          totalCount: 0,
        },
      };
    },
    mounted () {
      if (this.$store.getters.token) {
        this.getKnowledgeList();
      } else {
        this.$store.dispatch("user/toggleloginbox", true);
      }
    },
    methods: {
      handleSizeChange (val) {
        this.knowledgeListQuery.MaxResultCount = val;
        this.getKnowledgeList();
      },
      handleCurrentChange (val) {
        this.knowledgeListQuery.page = val;
        this.getKnowledgeList();
      },
      view (item) {
        this.$router.push({ name: "mircovideoinfo", query: { id: item.id } });
      },
      getKnowledgeList () {
        this.knowledgeListQuery.SkipCount =
          (this.knowledgeListQuery.page - 1) *
          this.knowledgeListQuery.MaxResultCount;
        getVideoList(this.knowledgeListQuery).then((res) => {
          this.knowledgeList = res.items;
          this.knowledgeListQuery.totalCount = res.totalCount;
        });
      }
    },
  };
</script>
<style >

</style>
