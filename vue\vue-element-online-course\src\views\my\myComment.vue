<template>
  <div class="profile_info_con">
    <SideMenu />
    <div class="profile_info_box">
      <div class="tabs">
        <a
          :class="{active:statue==0}"
          @click="changeState(0)"
        >课程评论</a>
        <a
          :class="{active:statue==1}"
          @click="changeState(1)"
        >知识锦囊</a>
      </div>
      <div
        v-if="statue==0"
        class="tabbox"
      >
        <div class="table_default table_comment">
          <div class="th_row">
            <div class="td1">
              课程
            </div>
            <div class="td2">
              评论内容
            </div>
            <div class="td3">
              时间
            </div>
            <div class="td4">
              操作
            </div>
          </div>
          <div
            v-for="item in list"
            class="row"
          >
            <div
              class="td1 title"
              :title="item.courseName"
            >
              <span
                class="name"
                :title="item.courseName"
              >{{ item.courseName }}</span>
            </div>
            <div
              class="td2 content"
              :title="item.content"
            >
              {{ item.content }}
            </div>
            <div class="td3">
              {{ item.creationTime | DateFromte("YYYY-MM-DD HH:mm:ss") }}
            </div>
            <div class="td4">
              <a @click="handleDeleteComment(0,item.id)">删除</a>
            </div>
          </div>
        </div>
        <el-pagination
          v-if="listQuery.totalCount>listQuery.MaxResultCount"
          class="my_pagination"
          :current-page="listQuery.page"
          :page-sizes="[10, 20, 50]"
          :page-size="listQuery.MaxResultCount"
          layout="total, sizes, prev, pager, next, jumper"
          :total="listQuery.totalCount"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
      <div
        v-if="statue==1"
        class="tabbox"
      >
        <div class="table_default table_comment">
          <div class="th_row">
            <div class="td1">
              知识锦囊
            </div>
            <div class="td2">
              评论内容
            </div>
            <div class="td3">
              时间
            </div>
            <div class="td4">
              操作
            </div>
          </div>
          <div
            v-for="item in knowledgeList"
            class="row"
          >
            <div
              class="td1 title"
              :title="item.knowledgeResource.name"
            >
              <span
                class="name"
                :title="item.knowledgeResource.name"
              >{{ item.knowledgeResource.name }}</span>
            </div>
            <div
              class="td2 content"
              :title="item.content"
            >
              {{ item.content }}
            </div>
            <div class="td3">
              {{ item.creationTime | DateFromte("YYYY-MM-DD HH:mm:ss") }}
            </div>
            <div class="td4">
              <a @click="handleDeleteComment(1,item.id)">删除</a>
            </div>
          </div>
        </div>
        <el-pagination
          v-if="knowledgeListQuery.totalCount>knowledgeListQuery.MaxResultCount"
          class="my_pagination"
          :current-page="knowledgeListQuery.page"
          :page-sizes="[10, 20, 50]"
          :page-size="knowledgeListQuery.MaxResultCount"
          layout="total, sizes, prev, pager, next, jumper"
          :total="knowledgeListQuery.totalCount"
          @size-change="handleKnowledgeSizeChange"
          @current-change="handleKnowledgeCurrentChange"
        />
      </div>
    </div>
  </div>
</template>
<script>
import {myCommentList,deleteComment, knowledgeCommentList, deleteknowledgeComment} from '@/api/user';
import SideMenu from "@/layout/SideMenu.vue";
export default {
  name: 'MyComment',
  components: {
      SideMenu
  },
  data() {
   return {
    statue:0,
    list:[],
    listQuery:{
      Filter: '',
      CommentType: null,
      CourseId: null,
      ParentId: null,
      SkipCount:0,
      MaxResultCount:10,
      page:1,
      totalCount:0
    },
    knowledgeList: [],
    knowledgeListQuery: {
      Filter: '',
      KnowledgeResourceId: null,
      ParentId: null,
      Sorting: null,
      SkipCount:0,
      MaxResultCount:10,
      page:1,
      totalCount:0
    }
   }
  },
  mounted(){
    if (this.$store.getters.token) {
      this.getMyCommentList()
      this.getKnowledgeCommentList()
    } else{
      this.$store.dispatch("user/toggleloginbox", true);
    }

  },
  methods: {

    getMyCommentList(){
      this.listQuery.SkipCount = (this.listQuery.page - 1) * this.listQuery.MaxResultCount
      myCommentList(this.listQuery).then(res=>{
        this.list = res.items
        this.listQuery.totalCount = res.totalCount
      })
    },
    getKnowledgeCommentList() {
      this.knowledgeListQuery.SkipCount = (this.knowledgeListQuery.page - 1) * this.knowledgeListQuery.MaxResultCount
      knowledgeCommentList(this.knowledgeListQuery).then(res=>{
        this.knowledgeList = res.items
        this.knowledgeListQuery.totalCount = res.totalCount
      })
    },
    handleDeleteComment(t,id) {
      if(t === 0) {
        this.$confirm('是否确定删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          deleteComment(id)
            .then((res) => {
              this.$message.success('删除成功')
              this.getMyCommentList()
            })
            .catch(() => {
              this.$message.error('删除失败')
            })
        })
        .catch(() => {
          this.$message.info('已取消删除')
        })
      }else {
        this.$confirm('是否确定删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          deleteknowledgeComment(id)
            .then((res) => {
              this.$message.success('删除成功')
              this.getKnowledgeCommentList()
            })
            .catch(() => {
              this.$message.error('删除失败')
            })
        })
        .catch(() => {
          this.$message.info('已取消删除')
        })
      }
        
        
    },
    changeState(statue){
      this.statue = statue
    },
    handleSizeChange(val) {
      this.listQuery.MaxResultCount = val
      this.getMyCommentList()
    },
    handleCurrentChange(val) {
      this.listQuery.page = val
      this.getMyCommentList()
    },
    handleKnowledgeSizeChange(val) {
      this.knowledgeListQuery.MaxResultCount = val
      this.getKnowledgeCommentList()
    },
    handleKnowledgeCurrentChange(val) {
      this.knowledgeListQuery.page = val
      this.getKnowledgeCommentList()
    }
    
  }
}
</script>

