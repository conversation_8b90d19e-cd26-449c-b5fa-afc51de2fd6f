<template>
    <div class="biaozhun">
        <div class="maps">
            <img src="../../assets/image/zan/map.png" />
            <span>培训中心</span>
            <img src="../../assets/image/zan/left.png" />
            <span>名师风采</span>
        </div>
        <img class="train-head" src="../../assets/image/zan/trainteacher.png" />
        <div class="card-box" style="margin-top: 30px;">
            <div class="card-item" v-for="(item, index) in teacherList" :key="index" @click="goDetail(item.id)">
                <img class="card-img" :src="item.profilePhoto" />
                <div class="card-teacher-box">
                    <p>{{ item.introduce }}</p>
                    <div class="teacher-detail">详情</div>
                </div>
            </div>

        </div>
        <el-pagination
            v-if="parmas.totalCount > parmas.maxResultCount"
            class="my_pagination"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="parmas.page"
            :page-sizes="[10, 20, 60]"
            :page-size="parmas.MaxResultCount"
            layout="total, sizes, prev, pager, next, jumper"
            :total="parmas.total"
            ></el-pagination>
    </div>
</template>
<script>
import {
    getTeachers
} from "@/api/newadd"
export default {
    data() {
        return {
            teacherList: [], // 名师风采
            parmas:{
                Filter: "",
                Sorting: "",
                SkipCount: 0,
                MaxResultCount: 10,
                total:0,
                page:1
            },
        };
    },
    methods: {
        goDetail(id){
            const url= this.$router.resolve({
                name: "teacherTrainDetail",
                query: { id: id },
            }).href;
            window.open(url, "_blank");
            // this.$router.push({
            //     name: "teacherTrainDetail",
            //     query: { id: id },
            // });
        },
        handleSizeChange(val) {
            this.parmas.MaxResultCount = val;
            this.getList();
        },
        handleCurrentChange(val) {
            this.parmas.page = val;
            this.getList();
        },
        getList(){
            this.parmas.SkipCount=this.parmas.MaxResultCount*(this.parmas.page-1)
            getTeachers(this.parmas).then((res) => {
                this.teacherList = res.items;
                this.parmas.total = res.totalCount;
            });
        }
    },
    mounted() {
        this.getList();
    }
};
</script>
<style scoped>

</style>
