import Vue from 'vue'
import Router from 'vue-router'

Vue.use(Router)

/* Layout */
import Layout from '@/layout'

/**
 * Note: sub-menu only appear when route children.length >= 1
 * Detail see: https://panjiachen.github.io/vue-element-admin-site/guide/essentials/router-and-nav.html
 *
 * hidden: true                   if set true, item will not show in the sidebar(default is false)
 * alwaysShow: true               if set true, will always show the root menu
 *                                if not set alwaysShow, when item has more than one children route,
 *                                it will becomes nested mode, otherwise not show the root menu
 * redirect: noRedirect           if set noRedirect will no redirect in the breadcrumb
 * name:'router-name'             the name is used by <keep-alive> (must set!!!)
 * meta : {
    roles: ['admin','editor']    control the page roles (you can set multiple roles)
    title: 'title'               the name show in sidebar and breadcrumb (recommend set)
    icon: 'svg-name'/'el-icon-x' the icon show in the sidebar
    breadcrumb: false            if set false, the item will hidden in breadcrumb(default is true)
    activeMenu: '/example/list'  if set path, the sidebar will highlight the path you set
  }
 */

/**
 * constantRoutes
 * a base page that does not have permission requirements
 * all roles can be accessed
 */
export const constantRoutes = [

    {
        path: '/404',
        component: () => import('@/views/404'),
        hidden: true
    },
    // {
    //   path: '/login',
    //   component: () => import('@/views/login/MaintenancePage'), // 维护中
    //   hidden: true
    // },
    {
        path: '/login',
        component: () => import('@/views/login/index'),
        hidden: true
    },
    {
        path: '/resetPassword',
        component: () => import('@/views/login/resetPassword'),
        hidden: true
    },
    {
        path: '/client/lives',
        component: () => import('@/views/live/index2'),
        hidden: true
    },

    {
        path: '/auth-redirect',
        name: 'auth-redirect',
        component: () => import('@/views/login/redirect'),
        hidden: true
    },
    {
        path: '/mobile',
        name: 'mobile',
        hidden: true,
        component: () => import('@/views/mobile/resourceInfo'),
        meta: { title: 'ResourceInfo', icon: 'ResourceInfo', type: 'ResourceInfo' }
    },
    {
        path: '/',
        redirect: 'index',
        component: Layout,
        children: [
            {
                path: 'examEvalute',
                name: 'examEvalute',
                component: () => import('@/views/newAdd/examEvalute'),
                meta: { title: 'examEvalute', icon: 'examEvalute', type: 'examEvalute' }
            },
            {
                path:'qiye',
                name:'qiye',
                component: () => import('@/views/talent/qiye'),
                meta: { title: 'qiye', icon: 'qiye', type: 'qiye' }
            },
            {
                path:'qiyeDetail',
                name:'qiyeDetail',
                component: () => import('@/views/talent/qiyeDetail'),
                meta: { title: 'qiyeDetail', icon: 'qiyeDetail', type: 'qiye' }
            },
            {
                path:'personal',
                name:'personal',
                component: () => import('@/views/talent/personal'),
                meta: { title: 'personal', icon: 'personal', type: 'personal' }
            },
            {
                path:'personalDetail',
                name:'personalDetail',
                component: () => import('@/views/talent/personalDetail'),
                meta: { title: 'personalDetail', icon: 'personalDetail', type: 'personal' }
            },
            {
                path:'jishu',
                name:'jishu',
                component: () => import('@/views/technology/jishu'),
                meta: { title: 'jishu', icon: 'jishu', type: 'jishu' }
            },
            {
                path:'jishuDetail',
                name:'jishuDetail',
                component: () => import('@/views/technology/jishuDetail'),
                meta: { title: 'jishuDetail', icon: 'jishuDetail', type: 'jishu' }
            },
            // AI专家
            {
                path: 'AIexpert',
                name: 'AIexpert',
                component: () => import('@/views/newAdd/AIexpert'),
                meta: { title: 'AIexpert', icon: 'AIexpert', type: 'AIexpert' }
            },
            //AI工具
            {
                path: 'AItool',
                name: 'AItool',
                component: () => import('@/views/newAdd/AItool'),
                meta: { title: 'AItool', icon: 'AItool', type: 'AItool' }
            },
            // 班级资讯
            {
                path: 'biaozhun',
                name: 'biaozhun',
                component: () => import('@/views/newAdd/biaozhun'),
                meta: { title: 'biaozhun', icon: 'biaozhun', type: 'biaozhun' }
            },
            //行业资讯
            {
                path: 'hangye',
                name: 'hangye',
                component: () => import('@/views/newAdd/hangye'),
                meta: { title: 'hangye', icon: 'hangye', type: 'hangye' }
            },
            // 培训资讯
            {
                path: 'peixun',
                name: 'peixun',
                component: () => import('@/views/newAdd/peixun'),
                meta: { title: 'peixun', icon: 'peixun', type: 'peixun' }
            },
            // 课程资讯
            {
                path: 'kecheng',
                name: 'kecheng',
                component: () => import('@/views/newAdd/kecheng'),
                meta: { title: 'kecheng', icon: 'kecheng', type: 'kecheng' }
            },
            // 资讯详情
            {
                path: 'newsnoticeinfo',
                name: 'newsnoticeinfo',
                component: () => import('@/views/newAdd/newsnoticeinfo'),
                meta: { title: 'newsnoticeinfo', icon: 'newsnoticeinfo',type: 'newsnoticeinfo' }
            },
            //培训班级
            {
                path: 'classTrain',
                name: 'classTrain',
                component: () => import('@/views/newAdd/classTrain'),
                meta: { title: 'classTrain', icon: 'classTrain', type: 'classTrain' }
            },
            // 培训风采
            {
                path: 'trainTrain',
                name: 'trainTrain',
                component: () => import('@/views/newAdd/trainTrain'),
                meta: { title: 'trainTrain', icon: 'trainTrain', type: 'trainTrain' }
            },
            //培训风采详情
            {
                path: 'trainTrainDetail',
                name: 'trainTrainDetail',
                component: () => import('@/views/newAdd/trainTrainDetail'),
                meta: { title: 'trainTrainDetail', icon: 'trainTrainDetail', type: 'trainTrainDetail' }
            },
            // 名师风采
            {
                path: 'teacherTrain',
                name: 'teacherTrain',
                component: () => import('@/views/newAdd/teacherTrain'),
                meta: { title: 'teacherTrain', icon: 'teacherTrain', type: 'teacherTrain' }
            },
            {
                path: 'teacherTrainDetail',
                name: 'teacherTrainDetail',
                component: () => import('@/views/newAdd/teacherTrainDetail'),
                meta: { title: 'teacherTrainDetail', icon: 'teacherTrainDetail', type: 'teacherTrainDetail' }
            },
            {
                path: 'index',
                name: 'Index',
                component: () => import('@/views/index/index'),
                meta: { title: 'index', icon: 'index' }
            },
            {
                path: 'coursecenter',
                name: 'CourseCenter',
                component: () => import('@/views/course/index'),
                meta: { title: 'coursecenter', icon: 'coursecenter' }
            },
            {
                path: 'noticecenter',
                name: 'NoticeCenter',
                component: () => import('@/views/notice/index'),
                meta: { title: 'noticecenter', icon: 'noticecenter' }
            },
            {
                path: 'noticeinfo',
                name: 'NoticeInfo',
                component: () => import('@/views/notice/info'),
                meta: { title: 'noticeinfo', icon: 'noticeinfo' }
            },
            {
                path: 'mircovideoinfo',
                name: 'mircovideoinfo',
                component: () => import('@/views/mircovideo/info'),
                meta: { title: 'mircovideoinfo', icon: 'mircovideoinfo', type: 'ResourceInfo' }
            },
            {
                path: 'mircovideo',
                name: 'mircovideo',
                component: () => import('@/views/mircovideo/index'),
                meta: { title: 'mircovideo', icon: 'mircovideo', }
            },
            {
                path: 'livecenter',
                name: 'LiveCenter',
                component: () => import('@/views/live/index'),
                meta: { title: 'livecenter', icon: 'livecenter', type: 'live' }
            },

            {
                path: 'liveEvaluate',
                name: 'LiveEvaluate',
                component: () => import('@/views/live/evaluate'),
                meta: { title: 'liveevaluate', icon: 'liveevaluate', type: 'live' }
            },
            {
                path: 'examcenter',
                name: 'ExamCenter',
                component: () => import('@/views/exam/index'),
                meta: { title: 'examcenter', icon: 'examcenter', type: 'my' }
            },
            {
                path: 'examinfo',
                name: 'ExamInfo',
                component: () => import('@/views/exam/info'),
                meta: { title: 'examinfo', icon: 'examinfo', type: 'ResourceInfo' },
                hidden: true
            },
            {
                path: 'searchCenter',
                name: 'SearchCenter',
                component: () => import('@/views/index/search'),
                meta: { title: 'searchCenter', icon: 'searchCenter' }
            },

            {
                path: 'traincenter',
                name: 'TrainCenter',
                component: () => import('@/views/train/index'),
                meta: { title: 'traincenter', icon: 'traincenter', type: 'train' }
            },
            {
                path: 'courseinfo',
                name: 'CourseInfo',
                component: () => import('@/views/course/courseinfo'),
                meta: { title: 'CourseInfo', icon: 'CourseInfo', type: 'CourseInfo' }
            },
            {
                path: 'resourceInfo',
                name: 'ResourceInfo',
                component: () => import('@/views/course/resourceInfo'),
                meta: { title: 'ResourceInfo', icon: 'ResourceInfo', type: 'ResourceInfo' }
            },

            {
                path: 'bankList',
                name: 'BankList',
                component: () => import('@/views/exercise/bankList'),
                meta: { title: 'BankList', icon: 'BankList', type: 'exercise' }
            },
            {
                path: 'exerciseCenter',
                name: 'ExerciseCenter',
                component: () => import('@/views/exercise/index'),
                meta: { title: 'exerciseCenter', icon: 'exerciseCenter', type: 'exercise' }
            },
            {
                path: 'keysExercise',
                name: 'KeysExercise',
                component: () => import('@/views/exercise/keysExercise'),
                meta: { title: 'keysExercise', icon: 'keysExercise', type: 'exercise' }
            },
            {
                path: 'daliyExercise',
                name: 'DaliyExercise',
                component: () => import('@/views/exercise/daliyExercise'),
                meta: { title: 'dailyExercise', icon: 'dailyExercise', type: 'exercise' }
            },
            {
                path: 'selfExercise',
                name: 'SelfExercise',
                component: () => import('@/views/exercise/selfExercise'),
                meta: { title: 'selfExercise', icon: 'selfExercise', type: 'exercise' }
            },
            {
                path: 'startExercise',
                name: 'StartExercise',
                component: () => import('@/views/exercise/start'),
                meta: { title: 'startExercise', icon: 'startExercise', type: 'exercise' }
            },
            {
                path: 'totalExercise',
                name: 'TotalExercise',
                component: () => import('@/views/exercise/total'),
                meta: { title: 'totalExercise', icon: 'totalExercise', type: 'exercise' }
            },
            {
                path: 'resultExercise',
                name: 'ResultExercise',
                component: () => import('@/views/exercise/result'),
                meta: { title: 'resultExercise', icon: 'resultExercise', type: 'exercise' }
            },
            // {
            //   path: 'mobile',
            //   name: 'mobile',
            //   component: () => import('@/views/mobile/resourceInfo'),
            //   meta: { title: 'ResourceInfo', icon: 'ResourceInfo',type:'ResourceInfo' }
            // },
            {
                path: 'aboutus',
                name: 'Aboutus',
                component: () => import('@/views/index/aboutus'),
                meta: { title: 'aboutus', icon: 'aboutus' }
            },
            {
                path: 'protocol',
                name: 'protocol',
                component: () => import('@/views/index/protocol'),
                meta: { title: 'protocol', icon: 'protocol' }
            },
            {
                path: 'profile',
                name: 'profile',
                component: () => import('@/views/profile/index'),
                meta: { title: 'profile', icon: 'profile' },
                hidden: true
            },
            {
                path: 'changePassword',
                name: 'changePassword',
                component: () => import('@/views/changePassword/index'),
                meta: { title: 'changePassword', icon: 'changePassword' },
                hidden: true
            },
            {
                path: 'myCourse',
                name: 'myCourse',
                component: () => import('@/views/my/myCourse'),
                meta: { title: 'myCourse', icon: 'myCourse', type: 'my' },
                hidden: true
            },
            {
                path: 'myTiKu',
                name: 'myTiKu',
                component: () => import('@/views/my/myTiKu'),
                meta: { title: 'myTiKu', icon: 'myTiKu', type: 'my' },
                hidden: true
            },
            {
                path: 'myTraining',
                name: 'myTraining',
                component: () => import('@/views/my/myTraining'),
                meta: { title: 'myTraining', icon: 'myTraining', type: 'my' },
                hidden: true
            },
            {
                path: 'myTotal',
                name: 'myTotal',
                component: () => import('@/views/my/myTotal'),
                meta: { title: 'myTotal', icon: 'myTotal' },
                hidden: true
            },
            {
                path: 'myKnowledge',
                name: 'myKnowledge',
                component: () => import('@/views/my/myKnowledge'),
                meta: { title: 'myKnowledge', icon: 'myKnowledge', type: 'my' },
                hidden: true
            },
            {
                path: 'myLive',
                name: 'myLive',
                component: () => import('@/views/my/myLive'),
                meta: { title: 'myLive', icon: 'myLive', type: 'my' },
                hidden: true
            },
            {
                path: 'myComment',
                name: 'myComment',
                component: () => import('@/views/my/myComment'),
                meta: { title: 'myComment', icon: 'myComment' },
                hidden: true
            },
            {
                path: 'myRecord',
                name: 'myRecord',
                component: () => import('@/views/my/myRecord'),
                meta: { title: 'myRecord', icon: 'myRecord' },
                hidden: true
            },
            {
                path: 'TrainingInfo',
                name: 'TrainingInfo',
                component: () => import('@/views/my/TrainingInfo'),
                meta: { title: 'TrainingInfo', icon: 'TrainingInfo', type: 'TrainingInfo' },
                hidden: true
            }
        ]
    },


    // 404 page must be placed at the end !!!
    { path: '*', redirect: '/404', hidden: true }
]

const createRouter = () => new Router({
    // mode: 'history', // require service support
    scrollBehavior: () => ({ y: 0 }),
    routes: constantRoutes
})

const router = createRouter()

// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-357941465
export function resetRouter() {
    const newRouter = createRouter()
    router.matcher = newRouter.matcher // reset router
}

export default router
