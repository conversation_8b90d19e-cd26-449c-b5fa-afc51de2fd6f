<template>
  <div class="comment-div">
    <!--问答评论列表页-->
    <div
      v-if="pageType==0"
      class="comment-list-box"
    >
      <div class="opr-box">
        <div class="list">
          <a
            :class="{active:params.CommentType===null||params.CommentType===''}"
            @click="changelist('')"
          >全部</a>
          <a
            :class="{active:params.CommentType===0}"
            @click="changelist(0)"
          >评论</a>
          <a
            :class="{active:params.CommentType===1}"
            @click="changelist(1)"
          >问答</a>
        </div>
        <button
          v-if="cloudLearn"
          @click="showAddTypeBox()"
        >
          我要发布
        </button>
      </div>
      <div class="comment-list">
        <div
          v-for="item in commentlist"
          :class="['comment-item',item.commentType==0?'pl':'']"
          @click="info(item)"
        >
          <div class="t-div">
            <span class="user-box">
              <img
                src="/images/user.png"
                class="userImg"
              >
              <span>{{ item.author | userNameFormat }}</span>
            </span>
            <span
              v-if="item.commentType==0"
              class="type0"
            >【评论】</span>
            <span
              v-if="item.commentType==1"
              class="type1"
            >【问答】</span>
          </div>
          <div class="m-div">
            <div class="content-div">
              {{ item.content }}
            </div>
            <div
              v-if="item.commentType==1&&item.bestAnswer!=null"
              class="best-answer"
            >
              <div>最佳答案：<span class="txt">{{ item.bestAnswer.author | userNameFormat }}</span></div>
              <div class="best-answer-content">
                {{ item.bestAnswer.content }}
              </div>
            </div>
            <div
              v-if="item.commentType==1"
              class="sum"
            >
              <span class="num">{{ item.answerCount }}回复</span>
              <span class="num">{{ item.viewCount }}浏览</span>
              <span class="time">{{ item.creationTime | showTime }}</span>
            </div>
            <div
              v-if="item.commentType==0"
              class="sum"
            >
              <span class="time">{{ item.creationTime | showTime }}</span>
            </div>
          </div>
        </div>
        <div
          v-if="params.total>commentlist.length"
          class="more"
          @click="getMore()"
        >
          加载更多
        </div>
      </div>
    </div>
    <!--问答详情页-->
    <div
      v-if="pageType==1"
      class="reply-div"
    >
      <div class="root">
        <a
          style="cursor: pointer;"
          @click="initdata('')"
        >问答评论</a> <span>></span> <a
          style="cursor: pointer;"
          @click="initdata(1)"
        >问答</a> <span>></span> {{ infoItem.content | Substr(15) }}
      </div>
      <div class="comment-info">
        <div class="type-title">
          问答
        </div>
        <div class="t-div">
          <span class="user-box">
            <img
              src="/images/user.png"
              class="userImg"
            >
            <span>{{ infoItem.author | userNameFormat }}</span>
          </span>
        </div>
        <div class="m-div">
          <div class="content-div">
            {{ infoItem.content }}
          </div>
          <div>
            <button
              v-if="cloudLearn"
              class="replybtn"
              @click="showReplyBox()"
            >
              我来回答
            </button>
          </div>
          <div class="sum">
            <span class="num">{{ infoItem.answerCount }}回复</span>
            <span class="num">{{ infoItem.viewCount }}浏览</span>
            <span class="time">{{ infoItem.creationTime | showTime }}</span>
          </div>
        </div>
      </div>
      <div class="reply-list-box">
        <div
          v-for="item1 in infoItem.ReplyList"
          class="comment-info"
        >
          <img
            v-if="item1.isBestAnswer"
            src="/images/bestanswer.png"
            class="bestanswer-ico"
          >
          <div class="t-div">
            <span class="user-box">
              <img
                src="/images/user.png"
                class="userImg"
              >
              <span>{{ item1.author | userNameFormat }}</span>
            </span>
          </div>
          <div class="m-div">
            <div class="content-div">
              {{ item1.content }}
            </div>
            <button
              v-if="!item1.isBestAnswer&& cloudLearn && userSub == infoItem.creatorId"
              class="setbestanswer-btn"
              @click="setBest(item1)"
            >
              采纳为最佳回答
            </button>
            <span class="time">{{ item1.creationTime | showTime }}</span>
          </div>
        </div>
        <div
          v-if="replyparams.total>infoItem.ReplyList.length"
          class="more"
          @click="getMoreReply()"
        >
          加载更多
        </div>
      </div>
    </div>
    <!--添加问答评论-->
    <div
      v-if="pageType==2"
      class="add-comment-div"
    >
      <div class="root">
        <a
          style="cursor: pointer;"
          @click="initdata('')"
        >问答评论</a><span>></span> <a>我要发布</a><span>></span><a>{{ postparams.commentType==0?'我要评论':'我要提问' }}</a>
      </div>
      <div class="title">
        <span
          v-if="postparams.commentType==1"
          class="imgbox-type1 imgbox-type"
        ><img src="/images/type-q-1.png"></span>
        <span
          v-if="postparams.commentType==0"
          class="imgbox-type0 imgbox-type"
        ><img src="/images/type-q-2.png"></span>
        <span class="txt">{{ postparams.commentType==0?'我要评论':'我要提问' }}</span>
      </div>
      <div class="content-box">
        <textarea
          v-model.trim="postparams.content"
          maxlength="200"
        />
        <span>{{ postparams.content.length }}/200</span>
      </div>
      <button
        class="submit-comment-btn"
        :disabled="postparams.content.length==0"
        @click="submitdata()"
      >
        提交
      </button>
    </div>
    <!--回复-->
    <el-dialog
      :visible.sync="IsShowReplyBox"
      class="reply-dialog"
      :show-close="false"
      width="730px"
      height="560px"
      :modal="true"
      :close-on-click-modal="true"
      :close-on-press-escape="false"
    >
      <div class="reply-box">
        <div class="title">
          <span>我来回答</span><a
            class="close"
            @click="IsShowReplyBox = false"
          ><img src="/images/icon-close.png"></a>
        </div>
        <div class="comment-info">
          <div class="type-title">
            问答
          </div>
          <div class="t-div">
            <span class="user-box">
              <img
                src="/images/user.png"
                class="userImg"
              >
              <span>{{ infoItem.author | userNameFormat }}</span>
            </span>
          </div>
          <div class="m-div">
            <div class="content-div">
              {{ infoItem.content }}
            </div>
          </div>
        </div>
        <div class="contentbox">
          <div class="title">
            回答
          </div>
          <div class="text-box">
            <textarea
              v-model.trim="postparams.content"
              maxlength="200"
            />
            <span class="len">{{ postparams.content.length }}/200</span>
          </div>
          <button
            :disabled="postparams.content.length==0"
            @click="submitReply()"
          >
            提交
          </button>
        </div>
      </div>
    </el-dialog>

    <!--选择添加类型-->
    <el-dialog
      :visible.sync="IsShowChooseType"
      class="choose-type-dialog"
      :show-close="false"
      width="730px"
      height="560px"
      :modal="true"
      :close-on-click-modal="true"
      :close-on-press-escape="false"
    >
      <div class="choose-type-box">
        <div class="title">
          <span>我要发布</span><a
            class="close"
            @click="IsShowChooseType = false"
          ><img src="/images/icon-close.png"></a>
        </div>
        <div class="btn-list">
          <div
            class="choose-item"
            @click="chooseAddType(1)"
          >
            <span class="imgbox type1"><img src="/images/type-q-1.png"></span>
            <span class="r">
              <span class="t">我要提问</span>
              <span class="desc">将学习过程中遇到的关于课程资料、学习方法等问题，课程讲师或同学 会为你答疑解惑。</span>
            </span>
          </div>
          <div
            class="choose-item"
            @click="chooseAddType(0)"
          >
            <span class="imgbox type0"><img src="/images/type-q-2.png"></span>
            <span class="r">
              <span class="t">我要评论</span>
              <span class="desc">您可以将自己学习过程中的心得、看过的好书或是好文章等分享到这里，与大家一起共同进步。</span>
            </span>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
  import {updateCommentViewcount,setBestAnswer,deleteComment,addComment,getCommentList} from '@/api/course';
  import { mapGetters } from "vuex";
  export default {
    name:'Comment',
	props:{
		courseId:'',
		courseName:'',
		cloudLearn:false,

	},
	 computed: {
      ...mapGetters({
        userSub: "sub",
      })
    },
	watch:{
		'$store.state.user.token': {
		  handler: function (val, oldVal) {
		   this.userLogin = this.$store.getters.token!=null?true:false
		  },
		  // deep: true
		},

	},
	data() {
		return {
			userLogin:this.$store.getters.token!=null?true:false,
			//user_sub : '',
			params:{
				Filter:'',
				CourseId:this.courseId,
				CommentType:'',
				ParentId:'',
				Sorting:'',
				SkipCount:0,
				MaxResultCount:10,
				total:0,
				page:0
			},
			postparams:{
				courseId: this.courseId,
				title: "",
				content: "",
				author: "",
				courseName: this.courseName,
				commentType: 0, //类型：评论0，提问1，回答2
				parentId: ""
			},
			replyparams:{  //获取回复
				Filter:'',
				CourseId:this.courseId,
				CommentType:2,
				ParentId:'',
				Sorting:'',
				SkipCount:0,
				MaxResultCount:10,
				total:0,
				page:0
			},
			commentlist:[],
			pageType:0 , // 0 列表页，1 问答详情页，2 添加问答评论
			IsShowChooseType:false,
			infoItem:{} ,
			IsShowReplyBox:false,
			// Isdisabled:false, // 我来回答的提交按钮
		}
	},
	mounted(){
		this.getlist()
	},

	methods:{
		changelist(type){ // 筛选类型
			this.params.CommentType =  type
			this.params.page = 0
			this.commentlist = []
			this.getlist()
		},
		chooseAddType(type){ //选择添加类型
		    this.postparams.commentType = type
			this.pageType = 2  //显示 添加页面
			this.IsShowChooseType = false
			if(type==0) this.postparams.parentId =''
		},
		showAddTypeBox(){ //我要发布按钮事件
			if (!this.userLogin)  this.$store.dispatch("user/toggleloginbox", true) // 未登录
			else this.IsShowChooseType = true
		},
		//初始化数据
		initdata(type){
			this.pageType = 0 //列表页
			this.params.CommentType = type
			this.params.page = 0
			this.commentlist = []
			this.getlist()
		},
		// 进入问答详情
		info(item){
			if(item.commentType==1){
				if (!this.userLogin)  this.$store.dispatch("user/toggleloginbox", true) // 未登录
				else{
					updateCommentViewcount(item.id) // 增加浏览数
					this.pageType = 1
					this.infoItem = item
					this.infoItem.ReplyList = []
					this.replyparams.ParentId = item.id
					this.replyparams.page = 0
					this.getReplyList()
				}

			}
		},
		// 我来回答
		showReplyBox(){
			if (!this.userLogin)  this.$store.dispatch("user/toggleloginbox", true) // 未登录
			else{
				this.IsShowReplyBox = true
				this.postparams.content = ''
			}
		},
		// 提交回答
		submitReply(){
			// this.Isdisabled = true
			if(this.postparams.content.length>0){
				this.postparams.parentId = this.infoItem.id
				this.postparams.commentType = 2
				addComment(this.postparams).then(res=>{
					if(res!=null) {
						this.$message.success('回复成功')
						this.IsShowReplyBox = false
						this.Isdisabled = false
						this.postparams.content = ''  // 清空输入框
						//this.getReplyList()
						this.infoItem.ReplyList = []
						this.replyparams.ParentId = this.infoItem.id
						this.replyparams.page = 0
						this.getReplyList()
					}
				},error=>{
					this.$message.error('回复失败')
				})
			} else{
				this.$message.info('请输入回复内容')
			}

		},
		submitdata(){
			if(this.postparams.content.length>0 ){
				addComment(this.postparams).then(res=>{
					if(res) {
						this.pageType = 0 // 显示列表页
						this.postparams.content = '' // 清空输入框
						var str = this.postparams.commentType==0 ?"评论添加成功":"提问添加成功"
						this.$message.success(str)
						this.changelist(this.postparams.commentType)
					}
				},error=>{
					this.$message.error('添加失败')
				})
			} else{
				this.$message.info('请输入问答评论内容')
			}
		},
		getMore(){
			this.params.page++
			// this.params.SkipCount = this.params.page*this.params.MaxResultCount
			this.getlist()
		},
		getlist(){
			this.params.SkipCount = this.params.page*this.params.MaxResultCount
			getCommentList(this.params).then(res=>{
				this.params.total = res.totalCount
				if(res.totalCount>0) this.commentlist = this.commentlist.concat(res.items)
			})
		},

		getReplyList(){
			this.replyparams.SkipCount = this.replyparams.page*this.replyparams.MaxResultCount
			getCommentList(this.replyparams).then(res=>{
				this.replyparams.total = res.totalCount
				if(res.totalCount>0) this.infoItem.ReplyList = this.infoItem.ReplyList.concat(res.items)
				this.$forceUpdate()
			})
		},
		getMoreReply(){
			this.replyparams.page++
			this.getReplyList()
		},

		setBest(item){
			let that = this
			setBestAnswer({questionId:that.infoItem.id,answerId:item.id}).then(res=>{
				var index1 = that.infoItem.ReplyList.findIndex(x=>x.isBestAnswer)
				if(index1>=0) that.infoItem.ReplyList[index1].isBestAnswer = false
				var index = that.infoItem.ReplyList.findIndex(x=>x.id == item.id)
				if(index>=0) that.infoItem.ReplyList[index].isBestAnswer = true
				that.$forceUpdate()
				that.$message.success('已成功将此回复设为最佳答案')
			})
		}

	}
  }
</script>e
