/*
 * @Author: LZXpang <EMAIL>
 * @Date: 2022-07-28 10:04:29
 * @LastEditors: LZXpang <EMAIL>
 * @LastEditTime: 2022-08-18 16:46:02
 * @FilePath: /vue-element-online-course/src/api/live.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import axios from '@/axios'

// Filter, LiveStreamStatue,Sorting,SkipCount,MaxResultCount
export function getlives(data){
  return axios.gets('/api/lms/public/lives', data)
}
export function getlivesdetail(id){
  return axios.gets(`/api/lms/public/lives/${id}`)
}
export function getlivethemes(data){
  return axios.gets('/api/lms/public/lives/themes',data)
}
// 获取回放地址
export function getBackUrl(data){
  return axios.gets('/api/lms/public/live-users/get-live-back-url', data)
}

// 获取直播地址
export function getLiveUrl(data){
  return axios.gets('/api/lms/public/live-users/get-live-url', data)
}



