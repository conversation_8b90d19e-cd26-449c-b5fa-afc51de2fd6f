import router from '../router'
import axios from 'axios'
import qs from 'qs'
import config from '@/config'
import {
    Message,
    Loading
} from 'element-ui'
import {
    getToken
} from '@/utils/auth'

axios.defaults.timeout = 60000
axios.defaults.headers.post['Content-Type'] = 'application/x-www-form-urlencoded;charset=UTF-8;'
// axios.defaults.withCredentials = true
//TODO:配置读取
axios.defaults.headers['Accept-Language'] = "zh-Hans"
// axios.defaults.baseURL = '';
axios.defaults.baseURL = config.base.ip + config.base.backend_port
// POST传参序列化
axios.interceptors.request.use((config) => {
    // eslint-disable-next-line eqeqeq
    if (getToken() != '') {
        config.headers.Authorization = 'Bearer ' + getToken()
    } else {
        router.replace({
            path: '/'
        })
    }
    return config
}, (error) => {
    return Promise.reject(error)
})

// 返回状态判断
axios.interceptors.response.use((res) => {
    // return Promise.reject(res);
    return res
}, (err) => {
    // 404等问题可以在这里处理
    if (err.response) {
        const error = err.error = {}
        switch (err.response.status) { // 判断后台返回的token值是否失效
            case 401:
                Message({
                    message: '登录过期，请重新登录！',
                    type: 'error',
                    duration: 5 * 1000
                })
                router.replace({
                    path: '/'
                })
                break

            case 400:
                error.message = err.response.data.error.message
                error.details = err.response.data.error.details
                break

            case 403:
                error.message = err.response.data.error.message
                error.details = err.response.data.error.code
                break

            case 404:
                error.message = err.response.data.error.message || '未找到服务'
                error.details = err.response.data.error.code || '未找到服务'
                break

            case 408:
                error.message = err.response.data.error.message
                error.details = err.response.data.error.details
                break

            case 500:
                error.message = err.response.data.error.message
                error.details = err.response.data.error.message
                break

            case 501:
                error.message = err.response.data.error.message
                error.details = err.response.data.error.details
                break

            case 502:
                error.message = err.response.data.error.message
                error.details = err.response.data.error.details
                break

            case 503:
                error.message = err.response.data.error.message
                error.details = err.response.data.error.details
                break

            case 504:
                error.message = err.response.data.error.message
                error.details = err.response.data.error.details
                break

            case 505:
                error.message = err.response.data.error.message
                error.details = err.response.data.error.details
                break

            default:
        }
        return Promise.reject(err)
    } else if (err.request) {
        return Promise.reject(err.request)
    } else {
        // Something happened in setting up the request that triggered an Error
        return Promise.reject('Error', err.message)
    }
})
export default {
    fetchs(url, data, callback, signal) {
        const header = {
            "Content-Type": "application/json",
            "Authorization": 'Bearer ' + getToken(),
            "Accept": "text/event-stream"
        }
        return new Promise((resolve, reject) => {
            fetch(config.base.ip + url, {
                method: 'POST',
                headers: header,
                body: JSON.stringify(data),
                signal
            }).then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                // 获取 ReadableStream
                const reader = response.body.getReader();
                const decoder = new TextDecoder('utf-8');
                let buffer = '';

                let incompleteChunk = ''; // 用于存储不完整的数据块
                // 持续读取流数据
                function readStream() {
                    reader.read()
                        .then(({ done, value }) => {
                            if (done) {
                                // 流结束
                                // console.log('buffer' + buffer);
                                resolve(buffer);
                                return;
                            }

                            // 解码数据块
                            const chunk = decoder.decode(value, { stream: true });
                            const fullChunk = incompleteChunk + chunk;

                            // 处理 SSE 格式（data: {...}）
                            // const lines = chunk.split('\n');
                            const lines = fullChunk.split('\n');
                            // 保存最后一行，可能是不完整的
                            incompleteChunk = lines[lines.length - 1];
                            // for (const line of lines) {
                            for (let i = 0; i < lines.length - 1; i++) {
                                const line = lines[i].trim();
                                if (line.startsWith('data: ')) {
                                    const eventData = line.substring(6).trim();
                                    let cleanData = ""
                                    try {
                                        if (!eventData) continue;
                                        // 尝试处理可能的特殊字符
                                        cleanData = eventData.replace(/\r?\n|\r/g, '\\n')  // 处理换行符
                                            .replace(/\t/g, '\\t');  // 处理制表符

                                        // 验证数据是否为有效的 JSON 格式
                                        if (!cleanData.startsWith('{') && !cleanData.startsWith('[')) {
                                            // console.warn('Invalid JSON format:', cleanData);
                                            continue;
                                        }

                                        const parsedData = JSON.parse(cleanData);
                                        if (parsedData.event == 'message') {
                                            buffer += eventData;
                                            if (callback) {
                                                callback(parsedData)
                                            }
                                        }

                                        // 可以在这里处理实时数据（如 WebSocket 风格）
                                    } catch (e) {
                                        console.error('SSE Parse Error:', cleanData, e);
                                    }
                                }
                            }

                            // 继续读取
                            readStream();
                        })
                        .catch(error => {
                            reject(`SSE 读取失败: ${error.message}`);
                        });
                }
                // 开始读取流
                readStream();
            })
                .catch(error => {
                    if (error.name === 'AbortError') {
                        reject('请求已取消');
                    } else {
                        reject(`请求失败: ${error.message}`);
                    }
                });
        })

    },
    posts(url, params) {
        return new Promise((resolve, reject) => {
            axios.post(url, params)
                .then(response => {
                    resolve(response.data)
                }, err => {
                    Message({
                        message: err.error.message,
                        type: 'error',
                        duration: 5 * 1000
                    })
                    reject(err)
                })
                .catch((error) => {
                    reject(error)
                })
        })
    },
    posts2(url, params, headers, callback) {
        return new Promise((resolve, reject) => {
            axios.post(url, params, { headers: headers })
                .then(response => {
                    resolve(response.data)
                }, err => {
                    Message({
                        message: err.error.message,
                        type: 'error',
                        duration: 5 * 1000
                    })
                    reject(err)
                })
                .catch((error) => {
                    reject(error)
                })
        })
    },
    saves(url, params) { // 保存方法
        return new Promise((resolve, reject) => {
            const loading = Loading.service({
                lock: true,
                target: document.querySelector('.contentWrapper')
            })
            axios.post(url, params)
                .then(response => {
                    loading.close()
                    resolve(response.data)
                }, err => {
                    Message({
                        message: err.error.message,
                        type: 'error',
                        duration: 5 * 1000
                    })
                    reject(err)
                    loading.close()
                })
                .catch((error) => {
                    loading.close()
                    reject(error)
                })
        })
    },
    update(url, params) { // 修改方法
        return new Promise((resolve, reject) => {
            const loading = Loading.service({
                lock: true,
                target: document.querySelector('.contentWrapper')
            })
            axios.put(url, params)
                .then(response => {
                    loading.close()
                    resolve(response.data)
                }, err => {
                    Message({
                        message: err.error.message,
                        type: 'error',
                        duration: 5 * 1000
                    })
                    reject(err)
                    loading.close()
                })
                .catch((error) => {
                    loading.close()
                    reject(error)
                })
        })
    },
    view(url, params) {
        return new Promise((resolve, reject) => {
            const loading = Loading.service({
                lock: true,
                target: document.querySelector('.contentWrapper')
            })
            axios.get(url, {
                'params': params
            })
                .then(response => {
                    resolve(response.data)
                    loading.close()
                }, err => {
                    Message({
                        message: err.error.message,
                        type: 'error',
                        duration: 5 * 1000
                    })
                    reject(err)
                    loading.close()
                })
                .catch((error) => {
                    reject(error)
                    loading.close()
                })
        })
    },
    gets(url, params) {
        return new Promise((resolve, reject) => {
            axios.get(url, {
                'params': params
            })
                .then(response => {

                    resolve(response.data)
                }, err => {
                    Message({
                        message: err.error.message,
                        type: 'error',
                        duration: 5 * 1000
                    })
                    reject(err)
                })
                .catch((error) => {
                    reject(error)
                })
        })
    },
    deletes(url, params) {
        return new Promise((resolve, reject) => {
            axios.delete(url, {
                'params': params
            })
                .then(response => {
                    resolve(response.data)
                }, err => {
                    Message({
                        message: err.error.message,
                        type: 'error',
                        duration: 5 * 1000
                    })
                    reject(err)
                })
                .catch((error) => {
                    reject(error)
                })
        })
    },
    puts(url, params) {
        return new Promise((resolve, reject) => {

            axios.put(url, params)
                .then(response => {
                    resolve(response.data)
                }, err => {
                    Message({
                        message: err.error.message,
                        type: 'error',
                        duration: 5 * 1000
                    })
                    reject(err)
                })
                .catch((error) => {
                    reject(error)
                })
        })
    },
    instancePosts(url, params) { // 登录
        var instance = axios.create({
            baseURL: config.base.authip + config.base.auth_port
        })
        if (params.tenant && params.tenant.trim() != '') {
            instance.defaults.headers.post['__tenant'] = params.tenant
        } else {
            delete instance.defaults.headers.post['__tenant']
        }
        var data = qs.stringify(params)
        return new Promise((resolve, reject) => {
            instance.post(url, data)
                .then(response => {
                    resolve(response.data)
                }, err => {
                    if (err.response.status === 400) {
                        // console.log(err.response)
                        Message({
                            message: err.response.data.error_description ? err.response.data.error_description.replace('稍后', '4分钟后') : '登录失败,用户名或密码错误！',
                            type: 'error',
                            duration: 5 * 1000
                        })
                    } else {
                        Message({
                            message: err.message,
                            type: 'error',
                            duration: 5 * 1000
                        })
                    }
                    reject(err)
                })
                .catch((error) => {
                    Message({
                        message: '登录异常',
                        type: 'error',
                        duration: 5 * 1000
                    })
                    reject(error)
                })
        })
    },
    getUserInfo(url) { // 获取用户信息
        var instance = axios.create({
            baseURL: config.base.authip + config.base.auth_port
        })
        instance.defaults.headers.Authorization = 'Bearer ' + getToken()
        return new Promise((resolve, reject) => {
            instance.get(url)
                .then(response => {
                    resolve(response.data)
                }, err => {
                    Message({
                        message: err.message,
                        type: 'error',
                        duration: 5 * 1000
                    })
                    reject(err)
                })
                .catch((error) => {
                    reject(error)
                })
        })
    },
    getPermissions(url, params) {
        var instance = axios.create({
            baseURL: config.base.ip + config.base.identity_port
        })
        instance.defaults.headers.Authorization = 'Bearer ' + getToken()
        return new Promise((resolve, reject) => {
            instance.get(url, {
                'params': params
            })
                .then(response => {
                    resolve(response.data)
                }, err => {
                    Message({
                        message: err.message,
                        type: 'error',
                        duration: 5 * 1000
                    })
                    reject(err)
                })
                .catch((error) => {
                    reject(error)
                })
        })
    },
    localFileContent(url) {
        var file_server = config.base.file_ip
        url = url.replace(file_server, '')

        var instance = axios.create({
            baseURL: file_server
        })
        instance.defaults.timeout = 1000 * 60 * 5 //5分钟
        instance.defaults.headers.Authorization = 'Bearer ' + getToken()

        instance.defaults.responseType = "blob"
        return new Promise((resolve, reject) => {
            instance.get(url)
                .then(response => {
                    resolve(response)
                }, err => {
                    Message({
                        message: err.error.message || err.response.data.error.message,
                        type: 'error',
                        duration: 5 * 1000
                    })
                    reject(err)
                })
                .catch((error) => {
                    Message({
                        message: '加载异常',
                        type: 'error',
                        duration: 5 * 1000
                    })
                    reject(error)
                })
        })
    }

}
