<template>
  <div class="app-main">
    <!-- <div class="bread_box breadcrumb-box">
      <el-breadcrumb separator-class="el-icon-arrow-right" class="bread_con">
        <el-breadcrumb-item :to="{ path: '/' }">
          <i class="el-icon-location-outline" /> 首页
        </el-breadcrumb-item>
        <el-breadcrumb-item>个人中心</el-breadcrumb-item>
        <el-breadcrumb-item>我的信息</el-breadcrumb-item>
      </el-breadcrumb>
    </div> -->
    <div class="profile_info_con">
      <side-menu />
      <div class="profile_info_box">
        <div class="profile_info_title">
          <span>基本信息</span>
        </div>
        <div class="profile_info_content">
          <el-form ref="form" label-position="left" :model="form" label-width="96px" :rules="formRules">
            <el-form-item label="用户名">
              {{ username }}
            </el-form-item>
            <el-form-item label="手机号">
              {{ form != undefined ? form.phoneNumber : "" }}
              <el-button plain type="primary" round size="small" style="margin-left: 20px"
                @click="handleChangePhoneClick">
                更 换
              </el-button>
            </el-form-item>
            <el-form-item label="姓名" prop="name">
              <!-- <el-input v-model="form.name" class="infoClass" /> -->
              {{form.name}}
            </el-form-item>
            <el-form-item label="工号" prop="studentIDNumber">
              <!-- <el-input v-model="form.name" class="infoClass" /> -->
              {{form.studentIDNumber}}
            </el-form-item>
            <!-- <el-form-item label="所属学校" prop="school">
              <el-input v-model="form.school" class="infoClass" />
            </el-form-item>
            <el-form-item label="所属专业" prop="speciality">
              <el-input v-model="form.major" class="infoClass" />
            </el-form-item>
            <el-form-item label="职位" prop="position">
              <el-input v-model="form.position" class="infoClass" />
            </el-form-item>
            <el-form-item label="联系方式" prop="contact">
              <el-input v-model="form.contact" class="infoClass" />
            </el-form-item> -->
            <!-- <el-form-item label="邮箱" prop="email">
              <el-input v-model="form.email" class="infoClass" />
            </el-form-item> -->
            <el-form-item label="性别" prop="email">
              <el-radio v-model="form.surname" :label="'0'">
                男
              </el-radio>
              <el-radio v-model="form.surname" :label="'1'">
                女
              </el-radio>
            </el-form-item>
          </el-form>
          <button class="saveButton" @click="save">
            保 存
          </button>
        </div>
      </div>
    </div>
    <el-dialog :visible="changePhoneDialog" center top="120px" modal-append-to-body width="600px"
      :close-on-click-modal="false" :before-close="closeDialog">
      <div class="find_title_item">
        <div class="find_icon" />
        <span class="find_title">更换手机号</span>
      </div>
      <el-form ref="changePhoneForm" :model="changePhoneForm" label-width="100px" class="info_form form_style"
        :rules="changePhoneFormRules">
        <div v-if="step === 1">
          <el-form-item label="当前手机号" prop="currentPhoneNumber">
            <el-input v-model="changePhoneForm.currentPhoneNumber" class="input_style" placeholder="请输入手机号">
              <i slot="prefix" style="display: flex; align-items: center; height: 100%">
                <span class="svg-container">
                  <svg-icon icon-class="phone" />
                </span>
              </i>
            </el-input>
          </el-form-item>
          <el-form-item label="验证码" prop="oldCode" class="valid_form_item">
            <el-input v-model="changePhoneForm.oldCode" class="input_style" placeholder="请输入验证码">
              <i slot="prefix" style="display: flex; align-items: center; height: 100%">
                <span class="svg-container">
                  <svg-icon icon-class="code" />
                </span>
              </i>
            </el-input>

            <el-button type="primary" round style="margin-left: 20px" class="color_btn verificateCodeButton"
              :disabled="verificateCodeButtonDisabled" @click="getVerificateCode(0)">
              {{ verificateCodeButtonTitle }}
            </el-button>
          </el-form-item>
        </div>
        <div v-if="step === 2">
          <el-form-item label="新手机号" prop="phoneNumber">
            <el-input v-model="changePhoneForm.phoneNumber" class="input_style" autocomplete="on" placeholder="请输入手机号">
              <i slot="prefix" style="display: flex; align-items: center; height: 100%">
                <span class="svg-container">
                  <svg-icon icon-class="phone" />
                </span>
              </i>
            </el-input>
          </el-form-item>
          <el-form-item label="验证码" prop="code" class="valid_form_item">
            <el-input v-model="changePhoneForm.code" class="input_style" placeholder="请输入验证码">
              <i slot="prefix" style="display: flex; align-items: center; height: 100%">
                <span class="svg-container">
                  <svg-icon icon-class="code" />
                </span>
              </i>
            </el-input>

            <el-button type="primary" round style="margin-left: 20px" class="color_btn verificateCodeButton"
              :disabled="verificateCodeButtonDisabled" @click="getVerificateCode(1)">
              {{ verificateCodeButtonTitle }}
            </el-button>
          </el-form-item>
        </div>

        <div class="bottom_box">
          <el-button v-if="step === 1" class="color_btn sure_btn" round type="primary" @click="handleCheckPhoneSure">
            下一步
          </el-button>
          <el-button v-if="step === 2" class="color_btn sure_btn" round type="primary" @click="handleChangePhoneSure">
            确定
          </el-button>
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>
<script>
  import {
    getMyProfile,
    changeMyProfile,
    sendChangePhoneCode,
    checkChangePhoneCode,
    changePhone,
  } from "@/api/user";
  import { mapGetters } from "vuex";
  import SideMenu from "@/layout/SideMenu.vue";
  export default {
    name: "Profile",
    components: {
      SideMenu,
    },
    data () {
      const validatePhoneNumber = (rule, value, callback) => {
        if (value == null && value === "" && value.length === 0) {
          callback(new Error("请输入手机号"));
        } else {
          var reg_tel =
            /^(1[3-9])\d{9}$/;
          var reg_Tel = new RegExp(reg_tel);
          if (!reg_Tel.test(value)) {
            callback(new Error("请输入正确的手机号"));
          } else {
            callback();
          }
        }
      };
      const validateEmaill = (rule, value, callback) => {
        if (value != null && value != "" && value.length != 0) {
          var reg_email = /^[0-9a-zA-Z_\.-]+[@][0-9a-zA-Z_\.-]+([\.][a-zA-Z]+){1,2}$/;
          var reg_Email = new RegExp(reg_email);
          if (!reg_Email.test(value)) {
            callback(new Error("请输入正确的邮箱"));
          } else {
            callback();
          }
        } else {
          callback();
        }
      };
      const validateCode = (rule, value, callback) => {
        if (value && value.length !== 6) {
          callback(new Error("请输入正确的验证码"));
        } else {
          var numReg = /^[0-9]+$/;
          var numRe = new RegExp(numReg);
          if (!numRe.test(value)) {
            callback(new Error("请输入正确的验证码"));
          } else {
            callback();
          }
        }
      };
      return {
        form: {
          name: "",
          school: "",
          major: "",
          position: "",
          contact: "",
          email: "",
        },

        formRules: {
          name: [
            {
              required: false,
              message: "请输入名字",
              trigger: "blur",
            },
            {
              min: 1,
              max: 50,
              message: "长度在 1 到 50 个字符",
              trigger: "blur",
            },
          ],
          school: [
            {
              required: false,
              message: "请输入学校",
              trigger: "blur",
            },
            {
              min: 1,
              max: 100,
              message: "长度在 1 到 100 个字符",
              trigger: "blur",
            },
          ],
          speciality: [
            {
              required: false,
              message: "请输入专业",
              trigger: "blur",
            },
            {
              min: 1,
              max: 100,
              message: "长度在 1 到 100 个字符",
              trigger: "blur",
            },
          ],
          position: [
            {
              required: false,
              message: "请输入职位",
              trigger: "blur",
            },
            {
              min: 1,
              max: 100,
              message: "长度在 1 到 100 个字符",
              trigger: "blur",
            },
          ],
          contact: [
            {
              required: false,
              trigger: "blur",
              validator: validatePhoneNumber,
            },
          ],
          email: [
            {
              required: false,
              trigger: "blur",
              validator: validateEmaill,
            },
          ],
        },

        //验证码重新发送间隔时间
        resendVerificateCodeTime: 120,
        //发送短信按钮是否禁用
        verificateCodeButtonDisabled: false,
        //按钮标题
        verificateCodeButtonTitle: "发送验证码",
        changePhoneForm: {
          phoneNumber: "",
          code: "",
          currentPhoneNumber: "",
          oldCode: ""
        },
        step: 1,
        changePhoneDialog: false,
        changePhoneFormRules: {
          currentPhoneNumber: [
            {
              required: true,
              trigger: "blur",
              validator: validatePhoneNumber,
            }
          ],
          oldCode: [{
            required: true,
            trigger: "blur",
            validator: validateCode,
          }],
          phoneNumber: [
            {
              required: true,
              trigger: "blur",
              validator: validatePhoneNumber,
            }
          ],
          code: [{
            required: true,
            trigger: "blur",
            validator: validateCode,
          }],
        }
      };
    },
    computed: {
      ...mapGetters({
        username: "name",
      }),
    },
    created () {
      // console.log(this.$store.state.user.name)
      this.getMyProfileInfo();
    },
    // watch: {
    //    $route: {
    //      handler: function(route) {
    //        //this.redirect = route.query && route.query.redirect
    //         this.getMyProfileInfo();
    //      },
    //      immediate: true
    //    }
    // },
    methods: {
      getMyProfileInfo () {
        getMyProfile().then((res) => {
          //console.log(res);
          this.form = res;
        });
      },

      //个人信息保存修改
      save () {
        this.$refs.form.validate((valid) => {
          if (valid) {
            changeMyProfile(this.form).then((res) => {
              if (res) {
                this.$message.success("修改成功");
              }
            });
          }
        });
      },
      handleChangePhoneClick () {
        if (this.$refs.changePhoneForm) {
          this.$refs.changePhoneForm.resetFields()
        }
        this.changePhoneForm = {
          phoneNumber: "",
          code: "",
          currentPhoneNumber: "",
          oldCode: ""
        }
        this.changePhoneDialog = true
      },
      closeDialog () {
        this.step = 1;
        this.changePhoneDialog = false;
      },
      handleCheckPhoneSure () {
        this.$refs.changePhoneForm.validate((valid) => {
          if (valid) {
            var form = {
              phoneNumber: this.changePhoneForm.currentPhoneNumber,
              code: this.changePhoneForm.oldCode,
            };
            checkChangePhoneCode(form)
              .then((res) => {
                if (res) {
                  this.resendVerificateCodeTime = 0;
                  this.verificateCodeButtonTitle = "发送验证码";
                  this.verificateCodeButtonDisabled = false;
                  this.step = 2;
                } else {
                  this.$message.error("验证码错误");
                }
              })
              .catch(() => { });
          }
        });
      },
      handleChangePhoneSure () {
        this.$refs.changePhoneForm.validate((valid) => {
          if (valid) {
            changePhone(this.changePhoneForm)
              .then((res) => {
                this.$message.success("修改成功");
                this.changePhoneDialog = false;
                this.getMyProfileInfo();
              })
              .catch(() => { });
          }
        });
      },
      getVerificateCode (t) {
        if (t === 0) {
          this.$refs.changePhoneForm.validateField("currentPhoneNumber", (oldPhoneNumber) => {
            if (!oldPhoneNumber) {
              this.resendVerificateCodeTime = 120;
              this.verificateCodeButtonDisabled = true

              var form = {
                phoneNumber: this.changePhoneForm.currentPhoneNumber,
                isNewPhone: false,
              };
              sendChangePhoneCode(form)
                .then((res) => {
                  this.timer();
                  this.$message.success("验证码发送成功!");
                })
                .catch(() => {
                  this.verificateCodeButtonDisabled = false
                });
            }
          }
          );
        } else {
          this.$refs.changePhoneForm.validateField(
            "phoneNumber",
            (phoneNumber) => {
              if (!phoneNumber) {
                this.resendVerificateCodeTime = 120;
                this.timer();
                var form = {
                  phoneNumber: this.changePhoneForm.phoneNumber,
                  isNewPhone: true,
                };
                sendChangePhoneCode(form)
                  .then((res) => {
                    this.$message.success("验证码发送成功!");
                  })
                  .catch(() => { });
              }
            }
          );
        }
      },
      timer () {
        if (this.resendVerificateCodeTime > 0) {
          this.verificateCodeButtonDisabled = true;
          this.resendVerificateCodeTime--;
          this.verificateCodeButtonTitle =
            "(" + this.resendVerificateCodeTime + "s)后重新发送";
          setTimeout(this.timer, 1000);
        } else {
          this.resendVerificateCodeTime = 0;
          this.verificateCodeButtonTitle = "发送验证码";
          this.verificateCodeButtonDisabled = false;
        }
      },
    },
  };
</script>
<style lang="scss">
  $main_color: #003686;
  $secondary_color: #005dc2;

  .verificateCodeButton.is-disabled {
    background: linear-gradient(to right,
        $secondary_color,
        $main_color ) !important;
  }
</style>
