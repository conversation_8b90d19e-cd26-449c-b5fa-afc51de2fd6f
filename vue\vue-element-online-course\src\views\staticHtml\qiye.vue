<template>
    <div class="biaozhun">
        <div class="maps">
            <img src="../../assets/image/zan/map.png" />
            <span>人才中心</span>
            <img src="../../assets/image/zan/left.png" />
            <span>企业岗位</span>
        </div>
        <div class="qiye-box">
            <div class="qiye-item" v-for="item in list" :key="item.id" @click="go(item.id)">
                <div class="font1">
                    <p>{{item.title}}</p>
                    <p>{{item.money}}</p>
                </div>
                <div class="font2">
                    <p>1-3年经验</p>
                    <p>本科</p>
                </div>
                <div class="font3">
                    <p>{{ item.name }}</p>
                    <p>上海.嘉定</p>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
export default {
    data() {
        return {
            list:[
                {
                    id:1,
                    title:'汽车维修工',
                    money:'12K',
                    name:'上海汽车工业（集团）总公司'
                },
                {
                    id:2,
                    title:'汽车设计师',
                    money:'12K',
                    name:'上海汽车工业（集团）总公司'
                },
                {
                    id:3,
                    title:'汽车设计师',
                    money:'12K',
                    name:'上海汽车工业（集团）总公司'
                },
                {
                    id:4,
                    title:'汽车设计师',
                    money:'12K',
                    name:'上海汽车工业（集团）总公司'
                },
                {
                    id:5,
                    title:'汽车设计师',
                    money:'12K',
                    name:'上海汽车工业（集团）总公司'
                },
                {
                    id:6,
                    title:'汽车设计师',
                    money:'12K',
                    name:'上海汽车工业（集团）总公司'
                },
                {
                    id:7,
                    title:'汽车设计师',
                    money:'12K',
                    name:'上海汽车工业（集团）总公司'
                }
            ]
        }
    },
    methods: {
        go(id) {
            if(id===1){
                const url = this.$router.resolve({
                    name: "qiyeDetail"
                }).href;

                window.open(url, "_blank");
            }

        }
    }
}
</script>
<style scoped>
.qiye-box{
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    margin-top: 20px;
}
.qiye-item{
    box-shadow: 0 5px 10px 0 rgba(0, 0, 0, 0.10);
    border-radius: 20px;
    width: 370px;
    height: 150px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    padding: 20px;
    background: #fff;
    margin-right: 55px;
    margin-bottom: 50px;
    cursor:  pointer;
}
.font1{
    display: flex;
    justify-content: space-between;
}
.font1>p{
    margin: 0 !important;
}
.font1>p:nth-child(1){font-size:16px;color: #5d5d5d;}
.font1>p:nth-child(2){font-size:18px;color: #096dd9;}
.font2{display: flex;}
.font2>p{
    background: #ebeef2;
    color: #a9aaab;
    padding:2px 6px;
    box-sizing: border-box;
    border-radius: 3px;
    margin-right: 10px;
    font-size: 14px;
}
.font3{
    display: flex;
    justify-content: space-between;
    border-top:1px solid #e6e6e6;
}
.font3>p{
    color: #5d5d5d;
}
</style>
