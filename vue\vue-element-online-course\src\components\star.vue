<template>
  <div class="star-div">
    <div class="add-star-box">
      <div class="title">
        对课程评分
      </div>
      <div class="StarsWrap">
        <span
          v-for="(i,index) in list"
          :key="index"
          @click="clickStars(index)"
        >
          <img :src="postparams.score>index?'/images/liang.png':'/images/an.png'">
        </span>
      </div>
      <span class="txt">{{ rateScoreText }}</span>
      <button
        :disabled="!cloudLearn"
        @click="submitdata"
      >
        提交评分
      </button>
    </div>
    <div class="star-list">
      <div
        v-for="(item,index) in starlist"
        class="star-item"
      >
        <img
          src="/images/user.png"
          class="userImg"
        >
        <span class="author">{{ item.author | userNameFormat }}</span>
        <span class="star-p-list">
          <span
            v-for="(i,index) in list"
            :key="index"
          >
            <img :src="item.score>index?'/images/liang.png':'/images/an.png'">
          </span>
        </span>
        <span class="txt">{{ rateScoreDesc[item.score-1] }}</span>
        <span class="time">{{ item.creationTime | DateFromte('YYYY-MM-DD HH:mm:ss') }}</span>
      </div>
      <div
        v-if="params.total>starlist.length"
        class="more"
        @click="getMore()"
      >
        加载更多
      </div>
    </div>
  </div>
</template>
<script>
  import {addscore,getscoreList} from '@/api/course';
  export default {
    name:'Comment',
	props:{
		courseId:'',
		courseName:'',
		cloudLearn:false,
	},
	data() {
		return {
			userLogin:this.$store.getters.token!=null?true:false,
			params:{
				Filter:'',
				CourseId:this.courseId,
				Sorting:'',
				SkipCount:0,
				MaxResultCount:10,
				total:0,
				page:0
			},
			postparams:{
				courseId: this.courseId,
				courseName: this.courseName,
				score: 0,
				content: "",
				author: ""
			},
			starlist:[],
			rateScoreText: '',
			rateScoreDesc: ['非常不满意', '不满意', '一般', '比较满意', '非常满意'],
			stara:'../../static/img/details/shoucang.png',//亮星星
			list:[0,1,2,3,4],
			starb:'../../static/img/details/shouc.png',//暗星星
			xing:0,
		}
	},
	computed:{

	},
	watch:{
		'$store.state.user.token': {
		  handler: function (val, oldVal) {
		   this.userLogin = this.$store.getters.token!=null?true:false
		  },
		  // deep: true
		}
	},
	created(){
		this.getlist()
	},
	methods:{
		submitdata(){
			addscore(this.postparams).then(res=>{
				if(res) {
					this.pageType = 0 // 显示列表页
					this.postparams.content = '' // 清空输入框
					var str = "课程评分成功"
					this.$message.success(str)
					// this.changelist(this.postparams.commentType)
					this.params.page = 0
					this.params.SkipCount = 0
					this.starlist = []
					this.getlist()
				}
			},error=>{
				this.$message.error('添加失败')
			})
		},
		getMore(){
			this.params.page++
			// this.params.SkipCount = this.params.page*this.params.MaxResultCount
			this.getlist()
		},
		getlist(){
			this.params.SkipCount = this.params.page*this.params.MaxResultCount
			getscoreList(this.params).then(res=>{
				this.params.total = res.totalCount
				if(res.totalCount>0) this.starlist = this.starlist.concat(res.items)
			})
		},
		clickStars(i){
			this.rateScoreText = this.rateScoreDesc[i]
			this.postparams.score = i+1
			// console.log("点击了"+(i+1)+"颗星")
		}
	}
  }
</script>
<style>
.star-box{min-height: 600px;}
.add-star-box{ margin: 30px 50px;}
.StarsWrap{    display: inline-block;  vertical-align: middle;}
.StarsWrap img{width: 30px;}
.add-star-box .txt{margin-left: 30px;vertical-align: middle;}
.add-star-box .title{    height: 50px;
    line-height: 50px;
    font-size: 18px;
    margin-bottom: 15px;}
.add-star-box button{display: inline-block;vertical-align: middle;
    vertical-align: middle;
    height: 36px;
    line-height: 36px;
    padding: 0;
    float: right;
    border: none;
    background: #ea2413;
    color: #fff;
    width: 90px;
    border-radius: 18px;
    font-size: 14px;
    text-align: center;
    margin-right: 10px;
    outline: none;
    cursor: pointer;}
.add-star-box button:disabled{opacity: 0.6;cursor: auto;}
.star-list{    margin: 10px 50px;    border-top: 1px solid #eee;}
.star-item  .userImg{width: 42px;vertical-align: middle;margin-right: 10px;}
.star-item .star-p-list{vertical-align: middle;
    margin-left: 60px;
    margin-right: 15px;
    display: inline-block;
    margin-top: 0;}
.star-item .star-p-list img{width: 25px;}
.star-item{ margin: 50px 0;}
.star-item .txt{vertical-align: middle;}
.star-item  .author{vertical-align: middle;font-size: 14px;color: #999;display: inline-block;width: 120px;}
.star-item .time{font-size: 14px;color: #999;float: right;vertical-align: middle;    margin-top: 14px;}
.star-list .more{text-align: center;font-size: 16px;color:#ea2413 ;cursor: pointer;line-height: 60px;
    background: #f5f5f5;
    margin-top: 20px;}
</style>
