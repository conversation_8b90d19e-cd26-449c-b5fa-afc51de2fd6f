<template>
    <div class="biaozhun">
        <img class="train-head" src="../../assets/image/zan/train.png" />
        <div class="train-detail">
            <img
                v-for="(item, index) in detailList"
                :src="item.photo"
                :key="index"
            />
        </div>
    </div>
</template>
<script>
export default {
    data() {
        return {
            detailList: JSON.parse(localStorage.getItem("trainTrainDetail")),
        };
    },
    methods: {},
    mounted() {},
    created() {
    },
};
</script>
<style scoped>

</style>
