import axios from '@/axios'


export function findTenant(data) {
  return axios.gets('/api/appuser/userLookup/findTenantByUserName',{ username:data.username })
}
export function login(data) {
  return axios.instancePosts('/connect/token', data)
}
export function logout(data) {
  return data
}
export function getInfo() {
  return axios.getUserInfo('/connect/userinfo')
}
//获取个人信息
// export function getMyProfile(){
//   return axios.gets('/api/account/my-profile/extensions')
// }
export function getMyProfile() {
  return axios.gets('/api/account/my-profile')
}

// //修改个人信息
// export function changeMyProfile(data) {
//   return axios.puts('/api/account/my-profile/extensions',data)
// }

//修改个人信息
export function changeMyProfile(data) {
  return axios.puts('/api/account/my-profile',data)
}
//获取注册验证码
export function getRegisterCode(data) {
  return axios.posts('/api/account/send-register-code?phoneNumber='+ data)
}
//检验注册验证码是否正确
export function checkRegisterCode(data) {
  return axios.posts('/api/account/check-register-code', data)
}
//注册
export function register(data) {
  return axios.posts('/api/account/register', data)
}
//获取忘记密码验证码
export function getForgetCode(data) {
  return axios.posts('/api/account/send-password-reset-code', data)
}
//重置密码
export function restPassword(data) {
  return axios.posts('/api/account/reset-password', data)
}
//修改密码
export function changePassword(data) {
  return axios.posts('/api/account/my-profile/change-password', data)
}


// 我的评论
export function myCommentList(data) {
  return axios.gets('/api/cms/public/course-comment/get-my-comment', data)
}
export function deleteComment(id) {
  return axios.deletes(`/api/cms/public/course-comment/${id}`)
}

// 知识锦囊评论
export function knowledgeCommentList(data) {
  return axios.gets('/api/cms/public/knowledges/comment/get-my-comment', data)
}
export function deleteknowledgeComment(id) {
  return axios.deletes(`/api/cms/public/knowledges/comment/${id}`)
}
// 我的直播
export function myLiveList(data) {
  return axios.gets('/api/lms/public/live-users/my-lives', data)
}
// 获取直播地址
export function myLiveUrl(id) {
  return axios.gets('/api/lms/public/live-users/get-live-url?id=' + id)
}
// 获取回放地址
export function myLiveBackUrl(id) {
  return axios.gets('/api/lms/public/live-users/get-live-back-url?id=' + id)
}
// 我发起的直播
export function initiateLiveList(data){
  return axios.gets('api/lms/public/live-users/my-teach-lives', data)
}

// 知识锦囊收藏
export function collectionKnowledgeList(data){
  return axios.gets('/api/cms/public/knowledges/collection/my', data)
}




// 手机号绑定
// 发送验证码
export function bindPhoneSendCode(num){
  return axios.posts('/api/account/send-check-phone-code?phonenumber='  + num)
}
// 绑定手机号
export function bindPhoneCheck(data){
  return axios.posts('/api/account/check-phone', data)
}

// 检查工号是否存在
export function checkJobNumber(num){
  return axios.posts('/api/account/check-number?number='  + num)
}

// 密码修改 发送手机验证码
export function sendPasswordResetCode(data){
  return axios.posts('/api/account/send-password-reset-code', data)
}

// 校验手机 验证码
export function checkPasswordResetCode(data){
  return axios.posts('/api/account/check-password-reset-code', data)
}
// 用户名手机号修改密码
export function resetPassword(data){
  return axios.posts('/api/account/reset-password', data)
}


// 发送登陆验证码
export function sendLoginCode(data){
  return axios.posts('/api/account/send-login-code', data)
}
// 工号 手机号登陆
export function loginByPhone(data){
  return axios.posts('/api/account/login-by-phone', data)
}

// 发送手机号修改验证码
export function sendChangePhoneCode(data){
  return axios.posts('/api/account/send-phone-number-confirmation-code', data)
}

// 验证手机号修改验证码
export function checkChangePhoneCode(data){
  return axios.posts('/api/account/check-phone-number-confirmation-code', data)
}

// 修改手机号
export function changePhone(data){
  return axios.posts('/api/account/confirm-phone-number', data)
}

// 学习统计
export function getMyHours(){
  return axios.gets('/api/appuser/my-credit-hours')
}
// 网络课程统计
export function getSumCoursesHours(){
  return axios.gets('/api/cms/public/courses/user-record/sum-courses')
}
// 培训统计
export function geSumTrainsHours(){
  return axios.gets('/api/cms/public/courses/user-record/sum-trains')
}
// 直播课程统计
export function getSumLives(){
  return axios.gets('/api/lms/public/live-users/sum-lives')
}
//详情
export function getMySumDetail(data){
  return axios.gets('/api/appuser/my-credit-hours/detail',data)
}

