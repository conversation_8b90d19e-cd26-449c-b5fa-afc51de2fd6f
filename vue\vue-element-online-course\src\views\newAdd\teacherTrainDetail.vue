<template>
    <div class="biaozhun">
        <!-- <img class="train-head" src="../../assets/image/zan/trainteacher.png" /> -->
        <div class="maps">
            <img src="../../assets/image/zan/map.png" />
            <span>培训中心</span>
            <img src="../../assets/image/zan/left.png" />
            <span>名师风采</span>
        </div>
        <!-- <div class="train-detail"> -->
            <div class="teacher-left">
                <div class="teacher-left-title">风采简介</div>
                <div style="margin:0 auto;text-align:center" v-html="detail.detail"></div>
            </div>
            <!-- <div class="teacher-right">
                <div class="other-title">其他名师</div>
                <div class="other-teacher">
                    <div class="other-teacher-item" v-for="(item, index) in teacherList" :key="index">
                        <img class="other-img" :src="item.profilePhoto" />
                        <div class="other-info">
                            <p class="other-name">{{ item.name }}</p>
                            <p class="other-introduce">{{ item.introduce }}</p>
                        </div>
                    </div>
                </div>
            </div> -->
        <!-- </div> -->
    </div>
</template>
<script>
import {
    getTeacherDetail,
    getTeachers
} from "@/api/newadd"
export default {
    data() {
        return {
            detail:{},
            teacherList: [], // 名师风采
            teacherParmas:{
                Filter: "",
                Sorting: "",
                SkipCount: 0,
                MaxResultCount: 2,
            },
        };
    },
    methods: {},
    mounted() {
        getTeachers(this.teacherParmas).then((res) => {
            this.teacherList = res.items;
        });
    },
    created() {
        const id = this.$route.query.id;
        getTeacherDetail(id).then((res) => {
            this.detail = res;
        });
    },
};
</script>
<style scoped>
.other-teacher{}
.other-teacher-item{
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
}
.other-img{
    width: 170px;
    height: 99px;
    display: block;
    border-radius: 10px;
    object-fit: cover;
    overflow: hidden;
}
.other-info{width: 175px;}
.other-name{
    font-size: 16px;
    color: #333333;
    margin: 0;
}
.other-introduce{
    margin: 0;
    font-size: 14px;
    color: #999999;
    height: 70px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    margin-top: 10px;
    line-height: 24px;
    word-break: break-all;
    word-wrap: break-word;
    white-space: normal;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
}
.other-title{
    height: 40px;
    line-height: 40px;
    border-bottom: 1px solid #ccc;
    box-sizing: border-box;
    padding:0 20px;
    font-size: 20px;
    color: #333;
    position: relative;
}
.other-title::before{
    content: "";
    position: absolute;
    left: 5px;
    bottom: 8px;
    width: 4px;
    height: 20px;
    background-color: #096DD9;
}
.train-head {
    display: block;
    height: 290px;
    width: 100%;
    object-fit: cover;
}
.train-detail{
    width: 100%;
    margin-top: 30px;
    display: flex;
    justify-content:space-between;
}
.teacher-left{
    width:100%;
    background: #fff;
    border-radius: 10px;
    box-sizing: border-box;
    padding: 0 20px;
    overflow: hidden;
}
.teacher-left-title{
    height: 50px;
    line-height: 40px;
    border-bottom: 1px solid #ccc;
    box-sizing: border-box;
    padding:0 20px;
    font-size: 18px;
    color: #096DD9;
    position: relative;
    margin-top: 15px;
    overflow: hidden;
}
.teacher-left-title::before{
    content: "";
    position: absolute;
    left: 0px;
    bottom: 0;
    width: 110px;
    height: 4px;
    background-color: #096DD9;
}
.teacher-right{
    width:400px;
    height: 330px;
    background: #fff;
    border-radius: 10px;
    box-sizing: border-box;
    padding: 10px 20px;
}
</style>
