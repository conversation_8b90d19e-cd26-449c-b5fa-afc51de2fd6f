<template>
    <div class="layout-box">
        <!-- <div class="web-title-div">职教云立方虚拟仿真实训平台</div> -->
        <div v-if="head_type != 'ResourceInfo'" class="head_blank" />
        <div v-if="head_type != 'ResourceInfo'" class="header">
            <div class="h_container">
                <div class="fixleft">
                    <el-row>
                        <router-link :to="{ name: 'Index' }">
                            <!-- <img class="logoClass" src="/images/image_new/logo.png" v-if="head_type!='competition'" /> -->
                            <img class="logoClass" src="/images/logo.png" />
                            <!-- 云立方在线学习平台 -->
                        </router-link>
                    </el-row>
                </div>
                <div class="menulist">
                    <router-link :to="{ name: 'Index' }">
                        首页
                        <span class="span0" />
                    </router-link>
                    <router-link
                        :to="{ name: 'CourseCenter' }"
                        :class="[
                            head_type == 'CourseInfo'
                                ? 'router-link-exact-active'
                                : '',
                        ]"
                    >
                        课程中心
                        <span class="span0" />
                    </router-link>
                    <a
                        :class="[
                            head_type == 'live'
                                ? 'router-link-exact-active'
                                : '',
                        ]"
                        @click="gotoLiveCenter"
                    >
                        直播中心
                        <span class="span0" />
                    </a>

                    <a
                        class="menu-item"
                        :class="[
                            head_type == 'classTrain' ||
                            head_type == 'trainTrain' ||
                            head_type == 'teacherTrain' ||
                            head_type == 'TrainingInfo' ||
                            head_type == 'teacherTrainDetail'
                                ? 'router-link-exact-active'
                                : '',
                        ]"
                        @mouseover="showSubmenu('training')"
                        @mouseleave="hideSubmenu('training')"
                    >
                        培训中心
                        <img
                            src="../assets/image/zan/head-down.png"
                            style="vertical-align: middle"
                        />
                        <span class="span0" />
                        <div
                            class="submenu"
                            v-show="activeSubmenu === 'training'"
                        >
                            <router-link
                                v-for="item in menuItems.training"
                                :key="item.name"
                                :to="{ name: item.path }"
                                :class="[
                                    head_type == item.type ||
                                    (item.type == 'classTrain' &&
                                        head_type == 'TrainingInfo') ||
                                    (item.type == 'teacherTrain' &&
                                        head_type == 'teacherTrainDetail')
                                        ? 'router-link-exact-active'
                                        : '',
                                ]"
                                class="submenu-item"
                            >
                                {{ item.name }}
                            </router-link>
                        </div>
                    </a>
                    <a
                        class="menu-item"
                        :class="[
                            head_type == 'exercise' || head_type == 'examEvalute'
                                ? 'router-link-exact-active'
                                : '',
                        ]"
                        @mouseover="showSubmenu('exam')"
                        @mouseleave="hideSubmenu('exam')"
                    >
                        考试中心
                        <img
                            src="../assets/image/zan/head-down.png"
                            style="vertical-align: middle"
                        />
                        <span class="span0" />
                        <div class="submenu" v-show="activeSubmenu === 'exam'">
                            <router-link
                                v-for="item in menuItems.exam"
                                :key="item.name"
                                :to="{ name: item.path }"
                                :class="[
                                    head_type == item.type
                                        ? 'router-link-exact-active'
                                        : '',
                                ]"
                                class="submenu-item"
                            >
                                {{ item.name }}
                            </router-link>
                        </div>
                    </a>
                    <a
                        class="menu-item"
                        :class="[
                            head_type == 'AIexpert'|| head_type == 'AItool'
                                ? 'router-link-exact-active'
                                : '',
                        ]"
                        @mouseover="showSubmenu('ai')"
                        @mouseleave="hideSubmenu('ai')"
                    >
                        AI中心
                        <img
                            src="../assets/image/zan/head-down.png"
                            style="vertical-align: middle"
                        />
                        <span class="span0" />
                        <div class="submenu" v-show="activeSubmenu === 'ai'">
                            <template v-for="item in menuItems.ai">
                                <router-link
                                    v-if="item.path !== '/'"
                                    :to="{ name: item.path }"
                                    :class="[
                                        head_type == item.type
                                            ? 'router-link-exact-active'
                                            : '',
                                    ]"
                                    class="submenu-item"
                                >
                                    {{ item.name }}
                                </router-link>
                                <div
                                    v-else
                                    :class="[
                                        head_type == item.type
                                            ? 'router-link-exact-active'
                                            : '',
                                    ]"
                                    class="submenu-item disabled-item"
                                >
                                    {{ item.name }}
                                </div>
                            </template>
                        </div>
                    </a>
                    <a
                        class="menu-item"
                        @mouseover="showSubmenu('personal')"
                        @mouseleave="hideSubmenu('personal')"
                    >
                        人才中心
                        <img
                            src="../assets/image/zan/head-down.png"
                            style="vertical-align: middle"
                        />
                        <span class="span0" />
                        <div class="submenu" v-show="activeSubmenu === 'personal'">
                            <div
                                v-for="item in menuItems.personal"
                                    :key="item.name"
                                    :class="[
                                        head_type == item.type
                                            ? 'router-link-exact-active'
                                            : '',
                                    ]"
                                    class="submenu-item"
                                >
                                    {{ item.name }}
                            </div>
                        </div>
                    </a>
                    <a
                        class="menu-item"
                        :class="[
                            head_type == 'biaozhun' ||
                            head_type == 'hangye' ||
                            head_type == 'peixun' ||
                            head_type == 'kecheng' ||
                            head_type == 'newsnoticeinfo'
                                ? 'router-link-exact-active'
                                : '',
                        ]"
                        @mouseover="showSubmenu('news')"
                        @mouseleave="hideSubmenu('news')"
                    >
                        资讯中心

                        <img
                            src="../assets/image/zan/head-down.png"
                            style="vertical-align: middle"
                        />
                        <span class="span0" />
                        <div class="submenu" v-show="activeSubmenu === 'news'">
                            <router-link
                                v-for="item in menuItems.news"
                                :key="item.name"
                                :to="{ name: item.path}"
                                :class="[
                                    head_type == item.type||
                                    (item.type == 'biaozhun' &&
                                        head_type == 'newsnoticeinfo'&& $route.query.mapname == '0') ||
                                    (item.type == 'hangye' &&
                                        head_type == 'newsnoticeinfo'&& $route.query.mapname == '1')||
                                    (item.type == 'peixun' &&
                                        head_type == 'newsnoticeinfo'&& $route.query.mapname == '2')||
                                    (item.type == 'kecheng' &&
                                        head_type == 'newsnoticeinfo'&& $route.query.mapname == '3')
                                        ? 'router-link-exact-active'
                                        : '',
                                ]"
                                class="submenu-item"
                            >
                                {{ item.name }}
                            </router-link>
                        </div>
                    </a>

                    <!-- <router-link
                        :to="{ name: 'mircovideo' }"
                        :class="[
                            head_type == 'MircoVideo'
                                ? 'router-link-exact-active'
                                : '',
                        ]"
                    >
                        知识锦囊
                        <span class="span0" />
                    </router-link> -->
                    <!-- <router-link
                        :to="{ name: 'BankList' }"
                        :class="[
                            head_type == 'exercise'
                                ? 'router-link-exact-active'
                                : '',
                        ]"
                    >
                        荟智题库
                        <span class="span0" />
                    </router-link> -->
                    <!-- <a
                        :href="
                            virtualTrainings != undefined &&
                            virtualTrainings[0] != undefined &&
                            virtualTrainings[0].url != undefined
                                ? virtualTrainings[0].url
                                : ''
                        "
                        target="_blank"
                    >
                        仿真实训
                        <span class="span0" />
                    </a> -->
                    <!-- <a
                        :class="[
                            head_type == 'my' ? 'router-link-exact-active' : '',
                        ]"
                        @click="gotoUsercenter"
                    >
                        我的学习
                        <span class="span0" />
                    </a> -->
                </div>
                <div class="fixright">
                    <!-- <div class="search_box">
                        <input v-model="searchVal" placeholder="输入关键字" />
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            xmlns:xlink="http://www.w3.org/1999/xlink"
                            width="16px"
                            height="16px"
                            @click="search()"
                        >
                            <path
                                fill-rule="evenodd"
                                fill="rgb(2, 75, 154)"
                                d="M15.797,15.267 L15.301,15.780 C15.028,16.064 14.587,16.068 14.318,15.788 L11.970,13.491 C10.719,14.426 9.182,15.000 7.500,15.000 C3.358,15.000 0.000,11.642 0.000,7.500 C0.000,3.358 3.358,-0.000 7.500,-0.000 C11.642,-0.000 15.000,3.358 15.000,7.500 C15.000,9.186 14.424,10.725 13.485,11.977 L15.805,14.247 C16.075,14.527 16.071,14.983 15.797,15.267 ZM7.500,2.000 C4.462,2.000 2.000,4.462 2.000,7.500 C2.000,10.538 4.462,13.000 7.500,13.000 C8.921,13.000 10.203,12.447 11.179,11.562 L11.611,11.115 C11.614,11.112 11.618,11.111 11.621,11.108 C12.469,10.140 13.000,8.888 13.000,7.500 C13.000,4.462 10.538,2.000 7.500,2.000 Z"
                            />
                        </svg>
                    </div> -->
                    <!-- <div class="notice-info-con" @click="gotoNoticeCenter()">
                        <img src="/images/icon-tongzhi.png" />
                    </div> -->
                    <div class="user_box">
                        <el-dropdown class="" @command="handleCommand">
                            <span class="el-dropdown-link">
                                <img src="/images/user.png" class="userImg" />
                                <span class="uifo">
                                    <span> {{ username | hidePhoneNum }}</span>
                                    <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        xmlns:xlink="http://www.w3.org/1999/xlink"
                                        width="17px"
                                        height="9px"
                                    >
                                        <path
                                            fill-rule="evenodd"
                                            fill="rgb(74, 75, 79)"
                                            d="M16.094,1.691 L9.137,8.647 C9.120,8.668 9.113,8.694 9.094,8.713 C8.897,8.910 8.638,9.005 8.380,9.002 C8.122,9.005 7.864,8.910 7.667,8.713 C7.647,8.694 7.640,8.668 7.623,8.647 L0.667,1.691 C0.279,1.303 0.279,0.674 0.667,0.287 C1.055,-0.101 1.683,-0.101 2.071,0.287 L8.380,6.596 L14.689,0.287 C15.077,-0.101 15.706,-0.101 16.094,0.287 C16.481,0.674 16.481,1.303 16.094,1.691 Z"
                                        />
                                    </svg>
                                </span>
                            </span>
                            <el-dropdown-menu @command="handleCommand">
                                <el-dropdown-item :command="1" class="dropdown">
                                    我的学习
                                </el-dropdown-item>
                                <el-dropdown-item :command="0" class="dropdown">
                                    个人中心
                                </el-dropdown-item>
                                <el-dropdown-item
                                    v-if="getRole()"
                                    :command="2"
                                    class="dropdown"
                                >
                                    管理入口
                                </el-dropdown-item>
                                <el-dropdown-item :command="4">
                                    退 出
                                </el-dropdown-item>
                            </el-dropdown-menu>
                        </el-dropdown>
                    </div>
                </div>
            </div>
        </div>
        <app-main />

        <div v-if="head_type != 'ResourceInfo'" class="footer_div">
            <div
                class="footer_bg"
                style="background: #001c2c; position: relative"
            >
                <img src="../assets/image/zan/jingge.png" class="jingge" />
                <div class="jingge-title">
                    <p>创新— —让教育更智慧</p>
                    <p>Innovation—Makes education more intelligent</p>
                </div>
                <div class="info_footer" style="height: 50px;line-height: 50px;background-color: #001c2c;text-align: left;margin-top: 10px;color: #fff;font-size: 14px;">
                    沪ICP备11039732号-1 | 沪公网安备 31011502003384号
                    <!-- <span class="qrcode"
                        >在线学习小程序：
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            xmlns:xlink="http://www.w3.org/1999/xlink"
                            width="32px"
                            height="32px"
                            style="position: absolute; top: -4px"
                            @mouseover="isShowCode = true"
                        >
                            <path
                                fill-rule="evenodd"
                                fill="rgb(0, 0, 0)"
                                d="M15.974,0.108 C7.211,0.108 0.108,7.211 0.108,15.973 C0.108,24.736 7.211,31.838 15.974,31.838 C24.735,31.838 31.838,24.736 31.838,15.973 C31.838,7.211 24.735,0.108 15.974,0.108 L15.974,0.108 ZM23.928,14.368 C23.310,15.359 22.345,16.085 21.222,16.405 C20.977,16.485 20.721,16.528 20.463,16.532 C19.968,16.529 19.567,16.126 19.567,15.631 C19.567,15.135 19.968,14.733 20.463,14.730 C20.532,14.731 20.600,14.718 20.663,14.692 C21.385,14.512 22.007,14.056 22.396,13.422 C22.622,13.053 22.740,12.628 22.738,12.194 C22.738,10.770 21.422,9.596 19.816,9.596 C19.260,9.597 18.714,9.740 18.229,10.011 C17.416,10.440 16.897,11.275 16.875,12.194 L16.875,19.762 C16.859,21.323 15.996,22.752 14.622,23.493 C13.867,23.918 13.015,24.136 12.150,24.128 C9.535,24.128 7.390,22.161 7.390,19.727 C7.395,18.964 7.607,18.216 8.003,17.563 C8.621,16.572 9.586,15.846 10.709,15.526 C10.955,15.447 11.210,15.404 11.468,15.399 C11.791,15.397 12.090,15.569 12.253,15.848 C12.415,16.128 12.415,16.473 12.253,16.752 C12.090,17.032 11.791,17.203 11.468,17.201 C11.399,17.200 11.331,17.213 11.268,17.239 C10.552,17.432 9.935,17.885 9.535,18.509 C9.309,18.878 9.191,19.303 9.193,19.737 C9.193,21.161 10.510,22.335 12.134,22.335 C12.690,22.334 13.236,22.191 13.721,21.920 C14.533,21.490 15.050,20.655 15.072,19.737 L15.072,12.204 C15.090,10.642 15.954,9.212 17.328,8.469 C18.069,8.025 18.918,7.795 19.781,7.803 C22.396,7.803 24.541,9.770 24.541,12.204 C24.536,12.967 24.324,13.715 23.928,14.368 L23.928,14.368 Z"
                            /></svg>
                    </span> -->
                    <!-- <div v-if="isShowCode" ref="showcode" class="box">
                        <span>扫一扫，进入在线学习小程序</span>
                        <img src="/images/qrCode.png" style="width: 170px" />
                        <span class="jue"></span>
                    </div> -->

                </div>
            </div>
        </div>
        <!-- <div
            v-if="!JSON.parse(passwordValid) || !JSON.parse(phoneValid)"
            :class="{ mask_shadow: true, shadow_hidden: shadowTime === 0 }"
        >
            <div
                v-if="!JSON.parse(passwordValid)"
                class="tip_box"
                @click="handleTipClick(0)"
            >
                为了您的账号安全，请立即修改您的密码
            </div>
            <div
                v-if="JSON.parse(passwordValid) && !JSON.parse(phoneValid)"
                class="tip_box"
                @click="handleTipClick(1)"
            >
                为了您的账号安全，请立即绑定手机号
            </div>
        </div> -->

        <!-- <el-dialog
            :visible="showChangePasswordDialog"
            center
            :show-close="false"
            top="5vh"
            width="660px"
        >
            <div class="find_title_item">
                <div class="find_icon" />
                <span class="find_title">修改密码</span>
            </div>
            <div class="tip_title">为了您的账号安全，请立即修改您的密码</div>
            <el-form
                ref="changeForm"
                :model="changeForm"
                label-width="80px"
                class="info_form password_form form_style"
                :rules="changeFormRules"
            >
                <el-form-item
                    label="原密码"
                    prop="currentPassword"
                >
                    <el-input
                    v-model="changeForm.currentPassword"
                    class="input_style"
                    show-password
                    autocomplete="on"
                    placeholder="请输入密码"
                    >
                    <i
                        slot="prefix"
                        style="display: flex; align-items: center; height: 100%"
                    >
                        <span class="svg-container">
                        <svg-icon icon-class="password" />
                        </span>
                    </i>
                    </el-input>
                </el-form-item>
                <el-form-item label="用户名" prop="userName">
                    <el-input
                        v-model="changeForm.userName"
                        class="input_style"
                        autocomplete="on"
                        placeholder="请输入用户名"
                    >
                        <i
                            slot="prefix"
                            style="
                                display: flex;
                                align-items: center;
                                height: 100%;
                            "
                        >
                            <span class="svg-container">
                                <svg-icon icon-class="user" />
                            </span>
                        </i>
                    </el-input>
                </el-form-item>
                <el-form-item label="手机号" prop="phoneNumber">
                    <el-input
                        v-model="changeForm.phoneNumber"
                        class="input_style"
                        autocomplete="on"
                        placeholder="请输入手机号"
                    >
                        <i
                            slot="prefix"
                            style="
                                display: flex;
                                align-items: center;
                                height: 100%;
                            "
                        >
                            <span class="svg-container">
                                <svg-icon icon-class="phone" />
                            </span>
                        </i>
                    </el-input>
                </el-form-item>
                <el-form-item
                    label="验证码"
                    prop="resetToken"
                    class="valid_form_item"
                >
                    <el-input
                        v-model="changeForm.resetToken"
                        class="input_style"
                        placeholder="请输入验证码"
                    >
                        <i
                            slot="prefix"
                            style="
                                display: flex;
                                align-items: center;
                                height: 100%;
                            "
                        >
                            <span class="svg-container">
                                <svg-icon icon-class="code" />
                            </span>
                        </i>
                    </el-input>

                    <el-button
                        type="primary"
                        round
                        style="margin-left: 20px"
                        class="color_btn verificateCodeButton"
                        :disabled="verificateCodeButtonDisabled"
                        @click="getChangePwdVerificateCode()"
                    >
                        {{ verificateCodeButtonTitle }}
                    </el-button>
                </el-form-item>
                <el-form-item label="新密码" prop="password">
                    <el-input
                        v-model="changeForm.password"
                        class="input_style"
                        show-password
                        autocomplete="on"
                        placeholder="请输入密码"
                    >
                        <i
                            slot="prefix"
                            style="
                                display: flex;
                                align-items: center;
                                height: 100%;
                            "
                        >
                            <span class="svg-container">
                                <svg-icon icon-class="password" />
                            </span>
                        </i>
                    </el-input>
                </el-form-item>
                <el-form-item label="确认密码" prop="againPassword">
                    <el-input
                        v-model="changeForm.againPassword"
                        class="input_style"
                        show-password
                        autocomplete="on"
                        placeholder="请再次输入密码"
                    >
                        <i
                            slot="prefix"
                            style="
                                display: flex;
                                align-items: center;
                                height: 100%;
                            "
                        >
                            <span class="svg-container">
                                <svg-icon icon-class="password" />
                            </span>
                        </i>
                    </el-input>
                </el-form-item>
                <el-form-item>
                    <span
                        >注：请将密码设置为8-20位，且由大写字母、小写字母、符号、数字组成</span
                    >
                </el-form-item>
                <div class="bottom_box">
                    <el-button
                        class="color_btn sure_btn"
                        round
                        type="primary"
                        @click="handleChangePasswordSure"
                    >
                        确 定
                    </el-button>
                </div>
            </el-form>
        </el-dialog> -->
        <!-- <el-dialog
            :visible="showBindPhoneDialog"
            center
            :show-close="false"
            top="120px"
            modal-append-to-body
            width="600px"
        >
            <div class="find_title_item">
                <div class="find_icon" />
                <span class="find_title">绑定手机号</span>
            </div>
            <div class="tip_title">为了您的账号安全，请立即绑定手机号</div>
            <el-form
                ref="bindPhoneForm"
                :model="bindPhoneForm"
                label-width="100px"
                class="info_form form_style"
                :rules="bindPhoneFormRules"
            >
                <el-form-item label="绑定手机号" prop="phonenumber">
                    <el-input
                        v-model="bindPhoneForm.phonenumber"
                        class="input_style"
                        autocomplete="on"
                        placeholder="请输入手机号"
                    >
                        <i
                            slot="prefix"
                            style="
                                display: flex;
                                align-items: center;
                                height: 100%;
                            "
                        >
                            <span class="svg-container">
                                <svg-icon icon-class="phone" />
                            </span>
                        </i>
                    </el-input>
                </el-form-item>
                <el-form-item
                    label="验证码"
                    prop="code"
                    class="valid_form_item"
                >
                    <el-input
                        v-model="bindPhoneForm.code"
                        class="input_style"
                        placeholder="请输入验证码"
                    >
                        <i
                            slot="prefix"
                            style="
                                display: flex;
                                align-items: center;
                                height: 100%;
                            "
                        >
                            <span class="svg-container">
                                <svg-icon icon-class="code" />
                            </span>
                        </i>
                    </el-input>

                    <el-button
                        type="primary"
                        round
                        style="margin-left: 20px"
                        class="color_btn verificateCodeButton"
                        :disabled="verificateCodeButtonDisabled"
                        @click="getVerificateCode()"
                    >
                        {{ verificateCodeButtonTitle }}
                    </el-button>
                </el-form-item>
                <div class="bottom_box">
                    <el-button
                        class="color_btn sure_btn"
                        round
                        type="primary"
                        @click="handleBindPhoneSure"
                    >
                        确 定
                    </el-button>
                </div>
            </el-form>
        </el-dialog> -->
        <!-- <el-dialog
            :visible.sync="noticeflag"
            class="notice-dialog"
            :show-close="false"
            width="1100px"
            style="text-align: center;"
        >
            <div class="h_title">
                <img class="icon-trumpet" src="/images/trumpet.png" />
                <div class="title">
                    <img src="/images/notice_title.png" />
                </div>
                <a class="close" @click="closeNotice()">&times;</a>
            </div>
            <div class="news-con">
                <div class="notice_title">
                    {{ NoticeInfo.title }}
                </div>
                <div class="con" v-html="NoticeInfo.content" />
                <div class="opr">
                    <a @click="gotoNoticeCenter()">查看全部通知</a>
                </div>
            </div>
        </el-dialog> -->
    </div>
</template>

<script>
//import { Navbar, Sidebar, AppMain } from './components'
//import ResizeMixin from './mixin/ResizeHandler'
import {
    bindPhoneSendCode,
    bindPhoneCheck,
    changePassword,
    sendPasswordResetCode,
    checkPasswordResetCode,
    resetPassword,
} from "@/api/user";
import AppMain from "./components/AppMain";
import SideMenu from "@/layout/SideMenu.vue";
import config from "@/config";
import { mapGetters } from "vuex";
import { getToken } from "@/utils/auth";
import { getNotices, getvirtualTrainings, getNoticeInfo } from "@/api/course";

export default {
    name: "Layout",
    components: {
        AppMain,
        SideMenu,
    },
    filters: {
        hidePhoneNum(tel) {
            var reg = /^(\d{3})\d{4}(\d{4})$/;
            return tel.replace(reg, "$1****$2");
        },
    },
    data() {
        const validatePassword = (rule, value, callback) => {
            var Regx = new RegExp(
                /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=(.*\W)).{8,}$/
            );
            if (
                value.length > 7 &&
                value.length < 21 &&
                Regx.test(this.changeForm.password)
            ) {
                callback();
            } else {
                callback(
                    new Error(
                        "请输入不少于8位,不超过20位且包含大写字母、小写字母、符号、数字的密码"
                    )
                );
            }
        };
        const validatePassword2 = (rule, value, callback) => {
            if (this.changeForm.againPassword !== this.changeForm.password) {
                callback(new Error("两次密码不一致"));
            } else {
                callback();
            }
        };
        const validatePhoneNumber = (rule, value, callback) => {
            if (value && value.length === 0) {
                callback(new Error("请输入手机号"));
            } else if (value == null && value == "" && value.length == 0) {
                callback(new Error("请输入手机号"));
            } else {
                var reg_tel = /^(1[3-9])\d{9}$/;
                var reg_Tel = new RegExp(reg_tel);
                if (!reg_Tel.test(value)) {
                    callback(new Error("请输入正确的手机号"));
                } else {
                    callback();
                }
            }
        };
        const validateCode = (rule, value, callback) => {
            if (value.length != 6) {
                callback(new Error("请输入正确的验证码"));
            } else {
                var numReg = /^[0-9]+$/;
                var numRe = new RegExp(numReg);
                if (!numRe.test(value)) {
                    callback(new Error("请输入正确的验证码"));
                } else {
                    callback();
                }
            }
        };

        return {
            activeSubmenu: "", // 当前激活的子菜单
            menuItems: {
                training: [
                    {
                        name: "培训班级",
                        path: "classTrain",
                        type: "classTrain",
                    },
                    {
                        name: "培训风采",
                        path: "trainTrain",
                        type: "trainTrain",
                    },
                    {
                        name: "名师风采",
                        path: "teacherTrain",
                        type: "teacherTrain",
                    },
                ],
                exam: [
                    { name: "考前练习", path: "BankList", type: "exercise" },
                    {
                        name: "考试评价",
                        path: "examEvalute",
                        type: "examEvalute",
                    },
                ],
                news: [
                    { name: "标准解读", path: "biaozhun", type: "biaozhun" },
                    { name: "行业资讯", path: "hangye", type: "hangye" },
                    { name: "培训资讯", path: "peixun", type: "peixun" },
                    { name: "课程资讯", path: "kecheng", type: "kecheng" },
                ],
                ai:[
                    {name: "AI专家",path:"AIexpert",type:"AIexpert"},
                    {name:"AI工具",path:"AItool",type:"AItool"},
                ],
                personal:[
                    {name: "人才简历",path:"/",type:"personal"},
                    {name:"企业岗位",path:"/",type:"qiye"},
                ]
            },
            // loginflag: false,
            isShowCode: false, //是否显示小程序二维码
            searchVal: this.$route.query.filter,
            Typelist: [],
            NoticeInfo: {}, //公告
            virtualTrainings: [], // 虚拟实训
            head_type: "",

            //验证码重新发送间隔时间
            resendVerificateCodeTime: 120,
            //发送短信按钮是否禁用
            verificateCodeButtonDisabled: false,
            //按钮标题
            verificateCodeButtonTitle: "发送验证码",

            menuDrawer: false,
            showView: 0,
            //角标数

            adminIp:
                config.adminIp +
                "/#/auth-redirect?params=train&token=" +
                getToken(),

            changeForm: {
                userName: "",
                phoneNumber: "",
                resetToken: "",
                resetTokenId: "",
                password: "",
                currentPassword: "",
                newPassword: "",
                againPassword: "",
            },
            changeFormRules: {
                // currentPassword: [
                //   { required: true, message: '请输入原密码', trigger: 'blur' },
                // ],
                userName: [
                    {
                        required: true,
                        message: "请输入用户名",
                        trigger: "blur",
                    },
                ],
                phoneNumber: [
                    {
                        required: true,
                        trigger: "blur",
                        validator: validatePhoneNumber,
                    },
                ],
                resetToken: [
                    {
                        required: true,
                        trigger: "blur",
                        validator: validateCode,
                    },
                ],
                password: [
                    {
                        required: true,
                        trigger: "blur",
                        validator: validatePassword,
                    },
                ],
                againPassword: [
                    {
                        required: true,
                        trigger: "blur",
                        validator: validatePassword2,
                    },
                ],
            },
            bindPhoneForm: {
                phonenumber: "",
                code: "",
            },
            bindPhoneFormRules: {
                phonenumber: [
                    {
                        required: true,
                        trigger: "blur",
                        validator: validatePhoneNumber,
                    },
                ],
                code: [
                    {
                        required: true,
                        trigger: "blur",
                        validator: validateCode,
                    },
                ],
            },
            showChangePasswordDialog: false,
            showBindPhoneDialog: false,
            shadowTime: 0,
            showTimer: null,
        };
    },
    watch: {
        $route: {
            handler: function (route) {
                if (route.meta.type != undefined)
                    this.head_type = route.meta.type;
                else this.head_type = "";
            },
            immediate: true,
        },
    },
    computed: {
        ...mapGetters({
            username: "name",
            loginflag: "loginflag",
            passwordValid: "passwordValid",
            phoneValid: "phoneValid",
            // noticeflag: "noticeflag",
            //  badgeNum: "badgeNum"
        }),
    },
    beforeDestroy() {
        document.removeEventListener("click", this.clickFun1);
    },
    mounted() {},
    created() {
        // console.log('store存储数据',JSON.parse(this.passwordValid),JSON.parse(this.phoneValid) )
        if (!JSON.parse(this.passwordValid) || !JSON.parse(this.phoneValid)) {
            this.shadowTime = 1.5;
            this.shadowTimer();
        }
        document.addEventListener("click", this.clickFun1);
        if (this.$store.getters.token) this.$store.dispatch("user/getInfo");
        if (this.head_type != "ResourceInfo") {
            // 公告
            // if (this.noticeflag) {
            //     getNotices({
            //         SkipCount: 0,
            //         MaxResultCount: 1,
            //     }).then((res) => {
            //         if (res.items.length > 0) {
            //             getNoticeInfo({ id: res.items[0].id }).then((res) => {
            //                 this.NoticeInfo = res;
            //             });
            //         } else {
            //             this.closeNotice();
            //         }
            //     });
            // }
            // 虚拟实训
            getvirtualTrainings().then((res) => {
                this.virtualTrainings = res.items;
            });
        }
    },
    methods: {
        showSubmenu(type) {
            this.activeSubmenu = type;
        },
        hideSubmenu(type) {
            this.activeSubmenu = "";
        },
        showNotice() {
            this.$store.dispatch("user/togglenoticebox", true);
        },
        closeNotice() {
            this.$store.dispatch("user/togglenoticebox", false);
        },
        // gotoNoticeInfo(item){
        //   this.closeNotice()
        //   this.$router.push({ name: 'NoticeInfo', query: { id: item.id } })
        // },
        gotoNoticeCenter() {
            this.closeNotice();
            this.$router.push({ name: "NoticeCenter" });
        },
        getRole() {
            if (this.$store.getters.role) {
                if (typeof this.$store.getters.role == "object") {
                    return this.$store.getters.role.find(
                        (item) => item === "admin"
                    );
                }
                if (typeof this.$store.getters.role == "string") {
                    return this.$store.getters.role.indexOf("admin") > -1;
                }
            }
        },
        clickFun1(e) {
            //debugger;
            let _this = this;
            if (_this.$refs.showcode == undefined) return;
            if (
                !!_this.$refs.showcode != undefined &&
                e.target != undefined &&
                _this.$refs.showcode.contains(e.target)
            )
                return;
            _this.isShowCode = false;
        },
        gotoUsercenter() {
            if (this.$store.getters.token) this.$router.push("ExamCenter");
            else this.showloginbox();
        },
        gotoLiveCenter() {
            if (this.$store.getters.token) this.$router.push("livecenter");
            else this.showloginbox();
        },
        gotoExamCenter() {
            if (this.$store.getters.token) this.$router.push("examcenter");
            else this.showloginbox();
        },
        showPwd() {
            this.$nextTick(() => {
                this.$refs.password.focus();
            });
        },
        showloginbox() {
            this.$store.dispatch("user/toggleloginbox", true);
        },
        closeloginbox() {
            this.$store.dispatch("user/toggleloginbox", false);
        },

        handleCommand(t) {
            switch (t) {
                case 0: {
                    this.$router.push("myTotal");
                    break;
                }
                case 1: {
                    this.$router.push("myTraining");
                    break;
                }
                case 2: {
                    window.open(this.adminIp, "_blank");
                    break;
                }
                // case 3: {
                //   this.$router.push({ name: "Apply", query: { opr: "submited" } });
                //   break;
                // }
                case 4: {
                    this.$store.dispatch("user/resetToken");
                    this.$router.push(
                        `/login?redirect=${this.$route.fullPath}`
                    );
                    // this.$router.push("index");
                    break;
                }
            }
        },
        getVerificateCode() {
            this.$refs.bindPhoneForm.validateField(
                "phonenumber",
                (phonenumber) => {
                    if (!phonenumber) {
                        this.resendVerificateCodeTime = 120;
                        this.verificateCodeButtonDisabled = true;
                        bindPhoneSendCode(this.bindPhoneForm.phonenumber)
                            .then((res) => {
                                this.$message.success("验证码发送成功!");
                                this.timer();
                            })
                            .catch(() => {
                                this.verificateCodeButtonDisabled = false;
                            });
                    }
                }
            );
        },
        logout() {
            this.$store.dispatch("user/resetToken");
            this.$router.push("index");
        },
        handleTipClick(t) {
            this.shadowTime = 0;
            clearTimeout(this.showTimer);
            if (t === 0) {
                this.showChangePasswordDialog = true;
            } else {
                this.showBindPhoneDialog = true;
            }
        },
        shadowTimer() {
            if (this.shadowTime > 0) {
                this.showMask = true;
                this.shadowTime -= 0.5;
                this.showTimer = setTimeout(() => {
                    this.shadowTimer();
                }, 500);
            } else {
                if (!JSON.parse(this.passwordValid)) {
                    // this.showChangePasswordDialog = true
                    //  console.log('22')
                    this.$router.push({
                        path: "changePassword",
                    });
                    return;
                } else if (!JSON.parse(this.phoneValid)) {
                    this.showBindPhoneDialog = true;
                }
                clearTimeout(this.showTimer);
            }
        },
        timer() {
            if (this.resendVerificateCodeTime > 0) {
                this.verificateCodeButtonDisabled = true;
                this.resendVerificateCodeTime--;
                this.verificateCodeButtonTitle =
                    "(" + this.resendVerificateCodeTime + "s)后重新发送";
                setTimeout(this.timer, 1000);
            } else {
                this.resendVerificateCodeTime = 0;
                this.verificateCodeButtonTitle = "发送验证码";
                this.verificateCodeButtonDisabled = false;
            }
        },
        search() {
            this.$router.push({
                name: "SearchCenter",
                query: {
                    filter: this.searchVal,
                },
            });
        },
        resetForm(t) {},
        getChangePwdVerificateCode() {
            this.$refs.changeForm.validateField(
                "phoneNumber",
                (phoneNumber) => {
                    if (!phoneNumber) {
                        this.resendVerificateCodeTime = 120;
                        this.verificateCodeButtonDisabled = true;
                        // console.log(this.changeForm);
                        var data = {
                            phone: this.changeForm.phoneNumber,
                            appName: "Web",
                            username: this.changeForm.userName,
                            returnUrl: "",
                            returnUrlHash: "",
                        };

                        sendPasswordResetCode(data)
                            .then((res) => {
                                this.timer();
                                this.changeForm.resetTokenId = res;
                                this.$message.success("发送成功");
                            })
                            .catch(() => {
                                this.verificateCodeButtonDisabled = false;
                            });
                    }
                }
            );
        },
        handleChangePasswordSure() {
            this.$refs.changeForm.validate(async (valid) => {
                if (valid) {
                    if (!this.changeForm.resetTokenId) {
                        this.$message.error("验证码未发送");
                        return;
                    }
                    var resetCodeCheck = await checkPasswordResetCode({
                        token: this.changeForm.resetToken,
                        phoneNumber: this.changeForm.phoneNumber,
                        id: this.changeForm.resetTokenId,
                    });

                    if (!resetCodeCheck) {
                        this.$message.error("验证码错误");
                        return;
                    } else {
                        resetPassword(this.changeForm)
                            .then(() => {
                                this.$message.success("修改成功");
                                this.$store.dispatch(
                                    "user/passwordValid",
                                    true
                                );
                                this.showChangePasswordDialog = false;
                                if (!JSON.parse(this.phoneValid)) {
                                    this.shadowTime = 1.5;
                                    this.shadowTimer();
                                }
                            })
                            .catch(() => {
                                this.$message.error("修改失败");
                            });
                    }

                    // changePassword(this.changeForm).then(res => {
                    //   this.$store.dispatch('user/passwordValid', true)
                    //   this.showChangePasswordDialog = false
                    //   if (!JSON.parse(this.phoneValid)) {
                    //     this.shadowTime = 1.5
                    //     this.shadowTimer()
                    //   }
                    //   this.$message.success("修改成功");
                    // });
                } else {
                    return false;
                }
            });
        },
        handleBindPhoneSure() {
            bindPhoneCheck(this.bindPhoneForm).then((res) => {
                this.$store.dispatch("user/phoneValid", true);
                this.showBindPhoneDialog = false;
                this.$message.success("绑定成功");
            });
        },
    },
};
</script>
<style scoped lang="scss">
.jingge {
    position: absolute;
    right: 0px;
    top: 33px;
    z-index: 99;
}
.jingge-title > p {
    color: #fff;
}
.jingge-title > p:nth-child(1) {
    font-size: 24px;
}
.jingge-title > p:nth-child(2) {
    font-size: 11px;
    margin-top: 5px;
}
.menu-item {
    position: relative;
    cursor: pointer;

    .submenu {
        position: absolute;
        top: 70px;
        left: 0;
        min-width: 120px;
        background: #fff;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        border-radius: 4px;
        padding: 10px 0;
        z-index: 100;
        &::before {
            content: "";
            position: absolute;
            top: -6px;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 0;
            border-left: 6px solid transparent;
            border-right: 6px solid transparent;
            border-bottom: 6px solid #fff;
        }
        .submenu-item {
            display: block;
            color: #606266;
            text-decoration: none;
            text-align: center;
            height: 40px;
            line-height: 40px;
            font-size: 16px;
            font-weight: normal;

            &:hover {
                background-color: #f5f7fa;
                color: #003686;
            }
            &.router-link-exact-active {
                color: #003686;
            }
        }
    }
}
</style>
<style lang="scss" scoped>
$main_color: #003686;
$secondary_color: #005dc2;
$el-color: #606266;
$border_color: #dcdfe6;
$main_border_color: #003686;

.form_style {
    .el-form-item.is-error .el-input__inner {
        border-color: $border_color !important;
    }

    .el-form-item.is-error .el-input__inner:focus,
    .el-form-item.is-error .el-input__inner:focus-within {
        border-color: $main_border_color !important;
    }

    .el-form-item__error {
        padding-top: 8px;
    }

    .valid_form_item .el-form-item__content {
        display: flex;
    }

    .el-form-item {
        margin-bottom: 36px;
    }

    .input_style:focus-within {
        border-color: $main_border_color !important;

        .svg-icon {
            color: $main_color;
        }
    }
}

.svg-container {
    font-size: 20px;
    vertical-align: middle;
    color: rgb(115, 116, 138);
    margin: 0 5px;
}

.input_style .el-input__inner {
    padding-left: 40px;
}

.info_form {
    margin: 40px auto;
    width: 570px;
    height: 220px;
}

.password_form {
    height: 430px;
}

.color_btn {
    background: linear-gradient(to right, #005dc2, #003686);
    border: none;
    color: white;
}

.color_btn:hover,
.color_btn:focus {
    color: white;
    background: linear-gradient(to right, #005dc2, #003686);
}

/* .sure_btn {
  width: 100%;
  margin-top: 40px;
} */

.valid_form_item .el-form-item__content {
    display: flex;
}

.find_title_item {
    display: flex;
    justify-content: center;
    align-self: center;
    margin-bottom: 40px;
}

.find_icon {
    height: 20px;
    width: 5px;
    background-color: #003686;
}

.find_title {
    margin-left: 12px;
    font-size: 18px;
    font-weight: 600;
}

.tip_title {
    text-align: center;
}

.bottom_box {
    background: rgb(235, 238, 242);
    position: absolute;
    bottom: 0px;
    width: 100%;
    left: 0;
    display: flex;
    height: 60px;
    justify-content: center;
    align-items: center;
}

.verificateCodeButton.is-disabled {
    background: linear-gradient(
        to right,
        $secondary_color,
        $main_color
    ) !important;
}

.mask_shadow {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    justify-content: center;
    background-color: rgba(0, 0, 0, 0.2);
    z-index: 9999;
}

.shadow_hidden {
    display: none;
}

.tip_box {
    margin-top: 300px;
    border-radius: 10px;
    background-color: rgba(0, 0, 0, 0.5);
    width: 500px;
    height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    cursor: pointer;
}
</style>
