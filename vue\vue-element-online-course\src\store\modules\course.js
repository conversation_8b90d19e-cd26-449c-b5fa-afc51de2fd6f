
// import { getCartList } from '@/api/vms'
const getDefaultState = () => {
  return {
    currentRes:{}
  }
}

const state = getDefaultState()

const mutations = {
  SET_CURRENTRES: (state, currentRes) => {
    state.currentRes = currentRes
  },
}
const actions = {
  setCurrentRes({commit},currentRes){
    commit("SET_CURRENTRES",currentRes)
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}

