<template>
  <div class="app-main">
    <!-- <div class="bread_box breadcrumb-box">
      <el-breadcrumb separator-class="el-icon-arrow-right" class="bread_con">
        <el-breadcrumb-item :to="{ path: '/' }">
          <i class="el-icon-location-outline" /> 首页
        </el-breadcrumb-item>
        <el-breadcrumb-item>个人中心</el-breadcrumb-item>
        <el-breadcrumb-item>我的信息</el-breadcrumb-item>
      </el-breadcrumb>
    </div> -->

    <div class="profile_info_con">
      <side-menu />

      <div class="profile_info_box">
        <div class="profile_info_title">
          <span>基本信息</span>
        </div>
        <div class="profile_info_content">
          <el-form
            ref="changeForm"
            :model="changeForm"
            label-width="80px"
            class="password_form form_style"
            :rules="changeFormRules"
          >
            <!-- <el-form-item label="原密码" prop="currentPassword">
              <el-input
                v-model="changeForm.currentPassword"
                class="input_style"
                show-password
                autocomplete="on"
                placeholder="请输入密码"
              >
                <i
                  slot="prefix"
                  style="display: flex; align-items: center; height: 100%"
                >
                  <span class="svg-container">
                    <svg-icon icon-class="password" />
                  </span>
                </i>
              </el-input>
            </el-form-item> -->
            <el-form-item
              label="用户名"
              prop="userName"
            >
              <el-input
                v-model="changeForm.userName"
                class="input_style"
                autocomplete="on"
                placeholder="请输入用户名"
              >
                <i
                  slot="prefix"
                  style="display: flex; align-items: center; height: 100%"
                >
                  <span class="svg-container">
                    <svg-icon icon-class="user" />
                  </span>
                </i>
              </el-input>
            </el-form-item>
            <el-form-item
              label="手机号"
              prop="phoneNumber"
            >
              <el-input
                v-model="changeForm.phoneNumber"
                class="input_style"
                autocomplete="on"
                placeholder="请输入手机号"
              >
                <i
                  slot="prefix"
                  style="display: flex; align-items: center; height: 100%"
                >
                  <span class="svg-container">
                    <svg-icon icon-class="phone" />
                  </span>
                </i>
              </el-input>
            </el-form-item>
            <el-form-item
              label="验证码"
              prop="resetToken"
              class="valid_form_item"
            >
              <el-input
                v-model="changeForm.resetToken"
                class="input_style"
                placeholder="请输入验证码"
              >
                <i
                  slot="prefix"
                  style="display: flex; align-items: center; height: 100%"
                >
                  <span class="svg-container">
                    <svg-icon icon-class="code" />
                  </span>
                </i>
              </el-input>

              <el-button
                type="primary"
                round
                style="margin-left: 20px"
                class="color_btn verificateCodeButton"
                :disabled="verificateCodeButtonDisabled"
                @click="getChangePwdVerificateCode()"
              >
                {{ verificateCodeButtonTitle }}
              </el-button>
            </el-form-item>
            <el-form-item
              label="新密码"
              prop="password"
            >
              <el-input
                v-model="changeForm.password"
                class="input_style"
                show-password
                autocomplete="on"
                placeholder="请输入密码"
              >
                <i
                  slot="prefix"
                  style="display: flex; align-items: center; height: 100%"
                >
                  <span class="svg-container">
                    <svg-icon icon-class="password" />
                  </span>
                </i>
              </el-input>
            </el-form-item>
            <el-form-item
              label="确认密码"
              prop="againPassword"
            >
              <el-input
                v-model="changeForm.againPassword"
                class="input_style"
                show-password
                autocomplete="on"
                placeholder="请再次输入密码"
              >
                <i
                  slot="prefix"
                  style="display: flex; align-items: center; height: 100%"
                >
                  <span class="svg-container">
                    <svg-icon icon-class="password" />
                  </span>
                </i>
              </el-input>
            </el-form-item>
            <el-form-item>
              <span>注：请将密码设置为8-20位，且由大写字母、小写字母、符号、数字组成</span>
            </el-form-item>
            <el-form-item>
              <el-button
                class="color_btn sure_btn"
                round
                type="primary"
                @click="handleChangePasswordSure"
              >
                确 定
              </el-button>
            </el-form-item>
          </el-form>
          <!-- <el-form
            ref="passwordForm"
            label-position="left"
            :model="passwordForm"
            :rules="passwordRules"
            label-width="100px"
          >
            <el-form-item
              label="原密码"
              prop="currentPassword"
            >
              <el-input
                v-model="passwordForm.currentPassword"
                class="infoClass"
              />
            </el-form-item>
            <el-form-item
              label="新密码"
              prop="newPassword"
            >
              <el-input
                v-model="passwordForm.newPassword"
                class="infoClass"
              />
            </el-form-item>
            <el-form-item
              label="确认密码"
              prop="againPassword"
            >
              <el-input
                v-model="passwordForm.againPassword"
                class="infoClass"
              />
            </el-form-item>
            <button
              class="saveButton"
              @click="changePasswordhandle"
            >
              保 存
            </button>
          </el-form> -->
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import {
  getMyProfile,
  changeMyProfile,
  changePassword,
  sendPasswordResetCode,
  checkPasswordResetCode,
  resetPassword,
} from "@/api/user";
import { mapGetters } from "vuex";
import SideMenu from "@/layout/SideMenu.vue";
export default {
  name: "ChangePassword",
  components: {
    SideMenu,
  },
  data() {
    const validatePassword = (rule, value, callback) => {
      var Regx = new RegExp(
        /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=(.*\W)).{8,}$/
      );
      if (
        value.length > 7 &&
        value.length < 21 &&
        Regx.test(this.changeForm.password)
      ) {
        callback();
      } else {
        callback(
          new Error(
            "请输入不少于8位,不超过20位且包含大写字母、小写字母、符号、数字的密码"
          )
        );
      }
    };
    const validatePassword2 = (rule, value, callback) => {
      if (this.changeForm.againPassword !== this.changeForm.password) {
        callback(new Error("两次密码不一致"));
      } else {
        callback();
      }
    };
    const validatePhoneNumber = (rule, value, callback) => {
      if (value && value.length === 0) {
        callback(new Error("请输入手机号"));
      } else if (value == null && value == "" && value.length == 0) {
        callback(new Error("请输入手机号"));
      } else {
        var reg_tel = /^(1[3-9])\d{9}$/;
        var reg_Tel = new RegExp(reg_tel);
        if (!reg_Tel.test(value)) {
          callback(new Error("请输入正确的手机号"));
        } else {
          callback();
        }
      }
    };

    const validateCode = (rule, value, callback) => {
      if (value.length != 6) {
        callback(new Error("请输入正确的验证码"));
      } else {
        var numReg = /^[0-9]+$/;
        var numRe = new RegExp(numReg);
        if (!numRe.test(value)) {
          callback(new Error("请输入正确的验证码"));
        } else {
          callback();
        }
      }
    };
    return {
      //验证码重新发送间隔时间
      resendVerificateCodeTime: 120,
      //发送短信按钮是否禁用
      verificateCodeButtonDisabled: false,
      //按钮标题
      verificateCodeButtonTitle: "发送验证码",
      changeForm: {
        userName: "",
        phoneNumber: "",
        resetToken: "",
        resetTokenId: "",
        password: "",
        currentPassword: "",
        newPassword: "",
        againPassword: "",
      },
      changeFormRules: {
        // currentPassword: [
        //   { required: true, message: "请输入原密码", trigger: "blur" },
        // ],
        // newPassword: [
        //   { required: true, trigger: "blur", validator: validatePassword },
        // ],
        userName: [
          { required: true, message: "请输入用户名", trigger: "blur" },
        ],
        phoneNumber: [
          {
            required: true,
            trigger: "blur",
            validator: validatePhoneNumber,
          },
        ],
        resetToken: [
          {
            required: true,
            trigger: "blur",
            validator: validateCode,
          },
        ],
        password: [
          { required: true, trigger: "blur", validator: validatePassword },
        ],
        againPassword: [
          { required: true, trigger: "blur", validator: validatePassword2 },
        ],
      },
    };
  },
  computed: {
    ...mapGetters({
      username: "name",
    }),
  },
  mounted() {},
  methods: {
    timer() {
      if (this.resendVerificateCodeTime > 0) {
        this.verificateCodeButtonDisabled = true;
        this.resendVerificateCodeTime--;
        this.verificateCodeButtonTitle =
          "(" + this.resendVerificateCodeTime + "s)后重新发送";
        setTimeout(this.timer, 1000);
      } else {
        this.resendVerificateCodeTime = 0;
        this.verificateCodeButtonTitle = "发送验证码";
        this.verificateCodeButtonDisabled = false;
      }
    },
    getChangePwdVerificateCode() {
      this.$refs.changeForm.validateField("phoneNumber", (phoneNumber) => {
        if (!phoneNumber) {
          this.resendVerificateCodeTime = 120;
          this.verificateCodeButtonDisabled = true;
          console.log(this.changeForm);
          var data = {
            phone: this.changeForm.phoneNumber,
            appName: "Web",
            username: this.changeForm.userName,
            returnUrl: "",
            returnUrlHash: "",
          };

          sendPasswordResetCode(data)
            .then((res) => {
              this.timer();
              this.changeForm.resetTokenId = res;
              this.$message.success("发送成功");
            })
            .catch(() => {
              this.verificateCodeButtonDisabled = false;
            });
        }
      });
    },
    handleChangePasswordSure() {
      this.$refs.changeForm.validate(async (valid) => {
        if (valid) {
          if (!this.changeForm.resetTokenId) {
            this.$message.error("验证码未发送");
            return;
          }
          var resetCodeCheck = await checkPasswordResetCode({
            token: this.changeForm.resetToken,
            phoneNumber: this.changeForm.phoneNumber,
            id: this.changeForm.resetTokenId,
          });

          if (!resetCodeCheck) {
            this.$message.error("验证码错误");
            return;
          } else {
            resetPassword(this.changeForm)
              .then(() => {
                this.$message.success("修改成功");
                this.changeForm = {
                  userName: "",
                  phoneNumber: "",
                  resetToken: "",
                  resetTokenId: "",
                  password: "",
                  currentPassword: "",
                  newPassword: "",
                  againPassword: "",
                };
                this.$store.dispatch("user/passwordValid", true);
              })
              .catch(() => {
                this.$message.error("修改失败");
              });
          }
          // changePassword(this.changeForm).then((res) => {
          //   this.$message.success("修改成功");
          //   this.changeForm = {
          //     currentPassword: "",
          //     newPassword: "",
          //     againPassword: "",
          //   };
          // });
        } else {
          return false;
        }
      });
    },
  },
};
</script>
<style scoped>
.password_form {
  width: 100%;
}
.password_form ::v-deep .el-form-item input {
  width: 100%;
}
</style>
