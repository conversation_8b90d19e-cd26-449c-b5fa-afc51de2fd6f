<template>
	<div class="radial-indicator-container" >
		<div class="percentloop">
			<div class="circle-left" :style="{ backgroundColor: color}">
				<div :style="{ backgroundColor: backgroundColor,'transform':leftstyle.transform}" ref="leftcontent"></div>
			</div>
			<div class="circle-right" :style="{ backgroundColor: color}">
				<div :style="{ backgroundColor: backgroundColor,'transform':rightstyle.transform}" ref="rightcontent"></div>
			</div>
			<div class="content-wrap">
				<!-- <icon :name="icon" width="28" :style="{ 'fill': color }" /> -->
				<div class="content-number" :title="percentNum+'%'">{{percentNum}}%</div>
				<div class="content-text" :title="txt">{{txt}}</div>
			</div>
		</div>
	</div>
</template>
<script>
	// Events: animationFinished
	export default {
		name: 'RadialIndicator',
		props: {
			txt: '',
			percentNum: 0,
		},
		data() {
			return {
				color:"#1a74ef",
				backgroundColor:"#f3f8fe",
				leftstyle:{
					transform:''
				},
				rightstyle:{
					transform:''
				}
			}
		},
		created() {
			this.goRotate(this.transformToDeg(this.percentNum))
		},
		methods: {
			transformToDeg(percent) {
				let deg = 0
				if (percent >= 100) {
					deg = 360
				} else {
					deg = parseInt(360 * percent / 100)
				}
				return deg
			},
			//  > 180
			rotateLeft(deg) {
				this.leftstyle.transform = 'rotate(' + (deg - 180) + 'deg)'
			},
			//  < 180
			rotateRight(deg) {
				this.rightstyle.transform = 'rotate(' + deg + 'deg)'
			},
			goRotate(deg) {
				if (deg >= 180) {
					this.rotateLeft(deg)
					this.rotateRight(180)
				} else {
					this.rotateRight(deg)
				}
			},
		}
	}
</script>
<style >
	.radial-indicator-container{width: 184px;height:184px;}
	.radial-indicator-container .percentloop {
		position: relative;
		width: 100%;
		height: 100%;
		border-radius: 50%;
		overflow: hidden;
	}

	.radial-indicator-container .circle-left,
	.radial-indicator-container .circle-right {
		position: absolute;
		top: 0;
		left: 0;
		width: 50%;
		height: 100%;
		overflow: hidden;
	}

	.radial-indicator-container .circle-left>div,
	.radial-indicator-container .circle-right>div {
		width: 100%;
		height: 100%;
		transform-origin: right center;
		/* transition: all .5s linear; */
	}

	.radial-indicator-container .circle-right {
		left: 50%;
	}

	.radial-indicator-container .circle-right>div {
		transform-origin: left center;
	}

	.radial-indicator-container .content-wrap {
		position: absolute;
		top: 4%;
		bottom: 4%;
		left: 4%;
		right: 4%;
		background-color: #fff;
		border-radius: 50%;
		overflow: hidden;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		color: #000;
	}

	.radial-indicator-container .content-wrap .content-number {
		font-size: 24px;
		padding: 5px 0 7px 0;
		cursor: pointer;
	}

	.radial-indicator-container .content-wrap .content-text {
		display: inline-block;
		max-width: 90px;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		cursor: pointer;
	}
</style>
