<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <title>视频播放</title>
</head>
<body>
  <div id="playercontainer"></div>

  <script type="text/javascript" src="./cyberplayer-3.5.7/cyberplayer.js"></script>
  <script type="text/javascript" src="./cyberplayer-3.5.7/videojs/video.min.js"></script>
  <script type="text/javascript" src="./cyberplayer-3.5.7/videojs/videojs-contrib-hls.min.js"></script>
  <script type="text/javascript" src="./cyberplayer-3.5.7/videojs/videojs-contrib-quality-levels.min.js"></script>

  <script type="text/javascript">
    function getQueryVariable(variable) {
      var query = window.location.search.substring(1);
      var vars = query.split("&");
      for (var i = 0; i < vars.length; i++) {
        var pair = vars[i].split("=");
        if (pair[0] == variable) {
          return pair[1];
        }
      }
      return (false);
    }
    var url=getQueryVariable("url");
    var player = cyberplayer("playercontainer").setup({
      width: "100%",
      height: "100%",
      backcolor: "#FFFFFF",
      stretching: "uniform",
      file: url,
      ak: "5a8cf05266684131a0bed321d0032848",
      autoStart: true,
      repeat: false,
      volume: 100,
      controls: "over"
    });

  </script>
</body>

</html>
