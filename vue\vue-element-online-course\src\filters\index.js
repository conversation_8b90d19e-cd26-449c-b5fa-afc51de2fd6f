import moment from 'moment'
const timeFromte = function (time) {
  var second = time % 60
  var minute = Math.floor((time % 3600) / 60)
  var hour = Math.floor(time / 3600)
  return (hour > 9 ? hour : '0' + hour) + ':' + (minute > 9 ? minute : '0' + minute) + ':' + (second > 9 ? second : '0' + second)
}
const timeFromte2 = function (time) {
  var second = time % 60
  var minute = Math.floor(time / 60)
  return (minute > 9 ? minute : '0' + minute) + ':' + (second > 9 ? second : '0' + second)
}
const DateFromte = function (dataStr, pattern = 'YYYY-MM-DD HH:mm:ss') {
  if (dataStr != null) return moment(dataStr).format(pattern)
  else return '--'
}
const userNameFormat = function (name) {
  var reg_tel = /^(1[3-9])\d{9}$/;
  var reg_Tel = new RegExp(reg_tel);
  if (reg_Tel.test(name)) {
    var reg = /^(\d{3})\d{4}(\d{4})$/;
    return name.replace(reg, "$1****$2");
  } else {
    return name
  }

}
const Substr = function (str, num) {
  if (str != null && str != '' && str.length > num) return str.substring(0, num - 1) + '...'
  else return str
}
const showNum = function (num) {
  //num = 34349
  if (num >= 10000) return Math.round(num / 10000 * 10) / 10 + '万'
  else return num
}
const toFixed = function (num) {
  // num =  10.257
  if (Number.isInteger(num) || Number.isInteger(num * 10)) return num
  else return num.toFixed(2)
}
const showTime = function (time) {
  time = moment(time).format("YYYY-MM-DD HH:mm:ss")
  // let date =
  //   typeof time === "number" ?
  //     moment(time) :
  //     moment((time || "").replace(/-/g, "/"));
  // let diff = (moment().getTime() - date.getTime()) / 1000;
  let diff = moment().diff(moment(time), 's')
  let dayDiff = moment().diff(moment(time), 'days')


  return (
    (dayDiff === 0 &&
      ((diff < 60 && "刚刚") ||
        (diff < 120 && "1分钟前") ||
        (diff < 3600 && Math.floor(diff / 60) + "分钟前") ||
        (diff < 7200 && "1小时前") ||
        (diff < 86400 && Math.floor(diff / 3600) + "小时前"))) ||
    (dayDiff === 1 && "昨天") ||
    (dayDiff < 7 && dayDiff + "天前") ||
    (dayDiff < 31 && Math.ceil(dayDiff / 7) + "周前") ||
    (dayDiff >=31 && time)
  );
}
export {
  timeFromte,
  timeFromte2,
  DateFromte,
  Substr,
  showTime,
  showNum,
  toFixed,
  userNameFormat
}
