<template>
    <div>
          <!-- 单选 -->
        <div v-if="ex.questionType==0"  class="danxuan-item">
            <span class="tigan"> {{index+1}} . {{ex.questionStem.Title}} ({{ex.score}}分) </span>
            <div v-if="ex.questionStem.Title_Imgs!=null" class="tigan-imgs">
                <img
                    fit="contain"
                    :src="image1"
                    :key="'img'+ ex.questionBankId + index1"
                    v-show="image1!=''"
                    v-for="(image1,index1) in ex.questionStem.Title_Imgs"
                    />
            </div>
            <el-radio-group v-model="ex.answer2"   @change="flush">
                <el-radio  size="medium"  style="margin-bottom:20px"    :label="option.Order" v-for="(option,index) in ex.questionStem.Options" :key="ex.questionBankId + option.Order">
                    {{answerOrder[option.Order-1]}}.{{option.Title}}
                    <div  v-if="option.Images!=null" class="xuanxiang-imgs">
                        <img
                            fit="contain"
                            :src="oimage"
                            :key="'img'+ ex.questionBankId + option.Order+ index2"
                            v-show="oimage!=''"
                            v-for="(oimage,index2) in option.Images"
                            />
                    </div>
                </el-radio>
            </el-radio-group>
        </div>
        <!-- 多选 -->
        <div v-if="ex.questionType==1"  class="duoxuan-item">
            <span class="tigan"> {{index+1}} . {{ex.questionStem.Title}} ({{ex.score}}分) </span>
            <div v-if="ex.questionStem.Title_Imgs!=null" class="tigan-imgs">
                <img
                    fit="contain"
                    :src="image"
                    :key="'img'+ ex.questionBankId + index1"
                    v-show="image!=''"
                    v-for="(image,index1) in ex.questionStem.Title_Imgs"
                  
                    />
            </div>
            <el-checkbox-group v-model="ex.answer2" @change="flush" >
                <el-checkbox  size="medium" style="margin-bottom:20px"  :label="option.Order" v-for="(option,index) in ex.questionStem.Options" :key="ex.questionBankId + option.Order">
                    {{answerOrder[option.Order-1]}}.{{option.Title}}
                    <div  v-if="option.Images!=null"  class="xuanxiang-imgs">
                        <img
                          
                            fit="contain"
                            :src="oimage"
                            :key="'img'+ ex.questionBankId + option.Order+ index2"
                            v-show="oimage!=''"
                            v-for="(oimage,index2) in option.Images"
                            
                            />
                    </div>
                </el-checkbox>
            </el-checkbox-group>
        </div>
        <!-- 判断 -->
        <div v-if="ex.questionType==2" class="panduan-item" >
            <span class="tigan"> {{index+1}} . {{ex.questionStem.Title}} ({{ex.score}}分) </span>
            <div v-if="ex.questionStem.Title_Imgs!=null" class="tigan-imgs">
                <img
                  
                    fit="contain"
                    :src="image"
                    :key="'img'+ ex.questionBankId + index1"
                    v-show="image!=''"
                    v-for="(image,index1) in ex.questionStem.Title_Imgs"
                    
                    />
            </div>
            <!-- {{ex.answer}} -->
            <el-radio-group v-model="ex.answer2" direction="horizontal" @change="flush">
              <el-radio label="正确"  style="margin-bottom:20px"  size="medium"  >A.正确</el-radio>
              <el-radio label="错误"  style="margin-bottom:20px"  size="medium" >B.错误</el-radio>
            </el-radio-group>
        </div>
        <!--填空题-->
        <div v-if="ex.questionType==3" class="blank-item" >
            <span class="tigan"> {{index+1}} . 
                <!-- {{ ex.questionStem.Title}} -->
                <span v-for="(str,i) in ex.questionStem.TitleArr">
                    {{str}} <input v-if="i<ex.questionStem.TitleArr.length-1"  v-model="ex.answer2[i]" type="text"/>
                </span>
                <!-- {{ex.questionStem.Title}} -->
                ({{ex.score}}分) 
             </span>
            <div v-if="ex.questionStem.Title_Imgs!=null" class="tigan-imgs">
                <img
                  
                    fit="contain"
                    :src="image"
                    :key="'img'+ ex.questionBankId + index1"
                    v-show="image!=''"
                    v-for="(image,index1) in ex.questionStem.Title_Imgs"
                    />
            </div>
        </div>
        <!--简答题-->
        <div v-if="ex.questionType==6" class="expand-item" >
            <span class="tigan"> {{index+1}} . {{ex.questionStem.Title}} ({{ex.score}}分) </span>
            <div v-if="ex.questionStem.Title_Imgs!=null" class="tigan-imgs">
                <img
                  
                    fit="contain"
                    :src="image"
                    :key="'img'+ ex.questionBankId + index1"
                    v-show="image!=''"
                    v-for="(image,index1) in ex.questionStem.Title_Imgs"
                    
                    />
            </div>
            <!-- {{ex.answer}} -->
            <!-- <el-radio-group v-model="ex.answer" direction="horizontal">
              <el-radio name="正确" @click="flush">A.正确</el-radio>
              <el-radio name="错误" @click="flush">B.错误</el-radio>
            </el-radio-group> -->
            <textarea v-model="ex.answer2" ></textarea>
        </div>
    </div>
</template>
<script >
export default {
  name: 'question',
  props:{
    ex:{},
    index:0
  },
  data(){
    return{
        answerOrder:['A','B','C','D','E','F','G','H','I','J','K','L','M','N'],
    }
  },
  
  mounted(){
    
  } ,
  methods:{
    flush(){
        //console.log(333)
        this.$forceUpdate()
        this.$emit('flushFun')
    },
    // setAnswer(ex,index){
    //     var isAnswered = false
    //     var inputList = this.$refs['Answer' + (index+1)]
    //     // console.log(inputList)
    //     for(var i = 0;i<inputList.length;i++){
    //         if(inputList[i].value!='') isAnswered = true
    //     }
    //    if (isAnswered) ex.answer = 1
    //    else ex.answer = ''
    // }

  }
}
</script>

