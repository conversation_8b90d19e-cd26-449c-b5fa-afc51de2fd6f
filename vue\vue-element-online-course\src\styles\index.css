html {
    height: 100%;
    font-family: "微软雅黑";
    /* min-width: 1240px; */
  }
  @media print {
    body * {
      display: none;
    }
    body::after {
      content: "打印功能已被禁用。";
    }
  }
  body{height: 100%;}
  a {
    text-decoration: none;
  }
  #app,.layout-box{height: 100%; }
  .layout-box{ min-width: 1240px;}
  /*登录注册忘记密码*/

  div.customDialog {
    max-width: 400px;
    border-radius: 20px;
  }
  .app-main{
    background: #ebeef2;
    overflow: hidden;
    min-height: 100%;
    }
  .carousel-div{max-width: 1280px;margin: auto;padding: 20px 0;}
  .carousel-div .el-carousel{height: 360px;width:100%;border-radius: 20px;overflow: hidden;display: inline-block;}
  .school_link{display: inline-block;height: 20px;line-height: 0px;padding-right: 15px;border-right: 1px solid #999;cursor: pointer;}
  .school_link svg{vertical-align:middle;margin-right: 10px;}
  .school_link span{vertical-align:middle;color: #024b9a;font-size: 16px;}
  .carousel-div .el-carousel__container{height: 360px;}
  .carousel-div .el-image{width: 100%;height: 360px;}
  .RecommandCourse{max-width: 1280px;margin: auto;width: 100%;}
  .RecommandCourse h2 a{position:absolute;right: 16px;font-size: 16px;bottom: 7px;color: #666;cursor: pointer;}
  .RecommandCourse h2{font-size: 24px;font-weight: normal;border-bottom: 1px solid #ccc;height: 40px;position: relative;}
  .courseitem{width: 270px;border-radius: 20px;height: 320px;overflow: hidden;background: #fff;cursor: pointer;display: inline-block;margin-left:65px;margin-bottom: 24px;box-shadow: 0 0 10px #e6e6e6;transition: transform .3s ease-in-out;}
  .courseitem:hover {transform: translate3d(0,-10px,0);}
  .courseitem:nth-child(4n+1){margin-left: 0;}
  .courseitem .imgpan{height: 152px;display: inline-block;overflow: hidden;position: relative;width: 100%;}
  .courseitem .imgpan img{width: 100%;}
  .courseitem .title{font-size: 16px;color: #333;display: block;line-height: 24px;margin: 12px 20px 5px;height: 48px;overflow: hidden;}
  .courseitem .p-div{margin:0 20px;line-height: 16px;font-size: 14px;}
  .courseitem .teacher{/* margin-left: 20px; */font-size: 14px;line-height: 16px;color:#999;vertical-align: middle;width: 46%;display: inline-block;}
  .courseitem .teacher svg{vertical-align:middle;/* margin-top: -3px; */}
  .courseitem .teacher span{vertical-align:middle;}
  .courseitem .free{float: right;margin-right: 20px;font-size: 16px;color: #ed570e;letter-spacing: 5px;font-weight: bold;}
  .courseitem:hover .title{color: #024b9a;}
  .notice-info-con{display: inline-block;height: 50px;line-height: 50px;width: 50px;text-align: center;    margin: 0 8px;cursor: pointer;border-radius: 50%;}
  .notice-info-con:hover{background: #f3f5fb;}
  .notice-info-con img{vertical-align: middle;}
  .newsList{       border-radius: 20px;    background: #fff;    vertical-align: top;    margin: 0 20px;    width: auto;}
  .newsList .title{height: 40px;line-height: 40px;margin: 0 20px;border-bottom: 1px solid #d8d8d8;font-size: 16px;}
  .newsList .title a{float: right;color: #666;font-size: 14px;cursor: pointer;}
  .newsList .list{    overflow: hidden;    margin: 20px 0;}
  .newsList .item-news{line-height: 24px;padding: 10px 20px;font-size: 14px;cursor: pointer;border-radius: 6px;}
  .newsList .item-news:hover{background: #f3f5fb;}
  .newsList .item-news:hover .name{color: #024b9a;}
  .newsList .item-news img{margin-right: 5px;vertical-align: middle;}
  .newsList .item-news .name{vertical-align: top;    display: inline-block;    width: 450px;font-size: 14px;
    font-weight: bold;}
  .newsList .item-news .time{font-size: 12px;color: #666;float:right;}
  .newsList .dot{width: 6px;height: 6px ;border-radius: 50%; border: 1px solid #2866a9; display: inline-block;vertical-align: middle;margin-right: 10px;}

  .notice-cent{width: 1200px ;margin: auto;}
  .notice-box{background: #fff;    border-radius: 12px;    overflow: hidden;    min-height: 800px;    margin-bottom: 30px;}

  .notice-box  .title{height: 60px;line-height: 60px;margin: 0 20px;border-bottom: 1px solid #d8d8d8;font-size: 16px;}
  .notice-box  .title a{float: right;color: #666;font-size: 14px;cursor: pointer;}
  .notice-box  .list{margin: 0 20px;}
  .notice-box  .item-news{    line-height: 48px;text-indent: 50px; cursor: pointer;
    margin: 10px 0;
    font-size: 14px;
    height: 48px;}

  .notice-box  .item-news:hover{background: #f3f5fb;color: #024b9a;border-radius: 4px;}
  .notice-box .item-news .name{vertical-align: middle;}
  .notice-box  .item-news img{margin-right: 5px;vertical-align: middle;}
  .notice-box   .dot{width: 6px;height: 6px ;border-radius: 50%; border: 1px solid #2866a9; display: inline-block;vertical-align: middle;margin-right: 10px;}
  .notice-box .item-news .time{
    float: right;
    font-size: 12px;
    color: #666;
    margin-right: 50px;
}
.notice-info-box .notice-title{font-size: 24px;  color: #010101;  margin: 20px 60px;}
.notice-info-box .notice-time{    height: 36px;
  line-height: 36px;
  margin: 0 60px;
  border-bottom: 1px solid #d8d8d8;
  font-size: 16px;
  color: #666;}
  .notice-info-box .notice-content{  margin: 20px 80px 0 60px;    padding-bottom: 80px;
        line-height: 24px;
        color: #000;
        font-size: 14px;}
.home-live-item  .lecturer{
      font-size: 14px;
      line-height: 16px;
      color: #999;
      /* width: 64%; */
      display: inline-block;
      vertical-align: top;
  }
  .home-live-item  .usercount{
      font-size: 14px;
      color: #999;
      max-width: 29%;
      /* text-align: right; */
      display: inline-block;
      /* border-left: 1px solid #999; */
      /* padding-left: 15px; */
      height: 16px;
      line-height: 14px;
      /* float: right; */
      vertical-align: top;
      display: block;
      margin-top: 10px;
  }
  .home-live-item  .usercount img{
      vertical-align: middle;
      height: 14px;
      margin-right: 5px;
  }
  .home-live-item  .usercount span{
      vertical-align: middle;
  }
  .home-live-item  .time{
      font-size: 14px;
      color: #999;
      margin: 8px 20px;
      display: inline-block;
  }
  .home-live-item  .time img{vertical-align:middle}
  .home-live-item  .time span{vertical-align:middle;margin-left: 5px;}
  .home-live-item   .live_state_default{
      position: absolute;
      top: 0;
  }
  .CourseList{max-width: 1280px;margin: auto;min-height: 700px;padding-bottom: 40px;}

  .catlist{max-width: 1240px;line-height: 48px;border-radius: 4px;margin: 20px auto 40px;background: #fff;padding-left: 40px;position: relative; padding-bottom: 10px; padding-top: 10px; /* transform: translate3d(0,0,0); */z-index: 9;}
  .catlist .list{   width: 1040px;display: inline-block;}
  .catlist a{
      font-size: 16px;
      margin-right: 56px;
      display: inline-block;
      cursor: pointer;
  }
  .catlist a:hover{color: #024b9a;}
  .catlist .ft .con{
      cursor: pointer;
  }
  .catlist .ft{float:right;width: 97px;height: 36px;line-height: 36px;margin: 12px;border-bottom: 1px solid #333;text-align: center;top: 0;position: absolute;right: 0;}
  .catlist .ft .node{/* display:none *//* margin-top: 10px; */position: relative;background: #fff;display: block;margin-top: -1px;border-radius: 4px;background-color: rgb(255, 255, 255);box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.25);width: 100px;height: 100px;text-align: center;margin-left: -1px;overflow: hidden;}
  .catlist .ft svg{
      margin-left: 10px;
      cursor: pointer;
  }
  .catlist .ft .content{/* display:inline-block; *//* width:100%; */text-align:center;}
  .catlist .ft a{
      margin-right: 0;
      text-align: center;
      background: #fff;
      width: 40px;
      display: block;
      margin-left: 15px;
      line-height: 30px;
      margin-top: 10px;
  }
  .catlist .ft a.cur{
      border-bottom: 1px solid #333;
  }
  .catlist a.active{color:#024b9a;font-weight: bold;}
  .search_box{    display: inline-block;
    margin-left: 12px;
    position: relative;
    margin-right: 10px;
    height: 40px;
    line-height: 40px;
    padding-right: 9px;
    border-bottom: 1px solid #b2b2b2;}
  .search_box .svg{cursor:pointer}
  .search_box input{
    width: 120px;
    border: none;
    height: 36px;
    line-height: 36px;
    text-indent: 5px;
    outline: none;
    margin-right: 10px;
  }
  .search_box svg{
    position: absolute;
    right: 0px;
    bottom: 12px;
    line-height: 100px;
    cursor: pointer;
  }
  .breadcrumb-box .bread_con {
      height: 80px;
      line-height: 80px;
      font-size: 16px;
      color: #666;
  }

  /*详情*/
  .course_info_box{
      max-width: 1200px;
      margin: auto;
      overflow: hidden;
  }
  .courseInfo img{
      width: 400px;
      height: 225px;
      margin: 20px;
      border: 1px solid #eee;
  }
  .courseInfo{background: #fff;border-radius: 4px;height: 265px;width: 780px;display: inline-block;vertical-align: top;}
  .courseInfo_right{
      display: inline-block;
      vertical-align: top;
      height: 225px;
      margin-top: 20px;
      position: relative;
      width: 318px;
  }
  .courseInfo_right img{
      width: auto;
      height: auto;
      border: none;
      margin: 0;
      vertical-align: middle;
  }
  .courseInfo_right .name{
      color: #333;
      font-size: 20px;
      line-height: 24px;
      display: block;
      /* height: 82px; */
      margin-bottom: 5px;
      /* overflow: hidden; */
      text-align: justify;
      min-height: 40px;
      max-height: 72px;
      overflow: hidden;
  }
  .courseInfo_right .process-right{display:inline-block;}
  .courseInfo_right .process-right .txt{display: block;text-align: center;color: #ea2413;font-size: 14px;    line-height: 14px;}
  .courseInfo_right .totalprocess{width: 130px;background: #dedede;height: 6px;border-radius: 3px;    display: inline-block;vertical-align: middle;}
  .courseInfo_right .process-txt{vertical-align: middle;    margin: 0 10px 0 3px}
  .courseInfo_right .totalprocess .process{background: #ea2413;height: 6px;border-radius: 3px;    display: inline-block;vertical-align: top;}
  .courseInfo_right .btn_info{    color: #024b9b;vertical-align: middle;cursor: pointer;
    margin-left: 59px;
    font-size: 12px;}
  .courseInfo_right .lecturer{display: block;margin-top: 20px;}
  .courseInfo_right .opr{
    position: absolute;
    display: block;
    bottom: 0;
    width: 100%;
    text-align: left;
  }
  .courseInfo_right .opr .state{
      border-radius: 4px;
      width: 80px;
      height: 30px;
      display: inline-block;
      vertical-align: middle;
      line-height: 30px;
      text-align: center;
      float: left;
      margin-top: 10px;
      font-size: 14px;
  }
  .courseInfo_right .opr .unStarted{
      background: #d4f9d4;
      color: #018b3e;
  }
  .courseInfo_right .opr .isCompleted{background:#e1e1e1;color:#333}
  .courseInfo_right .opr .isStudying{background:#ffdccc;color:#ed570e}
  .courseInfo_right .free{
      color: #024b9a;
      font-size: 24px;
      vertical-align: middle;
      margin: 0 30px;
      letter-spacing: 12px;
  }
  .courseInfo_right .time{
      font-size: 16px;
      color: #666;
      vertical-align: middle;
      margin-right: 20px;
      margin-top: 12px;
      display: inline-block;
      color: #999;
      font-size: 14px;
  }
  .courseInfo_right button{
      border-radius: 4px;
      background: -webkit-linear-gradient(0deg,#005dc2 0%, #024b9a 100%);
background: -moz-linear-gradient(0deg,#005dc2 0%, #024b9a 100%);
background: -o-linear-gradient(0deg,#005dc2 0%, #024b9a 100%);
background: -ms-linear-gradient(0deg,#005dc2 0%, #024b9a 100%);
background: linear-gradient(0deg,#005dc2 0%, #024b9a 100%);
      /* background-image: -moz-linear-gradient( 0deg, #005dc2 0%, #024b9a 100%);
      background-image: -webkit-linear-gradient( 0deg, #005dc2 0%, #024b9a 100%);
      background-image: -ms-linear-gradient( 0deg, #005dc2 0%, #024b9a 100%); */
      width: 160px;
      height: 44px;
      border: 0;
      color: #fff;
      cursor: pointer;
      border-radius:  22px;
  }
  .courseInfo_right .score_txt_info{font-size: 12px;vertical-align: middle;line-height: 44px;color: #024b9b;float: right;}
  .courseInfo_right .score_txt_info span{font-size: 14px; font-weight: bold;margin-right: 8px;}
  .courseInfo_right button:disabled{
      opacity: 0.6;
      cursor: auto;
  }
  .courseInfo_right  .notFree_btn{color:#e14500;background:#e3e3e3;cursor: default;}
  .courseInfo_right  .isExpired_btn{background:#e1e1e1;color:#666;}


  .desc-box{margin-top:20px;background:#fff;width: 780px;border-radius: 4px;padding-bottom: 20px;margin-bottom: 40px;overflow: hidden;display: inline-block;}
  .desc-box .tabslist{height: 65px;line-height: 65px;margin: 0 20px;border-bottom: 1px solid #d8d8d8;}
  .desc-box .tabslist a{
      font-size: 16px;
      margin: 0 20px;
       display: inline-block;
      height: 62px;
      padding: 0 15px;
      cursor:pointer;
  }
  .desc-box .tabslist a:hover{color: #e41c23;}
  .desc-box .tabslist a.active{
      color: #e41c23;
      border-bottom: 4px solid #e41c23;
  }
  .desc-box .box{margin:20px;line-height: 40px;min-height: 700px;}
  .desc-box .box .desc-txt{
      min-height: 800px;
      padding: 0 20px;
      overflow: hidden;
  }
  .desc-txt img{max-width:100%}

  .Directory-div .item-dir .title{
      background-color: rgb(235, 238, 242);
      /* width: 741px; */
      height: 45px;
      color: #333;
      line-height: 45px;
      text-indent: 20px;
      font-size: 14px;
      overflow: hidden;
  }
  .Directory-div .Directory-div .item-dir .title{
      color: #999999;
      background: #fff;
      border-bottom: 1px solid #f0f0f0;
  }


  .evaluate-box{margin: 20px 20px 0;    min-height: 400px;}
  .evaluate-box .tab-info-txt{    height: 48px;
    color: #7a2f20;
    line-height: 48px;
    background: #fef7e0;
    border-radius: 4px;
    margin: 20px 0;
    text-indent: 15px;
    font-size: 14px;}
  .evaluate-list-box .e-item img{vertical-align: middle;}
  .evaluate-list-box .e-item .evaluate-title{color: #ea2413;font-size: 16px;    width: 455px;margin: 0 20px;color: #333; text-align: left;
      display: inline-block;
      vertical-align: middle;}
  .evaluate-list-box .e-item button{cursor: pointer; vertical-align: middle; width: 90px;height: 36px; text-align: center; line-height: 34px; border: 1px solid #ea2413;background: none;color: #ea2413;border-radius: 22px;}
  .evaluate-list-box .e-item button:disabled {background: #f2f2f2;border-color: #929292;color: #929292;cursor: auto;}
  .evaluate-info-div{ text-align: center;}
  .evaluate-box .evaluate-tihao-list{text-align: left;width: 712px;    margin: 0 auto 10px;}
  .evaluate-title {margin: 0 15px;    text-align: right;}
  .evaluate-title .title{display: inline-block;font-size: 16px; color: #333;font-weight: bold;}
  .evaluate-title .evaluate-num{color: #444;vertical-align: middle;}
  .evaluate-box .evaluate-tihao-list span{ cursor: pointer; position: relative; font-size: 12px;color: #999;text-align: center;line-height: 20px; display: inline-block;width: 70px;height: 20px; border: 1px solid #e4e4e4;margin-left: -1px;    margin-bottom: 8px;}
  .evaluate-box .evaluate-tihao-list span.active img{position:absolute ;left:-1px;bottom:0;}
  .evaluate-box .evaluate-tihao-list span.active,.evaluate-box .evaluate-tihao-list span.IsDone{background: #f5f5f6;}
  .evaluate-question-box{border: 1px solid #e4e4e4;background: #f5f5f6;border-radius: 4px;    margin: 15px 15px 0;text-align: left;    padding: 20px; min-height: 400px;}
  .evaluate-question-box .desc{line-height: 32px;  display: inline-block; margin-bottom: 20px;}
  .evaluate-question-box .imgs img{max-width: 100%;margin-bottom: 20px;}
  .evaluate-question-box label{display: block;}
  .evaluate-question-box label .el-radio__inner,  .evaluate-question-box label .el-checkbox__inner{width: 20px; height: 20px;   }
  .evaluate-question-box label  .el-radio__inner::after{width: 6px;height: 6px;}
  .evaluate-question-box label  .el-checkbox__inner::after{    height: 7px; left: 6px;top: 4px;width: 5px;}
  .evaluate-question-box label  .el-radio__label,  .evaluate-question-box label  .el-checkbox__label{line-height: 34px;vertical-align: middle;  word-break: break-all;  white-space: pre-line;color: #505968;
    display: inline-block;
    vertical-align: top;}
  .evaluate-question-box .el-radio-group,  .evaluate-question-box .el-checkbox-group{display: block;}
  .evaluate-opr{margin: 30px 15px;}
  .evaluate-opr button{cursor: pointer; width: 90px;height: 36px;line-height: 36px;text-align: center;border: 1px solid #333;background: none;line-height: 34px;border-radius: 18px;}
  .evaluate-opr .btn-submit{margin-right:25px;border-color: #ea2413;color: #ea2413;}
  .evaluate-opr .next,.evaluate-opr .prev{}
  .evaluate-opr .next:disabled,.evaluate-opr .prev:disabled{background: #f2f2f2;border-color: #929292;cursor: auto;}

  .evaluate-result-info{margin-top: 120px;text-align: center;}
  .evaluate-result-info img{vertical-align: middle;width: 144px;}
  .evaluate-result-info .result-txt{display: inline-block;margin-left: 25px;width: 280px;vertical-align: middle;color: #505968;font-size: 18px;line-height: 36px;    text-align: left;}
  .evaluate-result-nums{margin-top: 60px;text-align: center;margin-bottom: 60px;}
  .evaluate-result-nums .n-item{width: 128px;display: inline-block;text-align: left;}
  .evaluate-result-nums .n-item img{vertical-align: middle;margin-right: 10px;}
  .evaluate-result-nums .n-item .num{vertical-align: middle;font-size: 14px;}
  .evaluate-result-nums .n-item .num span{font-size: 16px;margin-left: 3px;font-weight: bold;}
  .evaluate-opr .fr{  float: right;}
  .evaluate-question-box .el-radio__input.is-checked .el-radio__inner{    border-color: #409EFF;
    background: #409EFF;}
  .evaluate-question-box .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner{    background-color: #409EFF;
    border-color: #409EFF;}
  .evaluate-question-box  .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner::after{border-color: #fff;}
  .evaluate-question-box   .el-radio__input.is-disabled.is-checked .el-radio__inner::after{background: #fff;}
  .evaluate-result-header{ margin: 15px 12px;
    padding: 0 20px;
    background: #ebeef2;
    height: 48px;
    line-height: 48px;
    border-radius: 4px;}
  .evaluate-result-header .r-item{}
  .evaluate-result-header .r-item img{vertical-align: middle;}
  .evaluate-result-header .r-item .num{vertical-align: middle;margin: 0 40px 0 8px;font-size: 14px;}
  .evaluate-result-header .r-item .num span{font-size: 16px;margin-left: 3px;font-weight: bold;}
  .reslist a{font-size:14px;height:48px;line-height:48px;text-indent: 18px;display: block;cursor: pointer;border-bottom: 1px solid #f0f0f0;position: relative;}
  .reslist a:hover{color: #ed570e;}
  .resitem.grey{color:#999!important;cursor: text;}
  .resitem.grey svg path{fill: #999 !important;}
  .reslist .time{display: inline-block;text-indent: 0;margin-right: 10px;vertical-align: middle;/* position: absolute; *//* right: 0; */}
  .reslist .state{margin-right: 10px;vertical-align: middle;line-height: 8px;text-indent: 0;}
  .reslist .state .uncomplated{
      width: 8px;
      height: 8px;
      border: 1px solid #9496a7;
      display: inline-block;
      border-radius: 50%;
      /* margin-right: 10px; */
  }
  .reslist .state svg{
      margin-top: -3px;
  }
  .reslist svg{
      vertical-align: middle;
      /* margin-top: -3px; */
  }

  .reslist .swf-icon{
      border-radius: 2px;
      background-color: rgb(102, 102, 102);
      width: 20px;
      height: 20px;
      display: inline-block;
      line-height: 20px;
      text-align: center;
      text-indent: 0;
  }
  .reslist .resname{
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    display: inline-block;
    width: 475px;
    line-height: 20px;
    height: 20px;
    vertical-align: middle;
  }
  .reslist .shikan_btn{
      display: inline-block;
      color: #e14500;
      border-radius: 4px;
      background-color: rgb(255, 255, 255);
      width: 37px;
      height: 16px;
      line-height: 15px;
      border: 1px solid #e14500;
      text-align: center;
      text-indent: 0;
      margin-right: 17px;
      vertical-align: middle;
  }
  .reslist .fr {float: right;color: #606266;position: absolute;right: 0;}
  .reslist  .resitem.grey .pdf-icon,.reslist  .resitem.grey .swf-icon{background: #999;}
  .reslist .pdf-icon{
      border-radius: 2px;
      background-color: rgb(102, 102, 102);
      width: 20px;
      height: 20px;
      display: inline-block;
      line-height: 20px;
      text-align: center;
      text-indent: 0;
      vertical-align: middle;
  }
  .Directory-div .Directory-div .Directory-div .item-dir {}
  .resitem.active{
      color: #ed570e;
  }
  .resitem.active  svg path {
      fill: #ed570e;
  }
  .resitem.active  .pdf-icon svg path,.resitem.active  .swf-icon svg path{
    fill:#fff;
  }
  .resitem.active  .pdf-icon ,.resitem.active  .swf-icon {
    background:#ed570e;
  }
  .recommand_course_box{
      display: inline-block;
      background: #fff;
      width: 400px;
      margin-left: 20px;
      vertical-align: top;
      /* margin-top: 20px; */
      /* padding-bottom: 50px; */
      /* min-height: 400px; */
      /* cursor: pointer; */
      height: 265px;
      overflow: hidden;
  }
  .box-title{
      height: 64px;
      line-height: 64px;
      font-size: 16px;
      margin: 0 20px;
      border-bottom: 1px solid #d8d8d8;
  }
  .box-title span{
     vertical-align: middle;
  }
  .box-title .line{
     width: 5px;
     height: 20px;
     background:#024b9a;
     display: inline-block;
     border-radius: 4px;
     margin-right: 20px;
     margin-left: 20px;
  }
  .courseitem1{
      margin: 7px 20px 0;
  }
  .courseitem1 .imgpan{display: inline-block;overflow: hidden;border-radius: 4px;width: 116px;height: 80px;border: 1px solid #eee;}
  .courseitem1 .imgpan img{width: 100%;}
  .courseitem1 .rt{display: inline-block;vertical-align: top;height: 82px;width: 240px;}
  .courseitem1 .title{font-size: 16px;color: #333;display: block;line-height: 22px;margin: 0 20px;height: 44px;cursor: pointer;}
  .courseitem1 .teacher{font-size: 14px;line-height: 16px;color:#999;display: block;margin-left: 20px;margin-bottom: 10px;margin-top: 10px;}
  .courseitem1 .teacher svg{vertical-align:middle;}
  .courseitem1 .free{margin-right: 20px;font-size: 14px;margin-left: 20px;color: #ed570e;}
  /*导航栏*/
  .header {
    background: #fff;
    /* position: relative; */
    position: fixed;
    width: 100%;
    z-index: 999;
    top: 0;
    min-width: 1240px;
  }
  .el-popup-parent--hidden .header .h_container{padding-right: 27px;}
  .head_blank{height: 100px;}
  .h_container {
    margin: auto;
    line-height: 100px;
    position: relative;
    height: 100px;
    padding: 0 10px;
    /* overflow: hidden; */
    max-width: 1250px;
  }
  .fixleft {
    display: inline-block;
    height: 100%;
  }

  .fixleft span {
    margin-left: 10px;
    font-size: 24px;
    vertical-align: middle;
    color: #333;
  }
  .fixright {
    text-align: center;
    float: right;
  }
  .down-link {
    cursor: pointer;
    color: #333;
  }

  /* new index */

  /*修改导航*/
  .logoClass {
    height: 35px;
    padding-top: 10px;
    vertical-align: middle;
  }
  .menulist {
    display: inline-block;
  }
  .menulist a {
    display: inline-block;
    margin: 0 8px;
    text-decoration: none;
    color: #000;
    transition: color 0.25s ease-in-out, background-color 0.25s ease-in-out,
    border-color 0.25s ease-in-out;
    font-weight: bold;
    cursor: pointer;
    font-size: 16px;
    font-weight: normal;
    padding: 0 9px;
  }
  .menulist a:hover{color: #024b9a;}
  .fixleft {
    /* margin-right: 5px; */
  }
  .user_box {
    display: inline-block;
    height: 44px;
    /* width: 171px; */
    width: 60px;
    vertical-align: middle;
    line-height: 44px;
    border-radius: 4px;
    /* margin-left: 20px; */
    color: #424551;
    cursor:pointer;
  }
  .user_box .userImg{
      vertical-align: middle;
      margin-right: 5px;
  }
  .user_box .uifo{
      vertical-align: middle;
  }
  .user_box .uifo span{
      width: 86px;
      overflow: hidden;
      display: inline-block;
      vertical-align: middle;
      white-space: nowrap;
      text-overflow: ellipsis;
      margin-top: -5px;
  }

  .pageend {
    font-size: 18px;
    color: #999;
    line-height: 45px;
    text-align: center;
  }

  .down-link span {
    color: #333;
  }

  .fixleft a {
    padding: 10px;
  }

  .foot_nav h2 {
    text-align: left;
    font-size: 24px;
    color: #464e58;
    font-weight: normal;
  }
  .foot_nav .weblink a {
    color: #464e58;
    margin-right: 20px;
    cursor: pointer;
  }
  .menulist .router-link-exact-active {
    color: #024b9a;
    font-weight: bold;
    display: inline-block;
    position: relative;
  }
  .menulist .router-link-exact-active .span0 {
    display: inline-block;
    background: #024b9a;
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 4px;
    padding: 0 5px;
    margin-left: -5px;
  }

  .footer-box {
    overflow: hidden;
  }

  .footer_con1 {
    background-image: -moz-linear-gradient(
      0deg,
      rgb(49, 189, 208) 0%,
      rgb(81, 216, 143) 100%
    );
    background-image: -webkit-linear-gradient(
      0deg,
      rgb(49, 189, 208) 0%,
      rgb(81, 216, 143) 100%
    );
    background-image: -ms-linear-gradient(
      0deg,
      rgb(49, 189, 208) 0%,
      rgb(81, 216, 143) 100%
    );
    height: 240px;
    text-align: center;
    line-height: 240px;
  }
  .footer_div .che {
    text-align: center;
    margin-top: -70px;
  }
  .footer_con2 {
    margin: auto;
    padding: 40px 10px;
    max-width: 1200px;
    /* height: 300px; */
    /* overflow: hidden; */
  }
  .footer_con3{font-size: 14px;text-align:center;margin: auto;width: 100%;/* height: 60px; *//* line-height: 60px; */padding-bottom: 30px;}
  /* .links-list {
    display: inline-block;
    vertical-align: top;
    margin-right: 78px;
    width: 460px;
    margin-left: 35px;
  } */
  .links-list-t .title {
    font-size: 18px;
    color: #333;
    margin-right: 60px;
  }
  .links-list-t{display: inline-block;}
  .links-list-t a{margin: 0 20px;font-size: 14px;color: #333;}
  /* .links-list a {
    display: inline-block;
    line-height: 36px;
    font-size: 18px;
    color: #333;
    margin: 0 40px;
  } */
  .links-list-right {
    float: right;
    margin-right: 0;
    width: 220px;
    display: inline-block;
    position: relative;
  }
  .links-list-right .tel {
    font-size: 30px;
    margin-top: 15px;
  }

  .links-list-right span{
      font-size: 14px;
      display: block;
      line-height: 40px;
  }
  .links-list-right span svg{vertical-align: middle;}
  .links-c{text-align: center;margin-bottom: 20px;clear: both;}
  .links-c a{font-size: 14px;color: #333;vertical-align: middle;}
  .links-c .hr{width: 1px  ;
        display: inline-block;
        background: #333;
        height: 12px;
        vertical-align: middle;margin: 0 30px  ;}
  .links-list-right .box{
     box-shadow: 0px 10px 50px 0px rgba(0, 0, 0, 0.1);
     background: #fff;
     position: absolute;
     bottom: 48px;
     left: 24px;
     width: 225px;
     height: 228px;
     text-align: center;
     padding: 10px 0;
     }
  .links-list-right   .jue{
      width: 15px;
      height: 15px;
      position: absolute;
      transform: rotate(45deg);
      z-index: 9;
      background: #fff;
      left: 99px;
      bottom: -7px;
  }
  /*aboutus*/
  .aboutus-body {
    background: #fff;
    margin: 0 auto 50px;

  }
  .Head-title0{
      height: 60px;
      line-height: 60px;
      padding-left: 10px;
      margin-bottom: 20px;
  }
  .Head-title0 span{
      vertical-align: middle;
      font-size: 20px;
  }
  .Head-title0 .line{
      background: #ed570e;
      display: inline-block;
      height: 30px;
      width: 3px;
      vertical-align: middle;
      margin-right: 20px;
  }
  .aboutus-body .breadcrumb-box .bread_con{
      max-width: 1200px;
      margin: auto;
  }
  .aboutus-body .breadcrumb-box {background:#ebeef2}

  .aboutus-body-con{
      max-width: 1200px;
      margin: auto;
        padding: 50px 0;
  }
  .aboutus-body .cent {
    /* width: 1540px; */
    /* margin: 10px auto 50px; */
  }
  .aboutus-body .box {
    padding: 25px 10px 0 10px;
  }
  .aboutus-body .box h1 {
    font-size: 32px;
    color: #333;
    font-weight: normal;
    margin: 0;
  }
  .aboutus-body .box .title_en {
    margin: 30px 0;
    font-size: 32px;
  }
  .aboutus-body .box .txt-green {
    font-size: 24px;
    color: #009900;
    margin-bottom: 45px;
    text-indent: 32px;
  }
  .aboutus-body .box .p-txt {
    /* text-indent: 32px; */
    line-height: 36px;
    font-size: 16px;
  }
  .aboutus-body .box .font24 {
    font-size: 24px;
  }
  .aboutus-body img {
    width: 100%;
  }
  .zizhi {
    text-align: center;
    margin-bottom: 35px;
  }
  .zizhi .color-box {
    line-height: normal;
    display: inline-block;
    width: 87px;
    text-align: center;
    color: #fff;
    font-size: 18px;
    border-radius: 16px;
    padding: 16px 0;
    line-height: 26px;
    margin: 0 10px;
  }
  .zizhi .color-box span {
    font-size: 24px;
  }
  .c-c30f22 {
    background: #c30f22;
  }
  .c-f39700 {
    background: #f39700;
  }
  .c-8fc31e {
    background: #8fc31e;
  }
  .c-31a7e0 {
    background: #31a7e0;
  }
  .c-a40d5e {
    background: #a40d5e;
  }
  .c-601986 {
    background: #601986;
  }

  /*学习中心*/
  .profile_info{width:240px;display:inline-block;border-radius: 4px;background: #fff;min-height: 480px;padding-bottom: 100px;}
  .profile_info a{display:block;height:80px;line-height:80px;text-align:center;color: #333;border-left: 4px solid #fff;}
  .profile_info a:hover{color: #024b9a;}
  .profile_info .menuItemActive{
      border-left: 4px solid #024b9a;
      background: #f8f9fd;
      color: #024b9a;
  }
  .profile_info_box{width: calc(100% - 280px);display:inline-block;vertical-align:top;margin-left:28px;background: #fff;border-radius: 4px;padding-bottom: 180px;min-height: 550px;position: relative;}
  .profile_info_con{max-width: 1200px;margin: 27px auto;overflow: hidden;}
  .profile_info_title{color:#024b9a;margin: 0 20px;height: 64px;line-height: 64px;border-bottom: 1px solid #d8d8d8;}
  .profile_info_title span{
      display: inline-block;
      padding: 0 30px;
      margin-left: 20px;
      border-bottom: 4px solid #024b9a;
      height: 60px;
  }
  .profile_info_content{
      margin: 30px 66px;
      min-height: 720px;
  }
  .profile_info_content .el-form-item{
      margin-bottom: 24px;
  }
  .profile_info_content.el-form-item label{
      font-size: 16px;
      color: #333;
  }
  .profile_info_content .el-form-item input{
      height: 48px;
      width: 480px;
  }
  .saveButton{
      margin-top: 48px;
      border-radius: 4px;
      background: linear-gradient( to right,#005dc2,#024b9a);
      /* background-image: -moz-linear-gradient( 0deg, rgb(237,114,14) 0%, rgb(243,72,19) 100%);
      background-image: -webkit-linear-gradient( 0deg, rgb(237,114,14) 0%, rgb(243,72,19) 100%);
      background-image: -ms-linear-gradient( 0deg, rgb(237,114,14) 0%, rgb(243,72,19) 100%); */
      width: 220px;
      height: 44px;
      border: 0;
      font-size: 16px;
      letter-spacing: 1px;
      color: #fff;
      margin-left: 96px;
  }

  /*资源详情*/
  .videoBox{height: calc(100% - 200px);width: calc(100% - 40px);margin-left: 20px;background: #333;overflow: hidden;}
  .videoBox .fullscreen_btn{
    position: absolute;
    bottom: 20px;
    right: 30px;
    cursor: pointer;
  }
  .Res-page{background: #3c3f4a;position: relative;min-height: 100%;width: 100%;overflow: hidden;}
  .Res-page .right-box{width: 420px;position: absolute;right: 0;top: 0;background: #fff;height: 100%;overflow-y: scroll;transition: all 0.5s ease;}
  .Res-page .right-box.CloseRight{width:0}
  .Res-page .left-box{position: absolute;width: calc(100% - 420px);background: #3c3f4a;height: 100%;min-width: 819px;transition: all 0.5s ease;}
  .Res-page .left-box.CloseRight{width:100%}
  .Res-page .header-box{height: 110px;line-height: 110px;position: relative;}
  .Res-page .return_btn{
    display: inline-block;
    border-width: 1px;
    border-color: rgb(49, 52, 65);
    border-style: solid;
    background-color: rgb(54, 58, 72);
    width: 129px;
    line-height: 40px;
    font-size: 14px;
    height: 38px;
    color: #aeafb3;
    border-radius: 0 17px 17px 0;
    margin-right: 30px;
    cursor: pointer;
    vertical-align: middle;
    }
  .Res-page .return_btn:hover{background:#2b2e39;color:#fff}
  .Res-page .return_btn:hover .svg-span-arrow{background:#fff}
  .svg-span-arrow{
      display: inline-block;
      width: 16px;
      height: 16px;
      background: #9b9da4;
      line-height: 16px;
      text-align: center;
      border-radius: 100%;
      margin-left: 15px;
      margin-right: 10px;
  }
  .prev-box{
      position: relative;
      margin-right: 42px;
      vertical-align: middle;
  }
  .prev-box a,.next-box a{
      border-width: 1px;
      border-color: rgb(49, 52, 65);
      border-style: solid;
      border-radius: 4px;
      background-color: rgb(54, 58, 72);
      width: 38px;
      height: 38px;
      display: inline-block;
      line-height: 38px;
      text-align: center;
      z-index: 3;
      position: relative;
      cursor: pointer;
  }
  .prev-box a:hover,.next-box a:hover{background-color: #2b2e39;}
  .prev-box a svg path,.next-box a svg path{fill: #9b9da4;}
  .prev-box a:hover svg path,.next-box a:hover svg path{fill: #fff;}
  .prev-box .line,.next-box .line{
      height: 110px;
      position: absolute;
      width: 4px;
      background: #313441;
      left: 50%;
      margin-left: -2px;
  }
  .next-box .line{height:90px}
  .Res-title{font-size: 18px;color: #fff;width: calc(100% - 760px);display: inline-block;white-space: nowrap;text-overflow: ellipsis;overflow: hidden;vertical-align: middle;}
  .right-span{float: right;/* width: 440px; */height: 40px;line-height: 40px;margin-top: 35px;background: #d8d9db;margin-right: 60px;border-radius: 4px;overflow: hidden;}
  .duration{width: 108px;height: 22px;line-height: 22px;display: inline-block;text-align: center;color: #333;border-right: 1px solid #333;}
  .learnedtime{color: #333;width: 170px;display: inline-block;text-align: center;font-size: 16px;}
  .right-span button{
    background-image: -moz-linear-gradient( 0deg, rgb(237,114,14) 0%, rgb(243,72,19) 100%);
    background-image: -webkit-linear-gradient( 0deg, rgb(237,114,14) 0%, rgb(243,72,19) 100%);
    background-image: -ms-linear-gradient( 0deg, rgb(237,114,14) 0%, rgb(243,72,19) 100%);
    width: 160px;
    height: 40px;
    border: none;
    color: #fff;
    outline: none;
    cursor: pointer;
    }
  .right-span button.disable{background-color: #999999;background-image: none;}
  .shq_btn{cursor: pointer; display: inline-block;border-width: 1px;border-radius: 50% 0 0 50%;position: absolute;width: 38px;height: 38px;line-height: 38px;text-align: center;right: 0;top: 30px;z-index: 7;
    border-color: rgb(49, 52, 65);border-style: solid;background-color: #363a48;}
  .shq_btn svg path{fill: #9b9da4;}
  .courseinfo-box2{    border-bottom: 1px solid #b2b2b2;    width: 400px;}
  .shq_btn:hover{ border-color: rgb(49, 52, 65);  background-color: #2b2e39;}
  .shq_btn:hover svg path{fill: #fff;}
  .directoryDiv{width: 400px;float: right;margin-bottom: 50px;}
  .courseinfo-box2 img{
      width: 160px;
      margin: 16px 15px 16px 35px;
      height: 80px;
      vertical-align: middle;
  }
  .courseinfo-box2 span{
      display: inline-block;
      vertical-align: middle;
      /* margin-top: 17px; */
      font-size: 16px;
      color: #333;
      width: 190px;
      line-height: 28px;
  }
  .right-box .Directory-div{/* margin-bottom: 50px; */}
  .res-app-main{height: 100%;}
  .next-box{height: 90px;position: absolute;left: 161px;}
  .next-box a{margin-top: 30px;}
  .right-box .reslist .resname{
    margin-left: 15px;
    white-space: nowrap;
    text-overflow: ellipsis;
    width: 200px;
    display: inline-block;
    overflow: hidden;
    vertical-align: middle;
  }
  .videoBox .pre-img{width: 100%;max-height: 100%;}
  #reader{
    height: 100% !important;
    width: 100% !important;
    overflow-y: scroll;
    background: #fff;
    text-align: center;
  }
  #reader .wenku-toolsbar-mod{
      width: 100% !important;
  }
  .videoBox iframe{height: 100%;width:100%;border: none;}
  .profile_info_box .tabs{
      margin: 0 20px;
      height: 64px;
      line-height: 64px;
      border-bottom: 1px solid #d8d8d8;
  }
  .profile_info_box .tabs a{
      padding: 0 25px;
      height: 60px;
      margin-left: 20px;
      cursor: pointer;
      line-height: 60px;
      font-size: 16px;
      width: auto;
      border: none;
  }
  .profile_info_box .tabs a:hover{color: #024b9a;}
  .profile_info_box .tabs a.active{
      display: inline-block;
      color: #024b9a;
      border-bottom: 4px solid #024b9a;
  }
  .profile_info_box .titem{height: 160px;margin: 0 40px;cursor: pointer;}
  .mytrains .titem .left{width: 100px;display: inline-block;vertical-align: middle;}
  .mytrains .titem .line1{
      display: inline-block;
      width: 42px;
      vertical-align: middle;
      height: 100%;
      position: relative;
  }
  .mytrains .titem .line1 span{
      position: absolute;
      width: 8px;
      height: 8px;
      background: #8f91a3;
      border-radius: 4px;
      top: 50%;
      left: 50%;
      margin-left: -5px;
      margin-top: -4px;
  }
  .mytrains .titem .line1 hr{
      border: none;
      border-left: 1px dashed #c7c8d1;
      height: 100%;
      width: 0;
      padding: 0;
      margin-top: -1px;
  }
  .mytrains .titem .right{
      width: calc(100% - 145px);
      display: inline-block;
      border-bottom: 1px solid #f0f0f0;
      height: 100%;
      vertical-align: middle;
  }
  .mytrains .titem .right .con{display: inline-block;width: calc( 100% - 148px);margin-left: 20px;vertical-align: top;margin-top: 20px;}
  .mytrains .titem .right img{margin: 20px 0;vertical-align: middle;width: 120px;}
  .mytrains .titem .left .year{font-size: 16px;color: #333;display: block;text-align: right;}
  .mytrains .titem .left .day{font-size: 14px;color: #999;text-align: right;display: block;margin-top: 10px;}
  .mytrains .titem .right .con .title{
      font-size: 16px;
      margin-bottom: 14px;
      margin-top: 4px;
      height: 18px;
      line-height: 18px;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
  }
  .mytrains .titem .right .con:hover .title{color: #ed570e;;}
  .mytrains .titem .right .con .time{
      font-size: 14px;
      color: #666;
      margin-bottom: 12px;
  }
  .mytrains .titem .right .con .num{
      font-size: 14px;
      color: #666;
  }
  .mytrains .titem .right .con  .state{display: inline-block;border-radius: 4px;width: 70px;height: 28px;text-align: center;line-height: 28px;margin-top: 10px;font-size: 14px;}
  .mytrains .titem .right .con  .state.unstarted{background:#dffadf;color:#018b3e}
  .mytrains .titem .right .con  .state.isfinished{background:#eaeaea;color:#666}
  .mytrains .titem .right .con  .state.isTraining{background:#fceed9;color:#ea8f00}
  .mytrains .titem .right .prewImg{width: 180px;height: 100px;border-radius: 4px;margin: 30px 0;}
  .mytrains .titem .right  .course-con{width: calc(100% - 200px);margin-top:30px;position: relative;}
  .mytrains .titem .right  .course-con:hover .title{color:#ed570e;}
  .mytrains .titem .right .course-con .title{    margin-bottom: 10px;}
  .mytrains .titem .right .course-con .state{    margin-top: 0px;}
  .mytrains .titem .right .course-con .time{    margin-bottom: 7px;}
  .mytrains .titem .right .course-con .btn{width: 120px;height: 38px;text-align: center;line-height: 34px;font-size: 14px; border-radius: 4px;border: 1px solid;background: none;float: right;vertical-align: middle;position: absolute;right: 0;top: 50%;margin-top: -24px;cursor: pointer;}
  .mytrains .titem .right .course-con .btn.start{color:#ed570e;border-color:#ed570e ;}
  .mytrains .titem .right .course-con .btn.end{color:#999}
  .mytrains .titem .right .course-con .state.isTraining{background:#fbe3d9;color:#ed570e}


  /*培训详情*/
  .trainInfo{border-radius: 4px;  height: 200px;width: 840px;background: #fff;}
  .trainInfo .right{display: inline-block;width: 610px;vertical-align: middle;/* margin-top: 20px; */position: relative;min-height: 148px;}
  .trainInfo img{margin: 20px;width: 160px;vertical-align: middle;}
  .trainInfo .right{}
  .trainInfo .time{
      font-size: 16px;
      color: #666;
      margin-bottom: 12px;
  }
  .trainInfo .title{
      font-size: 24px;
      margin-bottom: 13px;
      max-height: 55px;
      overflow: hidden;
      min-height: 40px;
  }
  .trainInfo .num{
      font-size: 16px;
      color: #666;
  }
  .trainInfo .state{display: inline-block;border-radius: 4px;width: 80px;height: 30px;text-align: center;line-height: 30px;margin-top: 25px;}
  .trainInfo .state.unstarted{background:#dffadf;color:#018b3e}
  .trainInfo .state.isfinished{background:#eaeaea;color:#666}
  .trainInfo .state.isTraining{background:#fceed9;color:#ea8f00}

  .total_div{display: inline-block;width: 340px;height: 200px;background: #fff;margin-bottom: 20px;}
  .left_con{width: 840px;display: inline-block;vertical-align: top;}
  .right_con{width: 340px;display: inline-block;vertical-align: top;margin-left: 20px;}
  .left_con .desc-box{width: 100%;}
  .right_con .notic_box{background: #fff;padding-bottom:120px;}
  .right_con .notic_box .notic_content{margin: 20px;line-height: 25px;}

  .right_con .ft .item{width: 40%;display: inline-block;color: #ea2413;font-size: 24px;vertical-align: middle;}
  .right_con .ft .item.total{color: #333;}
  .right_con .ft .item span{color: #333;display: block;font-size: 16px;margin-top: 10px;}
  .right_con .h-line{height: 50px;width:1px ;background: #b1b1b1;display: inline-block;vertical-align: middle;}
  .right_con .sign-box{margin-bottom: 20px;position: relative;}
  .right_con .sign-box .sign-btn{position: absolute;    top: 20px;right: 20px;font-size: 16px;width: 120px;height: 36px;line-height: 36px;
    border: none;color: #fff;text-align: center;border-radius: 18px;cursor: pointer;
    background: linear-gradient(to right, #005dc2,#024b99);}
  .right_con .sign-box .sign-btn:disabled{background: #7fa5cd;}
  .right_con   .el-calendar-table td{border: none !important;font-size: 14px;color: #333;height:40px !important;}
  .right_con   .el-calendar-table  thead{background: #f3f5fb;}
  .right_con   .el-calendar-table .day-box{text-align: center;height: 40px; overflow: hidden;}
  .right_con   .el-calendar-table .day-box span{display: block;margin: 3px 0;}
  .right_con   .el-calendar-table  .el-calendar-day{height:40px !important;border-radius: 4px;width: 40px;}
  .right_con   .el-calendar-table  .current {}
  .right_con   .el-calendar-table  .el-calendar-table__row{height: 50px;}
  .right_con  .el-calendar-table td.is-selected{background: none;}
  .right_con  .el-calendar-table td.is-selected .el-calendar-day{background: #dadfe9;}
  .right_con  .el-calendar-table td.is-today .el-calendar-day{background: linear-gradient(to bottom ,#4b83ff,#00aaf9);color: #fff;}
  .right_con  .el-button-group button{font-size: 0;border: none;height: 20px;}
  .right_con  .el-button-group button:nth-child(1){background: url(/images/arrow-left.png) no-repeat center;}
  .right_con  .el-button-group button:nth-child(2){background: url(/images/icon_today.png) no-repeat center;background-size: contain;}
  .right_con  .el-button-group button:nth-child(3){background: url(/images/arrow-right.png) no-repeat center;}
  /*圆环*/
  /* .pie {width: 120px;height: 120px;border-radius: 50%;background: #fee6d9;position: relative;margin: 60px 30px;display: inline-block;vertical-align: middle;}
  .pie .circle {position: absolute;top: 50%;left: 50%;z-index: 100;transform: translate(-50%, -50%);width: 100px;height: 100px;background: white;border-radius: 50%;line-height: 100px;text-align: center;font-size: 24px;color: #ed570e;}
  .pie .circle span{font-size:24px;color:#bcc3d1}
  .pie .left, .pie .right {  width: 60px;  height: 120px;  float: left;  overflow: hidden;}
  .pie .left {  border-radius: 120px 0 0 120px;}
  .pie .right {  border-radius: 0 120px 120px 0;}
  .pie .left-deg, .pie .right-deg {  width: 60px;  height: 120px;  background: #fd5808;}
  .pie .left-deg {  transform-origin: right center;  }
  .pie .right-deg {  transform-origin: left center;  } */


  .total_div .ft{    display: inline-block;    vertical-align: middle;width: 100%;margin: auto;text-align: center;margin-top: 30px;}
  /* .total_div .learned{    font-size: 20px;    color: #ed570e;    text-align: center;    margin-bottom: 30px;}
  .total_div .learned span{    font-size: 14px;    color: #333;    display: block;    margin-top: 10px;}
  .total_div .total{    font-size: 20px;    text-align: center;    margin-top: 30px;}
  .total_div .total span{    font-size: 14px;    display: block;    margin-top: 10px;}
  .total_div .ft hr{    border: none;    border-top: 2px solid #ed570e;    width: 30px;} */

  .course_info_box .item-course{height: 160px;border-bottom: 1px solid #d8d8d8;cursor: pointer;}
  .course_info_box .item-course .info-box span{vertical-align: middle;}
  .course_info_box .item-course .info-box .item{ padding: 0 25px; display: inline-block;border-left: 1px solid #b1b1b1;  color: #999;  height: 16px;line-height: 16px;text-align: center;font-size: 14px;}
  .course_info_box .item-course .info-box .item:first-child{border-left: 0;text-align: left;padding-left: 0;}
  .course_info_box .item-course .info-box .classhour{color: #024c9b;}
  .course_info_box .item-course .info-box svg{vertical-align: middle;margin-right: 5px;height: 14px;}
  .course_info_box .item-course .type span{width: 64px;text-align: center;height: 20px;line-height: 20px;display: inline-block;font-size: 12px;border-radius: 4px;}
  .course_info_box .item-course .type1{background: #fbe3d9;color:#e64513;}
  .course_info_box .item-course .type2{background: #e7dffa;color:#700099;}
  .course_info_box .item-course img{width: 180px;height: 100px;border-radius: 4px;margin: 30px 19px;border: 1px solid #eee;}
  .course_info_box .item-course .fr{display: inline-block;width: calc(100% - 240px);margin-top: 30px;vertical-align: top;position: relative;height: 100px;}
  .course_info_box .item-course .fr.fr1{width: calc(100% - 150px);}
  .course_info_box .item-course .fr .title{font-size: 16px;line-height: 16px;margin-bottom: 10px;width: 459px;white-space: nowrap;text-overflow: ellipsis;overflow: hidden;}
  .course_info_box .item-course:hover .fr .title{color:#ed570e;}
  .course_info_box .item-course .fr .time{font-size: 14px;color: #666;line-height: 20px;}
  .course_info_box .item-course .fr .state{display: inline-block;border-radius: 4px;width: 70px;height: 28px;text-align: center;line-height: 28px;margin-top: 7px;position: absolute;bottom: 0;font-size: 14px;}
  .course_info_box .item-course .fr .state.unstarted{background:#dffadf;color:#018b3e}
  .course_info_box .item-course .fr .state.isfinished{background:#eaeaea;color:#666}
  .course_info_box .item-course .fr .state.isTraining{background:#fbe3d9;color:#ed570e}
  .course_info_box .item-course .fr .btn{width: 108px;height: 34px;text-align: center;line-height: 34px;padding: 0;font-size: 14px; border-radius: 18px;border: 1px solid;background: none;float: right;vertical-align: middle;position: absolute;right: 0;top: 50%;margin-top: -24px;cursor: pointer;}
  .course_info_box .item-course .fr .btn.start{color:#ea2413;border-color:#ea2413;}
  .course_info_box .item-course .fr .btn.end{color:#999;border-color:#999;}
  .course_info_box .item-course .fr .examState{    position: absolute;
    right: 160px;
    height: 48px;
    line-height: 48px;
    top: 50%;
    margin-top: -30px;
    color: #ea2413;
    font-size: 16px;}
    .course_info_box .item-course .fr .examState img{    border: none;
      height: 48px;
      width: 48px;
      margin: 0;}
.train-live-item{padding: 35px 0;border-bottom: 1px solid #d8d8d8;}
.train-live-item .con{position: relative;display: inline-block;vertical-align: top;    height: 130px;overflow: hidden;    border: 1px solid #eee;border-radius: 5px;}
.train-live-item .live_state{position:absolute;top:10px;left: 10px; height: auto;line-height: 25px;color: #fff;padding: 0 10px;border-radius: 14px;font-size: 12px;}
.train-live-item .live_state img{width: 12px;margin-right: 4px; vertical-align: middle;}
.train-live-item .live_state span{vertical-align: middle;}
.train-live-item .con .coverImage {width: 216px;display: inline-block;}
.train-live-item .live_state_default {background-color: #0096ff; }
.train-live-item .live_state_box_s {background-color: rgb(194, 198, 214);}
.train-live-item .live_state_box_p { background-color: rgb(255, 51, 51);}
.train-live-item .live_state_box_e { background: linear-gradient(to right, #005dc2, #024b9a); }
.train-live-item .live-right-div{display: inline-block;margin-left: 22px;width:calc(100% - 240px);position: relative;}
.train-live-item .live-right-div .title{font-size: 16px;color: #333;width: 100%;    line-height: 30px}
.train-live-item .live-right-div:hover .title{color:#ed570e;}
.train-live-item .info-box{font-size: 14px;line-height: 26px;    color: #666;}
.train-live-item .info-box img{vertical-align: middle;}
.train-live-item .info-box span{vertical-align: middle;}
.train-live-item .info-box .classhour{color: #024c9b;}
.train-live-item  .opr-btns{margin-top: 5px;}
.train-live-item  .opr-btns .classhour{line-height: 40px;    float: right;color: #024c9b;}
.train-live-item  .opr-btns button{display: block;    cursor: pointer;
    outline: none;
    border: none;
    font-size: 14px;
    color: #fff;
    min-width: 110px;
    height: 36px;
    line-height: 36px;
    text-align: center;
    border-radius: 22px;
    padding: 0 20px;    display: inline-block;
    margin-right: 15px;}
.train-live-item  .opr-btns button.statue1{background: #c3c7d7;}
.train-live-item  .opr-btns button.statue2{background: #014c9c}
.train-live-item  .opr-btns .btn_enter{color: #ea2413;border: 1px solid #ea2413;background: #fff;}
.train-live-item  .opr-btns .btn_enter:disabled{color: #ea2413;border: 1px solid #ea2413;background: #fff;}
.day-schedule-dialog .ts-box{    background: #f3f5fb;
  height: 54px;
  line-height: 54px;
  text-indent: 30px;
  color: #6b7f95;}
.day-schedule-list{margin: 20px ;}
.day-schedule-dialog .day-span{font-size: 18px;margin-left:30px;}
.day-schedule-dialog .day-schedule-list-scroll{height: 400px;}
.day-schedule-dialog .day-schedule-list-scroll .el-scrollbar__wrap{overflow-x: hidden;}
.day-schedule-dialog .train-live-item{padding: 15px 0;}
  /*学习记录*/
  .tabbox{
      margin-top: 20px;
  }
  .tabbox .tishi{
      background: #fcf4e5;
      margin: 20px;
      color: #e14500;
      font-size: 14px;
      height: 48px;
      line-height: 48px;
      text-indent: 30px;
  }
  .tabbox .tishi .table_train{}
  .table_default{
      margin: 0 20px;
  }
  .table_default .th_row{
      background: #ebeef2;
      height: 50px;
      line-height: 50px;
      font-size: 14px;
  }
  .table_default .th_row div{
      display: inline-block;
  }
  .table_default .row{
      height: 66px;
      line-height: 66px;
      border-bottom: 1px solid #f1f1f1;
  }
  .table_default .row:hover{background-color: #F5F7FA;}
  .table_train .td1{width: 25%;text-indent: 30px;}
  .table_train .td1 span{
      line-height: 19px;
      display: inline-block;
      margin-left: 30px;
      text-indent: 0;
      margin-right: 10px;
      vertical-align: middle;
      margin-top: -3px;
  }
  .table_train .td1 span.name{
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    width: 169px;
  }
  .table_train .td2{    width: 23%;}
  .table_train .td3{    width: 14%;}
  .table_train .td4{    width: 15%;}
  .table_train .td5{    width: 13%;   }
  .table_train .td6{    width: 10%;    text-align: center;}
  .table_default .row div{display: inline-block;font-size: 14px;}
  .table_train .td6 a,.table_default .td4 a{color: #3e54f4;cursor: pointer;}
  .table_train .row .td1{    text-indent: 0;}
  .table_comment .td1{width: 20%;text-indent: 30px;}
  .table_comment .title {
    font-size: 14px;
      line-height: 25px;
      vertical-align: middle;
      text-align: left;
      height: 25px;
      overflow: hidden;
      -webkit-line-clamp: 1;
  }
  .table_comment .td2{
      width: 45%;
      }
  .table_comment .content {
    font-size: 14px;
    line-height: 25px;
    vertical-align: middle;
    text-align: left;
    max-height: 50px;
    overflow: hidden;
    -webkit-line-clamp: 2;
    word-break: break-all;
  }
  .table_comment .td3{width: 20%;}
  .table_comment .td4{width: 15%;}
  .table_course .td1{width: 45%;text-indent: 30px;}
  .table_course .td1 .name {
      font-size: 14px;
      line-height: 25px;
      vertical-align: middle;
      text-align: left;
      max-height: 50px;
      overflow: hidden;
      text-indent: 0;
      width: 80%;
      -webkit-line-clamp: 2;
  }
  .table_course .td2{width: 15%;}
  .table_course .td3{width: 15%;}
  .table_course .td4{width: 25%;}

  .shadow_RecordInfo{position: absolute;width: 700px;left: 50%;margin-left: -350px;top: 150px;height: 450px;background: #fff;border-radius: 20px;box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.25);}
  .shadow_RecordInfo h2{
      height: 60px;
      text-indent: 20px;
      font-size: 16px;
      border-bottom: 1px solid #f1f1f1;
      line-height: 60px;
      margin: 0;
      margin-bottom: 25px;
  }
  .shadow_RecordInfo h2 a{
      float: right;
      margin-right: 20px;
      cursor: pointer;
      font-size: 24px;
  }
  .shadow_RecordInfo .table_train .th_row{
      background: #e4eaf3;
      height: 48px;
      line-height: 48px;
  }
  .shadow_RecordInfo .table_train .row{
      height: 42px;
      line-height: 42px;
  }
  .shadow_RecordInfo .table_train{
      margin: 0 10px;
  }
  .shadow_RecordInfo .list{height: 250px;    overflow-y: auto;}
  .my_pagination{text-align: center;padding:  20px 0;}

  /*登录注册*/

  .title_box{text-align: center;height: 30px;font-size: 22px;color: #000;line-height: 30px;margin-bottom: 30px;}
  .customDialog .loginRegistButton ,.customDialog .verificateCodeButton {
    border-radius: 4px;
    background: linear-gradient( to right,#005dc2,#024b9a);
    /* background-image: -moz-linear-gradient( 0deg, rgb(237,114,14) 0%, rgb(243,72,19) 100%);
    background-image: -webkit-linear-gradient( 0deg, rgb(237,114,14) 0%, rgb(243,72,19) 100%);
    background-image: -ms-linear-gradient( 0deg, rgb(237,114,14) 0%, rgb(243,72,19) 100%); */
    width: 100%;
    height: 44px;
    font-size: 16px;
  }
  .customDialog  .el-button.is-disabled, .customDialog .el-button.is-disabled:focus,  .customDialog .el-button.is-disabled:hover{background-image: -moz-linear-gradient( 0deg, rgb(237,114,14,0.5) 0%, rgb(243,72,19,0.5) 100%);background-image: -webkit-linear-gradient( 0deg, rgb(237,114,14,0.5) 0%, rgb(243,72,19,0.5) 100%);background-image: -ms-linear-gradient( 0deg, rgb(237,114,14,0.5) 0%, rgb(243,72,19,0.5) 100%);font-size: 12px;}
  .customDialog .loginRegistButton span,.customDialog .verificateCodeButton span{color: #fff;}
  .forget_box{
      height: 30px;
      text-align: right;
      float: right;
      cursor: pointer;
  }
  .tips_box{
      height: 40px;
      line-height: 40px;
      text-align: center;
  }
  .tips_box .tips_btn{
      cursor: pointer;
      color: #014c9c;
  }
  /*空白页*/
  .NoContent{
      text-align: center;
      padding: 120px 0;
      color: #666;
  }
  .NoContent span{
      display: inline-block;
      width: 100%;
      line-height: 42px;
  }
  /*more*/
  .moreData{
      width: 90%;
      margin: auto;
      text-align: center;
      border: 1px solid #f1f1f1;
      margin-top: 40px;
      line-height: 40px;
      position: absolute;
      left: 5%;
      bottom: 50px;
      cursor: pointer;
  }
  .moreData:hover{background:#f1f1f1}

  /*协议*/
  .aboutus-body .title{font-size: 16px;font-weight: bold;height: 36px;line-height: 36px;margin: 15px 15px;display: block;}
  .aboutus-body .p-span{margin: 0 10px;font-size: 14px;line-height: 36px;display: block;}

  .weui-switch {
    display: inline-block;
    position: relative;
    width: 42px;
    height: 20px;
    border: 1px solid #DFDFDF;
    outline: 0;
    border-radius: 16px;
    box-sizing: border-box;
    background-color: #DFDFDF;
    transition: background-color 0.1s, border 0.1s;
    cursor: pointer;
    vertical-align: middle;
  }
  .weui-switch:before {
    content: " ";
    position: absolute;
    top: 0;
    left: 0;
    width: 40px;
    height:19px;
    border-radius: 15px;
    background-color: #FDFDFD;
    transition: transform 0.35s cubic-bezier(0.45, 1, 0.4, 1);
    border-bottom: 1px solid #eee;
  }
  .weui-switch:after {
    content: " ";
    position: absolute;
    top: 0;
    left: 0;
    width: 20px;
    height:19px;
    border-radius: 15px;
    background-color: #FFFFFF;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);
    transition: transform 0.35s cubic-bezier(0.4, 0.4, 0.25, 1.35);
  }
  .weui-switch-on {
    border-color: #1AAD19;
    background-color: #1AAD19;
    border-bottom: 1px solid #f1f1f1;
  }
  .weui-switch-on:before {
    border-color: #1AAD19;
    background-color: #1AAD19;
  }
  .weui-switch-on:after {
    transform: translateX(20px);
  }
  .switchbox{float: right;}
  .box-top{height: 50px;}
  .switchbox .txt{    vertical-align: middle;  margin-left: 10px;    font-size: 14px;color: #909399;}


  /*问答评论*/
  .comment-list-box{}
  .opr-box{height: 60px;line-height: 60px;border-bottom: 1px solid #c5c5c5;/* margin-top: -20px; */}
  .opr-box .list{
      display:  inline-block;
      vertical-align:  middle;
  }
  .opr-box .list a{padding:0 20px; cursor: pointer; border-right: 1px solid #cecece;}
  .opr-box .list a:last-child{border: none;}
  .opr-box .list a.active{color: #ea2413}
  .opr-box button{
      display: inline-block;
      vertical-align:  middle;
      height: 36px;
      line-height: 36px;
      padding:  0;
      float: right;
      border:  none;
      background: #ea2413;
      color: #fff;
      width: 90px;
      border-radius:  18px;
      font-size:  14px;
      text-align:  center;
      margin-top: 12px;
      margin-right: 10px;
      outline:  none;
      cursor: pointer;
  }
  .comment-item{padding: 25px 0;border-bottom: 1px solid #d8d8d8;cursor: pointer;}
  .t-div .user-box img{vertical-align: middle;width: 36px;height: 36px;border-radius: 18px;margin-right: 18px;}
  .t-div .user-box span{font-size: 14px;}
  .t-div .type1{float: right;color: #024b9a;}
  .t-div .type0{float: right;color: #ea2413;}
  .t-div{}
  .t-div .user-box{padding: 0 15px; margin-bottom: 10px;}
  .m-div{}
  .m-div .content-div{color: #14191e;margin-left: 67px;font-size: 16px;word-wrap: break-word;line-height: 24px;margin-top: 6px;margin-bottom: 5px;}
  .m-div .sum{color: #999;margin-left: 67px;margin-top: 12px;font-size: 14px;overflow:  hidden;line-height:  24px;}
  .m-div .sum .num{margin-right: 25px;font-size: 13px;}
  .m-div .sum .time{float: right;margin-right: 10px;font-size: 13px;}
  .comment-list-box .more,.reply-list-box .more{text-align: center;font-size: 16px;color:#ea2413 ;cursor: pointer;line-height: 60px;
      background: #f5f5f5;
      margin-top: 20px;}
  .best-answer{color:#14191e;font-size: 14px;color: #7d7a7a;margin-top: 10px;margin-left:  67px;line-height: 24px;margin-bottom:  15px;}
  .best-answer .best-answer-content{font-size:14px;line-height:18px;margin-top: 10px;}
  .best-answer .txt{color: #537393;}

  .pl .user-box span{font-size: 16px;}
  .pl .content-div{ font-size: 14px;color: #14191e;}
  .pl {cursor: auto;}

  .reply-div .root{/* margin-top: -20px; */font-size: 14px;height: 60px;line-height: 60px;border-bottom: 1px solid #d8d8d8;}
  .reply-div .root span{    font-family: monospace;    margin: 0 5px;}
  .comment-info{border-bottom: 1px solid #d8d8d8;padding:  20px 0;}
  .type-title{font-size: 16px;color: #333; margin-bottom: 20px;}
  .replybtn{cursor:pointer;display: inline-block;width: 90px;margin-top: 15px;margin-left: 67px;font-size: 14px;height: 36px;line-height: 36px;background: #e51c23;color: #fff;border-radius: 18px;padding: 0;border: none;outline: none;}
  .reply-box{margin: 0 ;display: block;background:#fff;}
  .reply-dialog .el-dialog__header,.choose-type-dialog .el-dialog__header {padding: 0;}
  .reply-dialog .el-dialog__body,.choose-type-dialog .el-dialog__body{padding: 0 ;}
  .reply-box .title,.choose-type-box .title{font-size: 18px;line-height: 60px;height: 60px;color:#333;text-indent: 20px;border-bottom: 1px solid #e7e7eb;}
  .reply-dialog .close,.choose-type-box .close{float: right;margin-right: 20px;line-height: 60px;cursor: pointer;}
  .reply-dialog .close img,.choose-type-box .close img{width: 24px;vertical-align: middle;}
  .reply-box .comment-info{margin: 0 20px;border: none;}
  .reply-dialog .contentbox{background: #f7f8fc;overflow: hidden;}
  .reply-dialog .contentbox textarea{height: 124px;width: 660px;border: 1px solid #e8e8e8;resize: none;padding: 15px 10px;outline: none;line-height: 20px;}
  .reply-dialog .contentbox .title{height: 54px;line-height: 54px;font-size: 18px;color: #333 ;border: none;}
  .text-box{display: inline-block;margin:0 20px;position: relative;height: 155px;}
  .len{position: absolute;right:10px;bottom: 7px;}
  .reply-list-box .m-div{position:relative}
  .reply-list-box .time{position:absolute;color: #999;font-size: 14px;right:0;bottom:0}
  .reply-dialog button{background: #e51c23;float: right;width: 96px;height: 36px;line-height: 36px;font-size: 14px;color: #fff;margin: 10px 25px 17px;border: none;padding:  0;border-radius: 18px;cursor: pointer;}
  .reply-dialog button:disabled{opacity:0.6}
  .reply-list-box .comment-info{position: relative;}
  .reply-list-box .bestanswer-ico{position:absolute;right:0;top: 0;}
  .reply-list-box .t-div {margin-bottom: 20px;}
  .setbestanswer-btn{    cursor: pointer; margin-top: 30px; border:2px solid #e51c23;background: #fff;outline: none;  color: #e51c23;font-size: 14px;border-radius: 16px; margin-left: 67px;width: 138px;padding: 5px 0;}

  .choose-type-box{overflow: hidden;}
  .btn-list{margin: 30px 25px;}
  .choose-item{height:126px;padding:  0 28px;cursor: pointer;}
  .choose-item:hover{background:#f7f8fc;}
  .btn-list .imgbox{    text-align:  center;  line-height:  100px;height:100px;margin-top: 13px;display:  inline-block; border-radius: 6px; width:  100px;margin-right:  25px;}
  .btn-list .imgbox img{vertical-align: middle;}
  .btn-list .imgbox.type1{
      background:  #7ca1cb;
  }
  .btn-list .r{
      display:  inline-block;
      width:  450px;
      vertical-align:  middle;
  }
  .btn-list .t{
      display:  block;
      line-height:  38px;
      font-size:  18px;
  }
  .btn-list .desc{
      line-height:  24px;
      font-size:  14px;
      color:  #999;
  }
  .btn-list .imgbox.type0{
      background: #f49189;
  }
  .add-comment-div .imgbox-type{width: 24px;height: 24px;border-radius: 4px;text-align: center;line-height: 24px;display:  inline-block;margin-right:  10px;}
  .add-comment-div .imgbox-type img{width: 14px;}
  .add-comment-div .imgbox-type0{background: #f49189;}
  .add-comment-div .imgbox-type1{background:  #7ca1cb;}
  .add-comment-div .root {
      /* margin-top:  -20px; */
      height:  60px;
      line-height:  60px;
      border-bottom: 1px solid #d8d8d8;
      font-size:  14px;
      color:  #333;
  }
  .add-comment-div .root span{
      font-family:  monospace;
      margin:  0 5px;
  }
  .add-comment-div .root a{}
  .add-comment-div .title{
      height:  60px;
      line-height:  60px;
      font-size:  16px;
      color:  #333;
  }
  .add-comment-div  .content-box{
      position:  relative;
      width: 360px;
      height: 260px;
      margin-bottom: 20px;
  }
  .add-comment-div  .content-box span{
      position:  absolute;
      right: 10px;
      bottom: 0;
      color:  #999;
  }
  .add-comment-div  textarea{
      border:  1px solid #e8e8e8;
      resize:  none;
      width: 320px;
      padding: 16px 20px;
      line-height: 24px;
      height: 232px;
      outline: none;
      font-size: 14px;
      color:  #333;
  }
  .add-comment-div .submit-comment-btn:disabled{opacity:0.6}

  .add-comment-div .submit-comment-btn{
      width:  90px;
      padding: 8px 0;
      border:  none;
      border-radius:  18px;
      background: #e51c23;
      color:  #fff;
      float:  right;
      outline: none;
      cursor: pointer;
  }



  /* 直播 */
  .live-cent{width: 1200px;margin: auto;padding: 15px 0;}
  .live-statue-list{border-bottom: 1px solid #B2B2B2;height: 51px;line-height: 39px;margin-bottom: 20px; margin-top: 0px;}
  .live-statue-list a{padding: 5px 20px; cursor: pointer; display: inline-block; font-size: 16px;color: #333;margin-right: 20px;}
  .live-statue-list a.active{border-bottom: 3px solid #e41c23; color: #e41c23;}
  .live-img-box{position: relative;margin-top: 20px;margin-left: 40px;border-radius: 4px;display: inline-block;vertical-align:  top;}
  .live-img-box .shaw{width: 400px;height: 225px;position: absolute;top: 0;left: 0;border-radius: 4px;background: url(/assets/image/opcity45.png);}
  .live-img-box .coverImage{width: 400px;height: 225px;border: 1px solid #eee ;border-radius: 5px;}
  .live-img-box .txt{position: absolute;top: 70px;text-align: center;width: 100%;}
  .live-img-box .title{display: block;font-size: 20px;color: #fff;}
  .live-img-box .statue{display: block; font-size: 12px;color: #fff;margin: 15px auto; background:url(/assetsimages/opcity70.png) ;width: 240px;height: 30px;border-radius: 15px;line-height: 30px;}
  .live-item{background: #fff;height: 265px;overflow: hidden;border-radius: 4px;margin-bottom: 30px;}
  .live-r-div{display: inline-block;margin-left: 30px;margin-top: 20px;width: 700px;height: 230px;}
  .live-r-div .title{
    font-size: 20px;
    color: #333;
    line-height: 24px;
    height: 51px;
    overflow: hidden;
      /* cursor: pointer; */
  }
  .live-r-div .title:hover{
    color:  #024b9a;
  }
  .live-r-div .desc{
      font-size:  14px;
      color:  #999;
      line-height:  21px;
      height: 65px;
      overflow:  hidden;
      margin-bottom: 10px;
  }
  .live-r-div .m{
      font-size:  16px;
      color:  #666;
      margin-bottom: 25px;
      line-height: 18px;
  }
  .live-r-div .b{}
  .live-r-div .b button{ cursor: pointer; padding: 0; outline: none;   border: none;font-size: 16px;color: #fff;min-width: 120px;height: 36px;line-height: 36px;text-align: center;border-radius:  22px;padding: 0 30px;}
  .live-r-div .b button.statue1{background: #c3c7d7;}
  .live-r-div .b button.statue2{background: #014c9c}
  .live-r-div .b .btn_enter{color: #ea2413;border: 1px solid #ea2413;background: #fff;margin-left: 20px;}
  .live-r-div .b .btn_enter:disabled{color: #ea2413;border: 1px solid #ea2413;background: #fff;}
  .live-r-div .lecturer{
      margin-right:  40px;
      display: block;
      margin-bottom: 10px;
  }
  .live-r-div .m img{vertical-align: middle;margin-top: -3px;margin-right: 6px;}
  .live-r-div .usercount{
      display: inline-block;
      border-right: 1px solid #e7e7eb;
      padding-right: 30px;
  }
  .live-r-div .time{
      padding-left: 30px;
  }
  .live-cent .more{width: 100%;border-radius: 4px;margin-top: 20px;height: 64px;line-height: 64px;color: #333;font-size: 18px;background: #fff;text-align:  center;}
  .fixright .live-btn{
      width:  90px;
      display: inline-block;
      line-height: 14px;
      padding: 11px 0;
      border: none;
      margin-left:  20px;
      vertical-align:  middle;
      background:linear-gradient( to right,#005dc2,#024b9a);
      color:  #fff;
      font-size:  14px;
      border-radius:18px;
      margin-right: 12px;
  }

  .footer_div {
    /* height: 115px; */
    background: #001C2C;
  }

  .footer_div .footer_bg {
    /* line-height: 115px; */
   padding: 50px 0 30px 0;
    width: 1280px;
    margin: 0 auto;
  }

  .footer_div .info_footer {
    position: relative;
    width: 100%;
    margin: 0 auto;
    display: inline-block;
    /* text-align: center; */
  }

  .footer_div .footer_bg .box {
    box-shadow: 0px 10px 50px 0px rgb(0 0 0 / 10%);
    background: #fff;
    position: absolute;
    bottom: 65px;
    left: calc(50% + 64px);
    width: 225px;
    height: 228px;
    text-align: center;
    padding: 10px 0;
    z-index: 3;
  }
  .footer_div .footer_bg .qrcode {
    position: relative;
  }
  .footer_div .footer_bg .box span {
        font-size: 14px;
      display: block;
      line-height: 40px;
  }
  .footer_div .footer_bg .box .jue {
    width: 15px;
      height: 15px;
      position: absolute;
      transform: rotate(45deg);
      z-index: 9;
      background: #fff;
      left: 99px;
      bottom: -7px;
  }
  .courseitem .star-p-list{display: block;margin-top: 15px;margin: 15px 20px;}
  .courseitem .star-p-list img{     width: 20px; height: 20px;border: none;  margin: 0;vertical-align: middle;}
  .courseitem .star-p-list .score_txt{font-size: 16px;color: #d83601;vertical-align: middle;margin-left: 10px;}
  .courseitem .star-p-list  .star-a-item{vertical-align: middle;}
  .joincount {color: #999;display: inline-block;vertical-align: middle;width: 30%;font-size: 14px;}
  .joincount img{vertical-align: middle;height: 14px;}
  .joincount span{vertical-align: middle;}
  .classhour{color: #999;display: inline-block;vertical-align: middle;width: 23%;/* border-left: 1px solid #999; */text-align: right;height: 14px;line-height: 14px;font-size: 14px;}
  .RecommandCourse .tabs span{
      font-size: 18px;
      margin-left: 45px;
      color: #666;
      padding-bottom: 11px;
      cursor: pointer;
      text-align: center;
  }
  .RecommandCourse .tabs span.active{
      color: #024b9a;
      border-bottom: 2px solid #024b9a;
  }
  .RecommandCourse .tabs{/* margin-left: 20px; */}
  .Recommand-live-box{max-width: 1200px;    margin: auto;width: 100%;}
  .Recommand-live-box h2 a{position:absolute;right: 16px;font-size: 16px;bottom: 7px;color: #666;cursor:pointer}
  .Recommand-live-box h2{font-size: 24px;font-weight: normal;border-bottom: 1px solid #ccc;height: 40px;position: relative;}
  .courseInfo_right .teacher{/* margin-left: 20px; */font-size: 14px;line-height: 16px;color:#999;vertical-align: middle;/* width: 33%; */display: inline-block;margin-right: 37px;}
  .courseInfo_right .teacher svg{vertical-align:middle;/* margin-top: -3px; */}
  .courseInfo_right .teacher span{vertical-align:middle;}

  .courseInfo_right .star-p-list{display: block;/* margin-top: 15px; */margin-bottom: 10px;margin-top: 10px;}
  .courseInfo_right .star-p-list img{     width: 20px; height: 20px;border: none;  margin: 0;vertical-align: middle;}
  .courseInfo_right .star-p-list .score_txt{font-size: 16px;color: #d83601;vertical-align: middle;margin-left: 10px;}
  .courseInfo_right .star-p-list  .star-a-item{vertical-align: middle;}


  .courseInfo_right  .state{
    border-radius: 4px;
    width: 65px;
    height: 22px;
    display: inline-block;
    vertical-align: middle;
    line-height: 22px;
    text-align: center;
    /* float: left; */
    /* margin-top: 6px; */
    font-size: 14px;
    /* position: absolute; */
    /* bottom: 65px; */
  }
  .courseInfo_right  .unStarted{
    background: #d4f9d4;
    color: #018b3e;
  }
  .courseInfo_right .statue_span{
      /* position: absolute; */
      /* bottom: 50px; */
      margin-top: 9px;
      display: inline-block;
  }
  .courseInfo_right  .isCompleted{background:#e1e1e1;color:#333}
  .courseInfo_right  .isStudying{background:#ffdccc;color:#ed570e}
  .courseInfo_right  .p-div{margin: 5px 0;}
  .comment_box_right{
    display: inline-block;
    background: #fff;
    width: 400px;
    margin-left: 20px;
    vertical-align: top;
    margin-top: 20px;
    padding-bottom: 50px;
    min-height: 400px;
    overflow: hidden;
    }
    .comment_box_con{margin: 0 20px;}
  .couldLearn-txt{    color: #333;
    display: inline-block;
    padding: 5px 10px;
  border-radius: 4px;
    opacity: 0.8;
    background: #eee;
    font-size: 14px;}
  .hme-message{max-width: 1200px;margin: 0 auto;border-radius:6px ; padding: 3px;background: rgb(254, 249, 230);color: rgb(207, 128, 84);}
  .footer_div .copyright-txt{width: 1000px ;margin:0 auto 15px; line-height: 36px;text-align: center;color: #999;}

  /*mircoVideo*/
  .video-item{width: 270px;height: 320px;position: relative;}
  .video-item .imgpan{height: 100%;}
  .video-item .imgpan .bg-op06{ background: rgb(0,0,0,0.4); display: block; height: 100%;width: 100%;position: absolute; top: 0; left: 0;}
  .video-item .infopan{position: absolute;bottom: 0;border-radius:  0 20px 20px 0;width: 100%;}
  .video-item .infopan .p-div1{margin: 0 15px;}
  .video-item .infopan .p-div{margin: 0;background: rgb(0 0 0 / 50%);overflow: hidden;padding-bottom: 20px;padding: 0 20px 20px;}
  .video-item .infopan .p-div1 .teacher-name{width: 50%;display: inline-block;color: #fff;}
  .video-item .infopan .p-div1 .teacher-name span{vertical-align: middle;margin-left: 5px;}
  .video-item .infopan .p-div1 .teacher-name img{vertical-align: middle;width: 32px;height: 32px;border-radius: 50%;border: 1px solid #fff;}
  .video-item .infopan .p-div1 .time{width: 50%;text-align: right;display: inline-block;color: #fff;}
  .video-item .infopan .p-div .title{font-size: 18px;color: #fff;margin: 10px 0 20px 0;height: 26px;overflow: hidden;white-space: nowrap;text-overflow: ellipsis;}
  .video-item .infopan .p-div .count{width: 33.3%;display: inline-block;text-align: left;}
  .video-item .infopan .p-div .count img{vertical-align: middle;height: 21px;}
  .video-item .infopan .p-div .count span{font-size: 16px;color: #fff;margin-left: 3px;vertical-align: middle;}
  .video-item .icon_play{position: absolute;right: 20px;top: 20px;}

  /*info*/
  .video-body {
    height: 100%;
    width: 100%;
    background: #262626;
  }

  .video-body .top {
    height: 60px;
    line-height: 60px;
    background: #070605;
  }

  .video-body .top .title {
    font-size: 22px;
    color: #fff;
    display: inline-block;
    margin-left: 20px;
    width: calc(100% - 400px);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .video-body .top .search-box {
    float: right;
    margin-right: 20px;
  }

  .video-body .top .search-box span {
    border-bottom: 2px solid #fff;
    padding-bottom: 10px;
  }

  .video-body .top .search-box input {
    border: none;
    background: none;
    outline: none;
    color: #fff;
    vertical-align: middle;
  }

  .video-body .top .search-box img {
    cursor: pointer;
    vertical-align: middle;
    margin-left: 8px;
  }
  .video-body .right {width: 27%;display: inline-block;vertical-align: top;position: absolute;top: 0;height: 100%;overflow: hidden;}

  .video-body .video-con {
    width: 100%;
    height: calc(100% - 60px);
    text-align: center;
    display: inline-block;
    position: relative;
  }

  .video-body video {
    height: 100%;
    max-width: 100%;
    background: #353435 !important;
  }

  .video-body .jw-icon-inline,
  .video-body .jw-icon-tooltip,
  .video-body .jw-slider-horizontal,
  .video-body .jw-text-duration,
  .video-body .jw-text-elapsed {
    height: 12px !important;
    line-height: 12px !important;
  }

  .video-body .jw-skin-bce .jw-controlbar {
    height: 1.5em !important;
  }

  .video-body .jw-skin-bce .jw-controlbar-right-group .jw-icon-playrate .jw-playrate-label {
    line-height: 10px !important;
  }

  .video-body .control-div {
    position: absolute;
    right: 20px;
    bottom: 20px;
    text-align: center;
  }
  .video-body .control-div .arrows{width: 40px;margin: 0 auto 20px;}
  .video-body .control-div .arrow-prev{cursor: pointer; display: block;  background: url(/microVideo/icon_arrow_t_d.png) no-repeat;   background-position: center; width: 40px;height: 48px;line-height: 48px;background-color: #383a48; border-radius:  20px 20px 0 0;}
  .video-body .control-div .arrow-prev:hover{background: url(/microVideo/icon_arrow_t_s.png) no-repeat;background-position: center;background-color: #383a48; }
  .video-body .control-div .arrow-next{cursor: pointer; display: block;  background: url(/microVideo/icon_arrow_b_d.png) no-repeat;   background-position: center; width: 40px;height: 48px;line-height: 48px;background-color: #383a48; border-radius:  0 0 20px 20px; }
  .video-body .control-div .arrow-next:hover{ background: url(/microVideo/icon_arrow_b_s.png) no-repeat; background-position: center;background-color: #383a48; }
  .video-body .control-div .user{text-align: center;color: #fff;margin-bottom: 30px;font-size: 18px;}
  .video-body .control-div .user img{margin-bottom: 10px;width: 48px;height: 48px;border-radius: 50%;}
  .video-body .nums .top2{    margin-bottom: 18px;text-align: center;}
  .video-body .nums .top2 img{height: 24px;}
  .video-body .nums .top2 .font_t{font-size: 16px;color:#fff;margin-top: 5px;}

  .video-body .right .con{width: 100%;position: relative;height: 100%;}
  .video-body .comment-con-box {
    height: 100%;
    background: #262730;
    position: relative;
    border-radius: 10px;
    margin-bottom: 68px;
  }
  .video-body .comment-con-box .close{    font-size: 30px;
    color: #fff;
    float: right;
    margin-right: 20px;
    margin-top: 10px;
    cursor: pointer;
  }
  .video-body .comment-list {
    /* margin: 10px 20px; */
    height: calc(100% - 110px);
  }
  .video-body .comment-list .el-scrollbar{    height: calc(100% - 15px);
    margin: 0 20px;}
  .video-body .comment-list .item {
    margin-top: 20px;
  }
  .video-body .comment-list .item .m {
    width: calc(100% - 60px);
    display: inline-block;
    margin-left: 15px;
    vertical-align: top;
    font-size: 14px;
    line-height: 24px;
    color: #fff;
  }

  .video-body .comment-list .item .img_commnet_user {
    width: 40px;
    height: 40px;
    vertical-align: middle;
    border-radius: 50%;
  }

  .video-body .comment-list .item .icon_comment_like {
    width: 16px;
    height: 16px;
    vertical-align: middle;
  }

  .video-body .comment-list .item .user-icon-box {
    line-height: 24px;
    vertical-align: middle;
  }

  .video-body .comment-list .item .username {
    font-size: 18px;
    vertical-align: middle;
    color: #d8d8d8;
  }

  .video-body .comment-list .item .time {
    color: #999;
    font-size: 14px;
    vertical-align: middle;
    margin-left: 10px;
  }



  .video-body .comment-list .item  .content {
    margin-top: 10px;
    font-size: 16px;
    color: #f5f5f5;
    padding-bottom: 15px;
    border-bottom: 1px solid #5c5d5e;
    word-break: break-all;
  }

  .video-body .comment-list .item  .icon_arrow_up {
    position: absolute;
    top: 12px;
    left: 10px;
  }
  .video-body .comment-list .item .ft .num {
    color: #999;
    font-size: 16px;
    margin-right: 6px;
    vertical-align: middle;
  }

  .video-body .comment-list .item  .ft {
    vertical-align: middle;
    float: right;
  }
  .video-body .comment-list .item  .ft img{vertical-align: middle;}
  .video-body .comment-con-box .htitle {
    text-align: left;
    height: 56px;
    line-height: 56px;
    color: #fff;
    text-indent: 15px;
    font-size: 20px;
  }
  .video-body .comment-con-box .more{
    text-align: center;
    cursor: pointer;
    font-size: 14px;
    background: #999;
    border-radius: 4px;
    padding: 4px;
    margin: 10px 0;
    }
  .video-body .text-con input {
    /* width: 430px; */
    height: 42px;
    margin: 10px 3%;
    line-height: 42px;
    text-indent: 10px;
    color: #a1a1a1;
    outline: none;
    background: #000;
    border: none;
    padding-right: 56px;
    width: calc(94% - 56px);
    border-radius: 6px;
  }

  .video-body .text-con  .submitbtn{cursor: pointer; display:inline-block;position: absolute; right: 32px; bottom: 20px;width: 24px;height: 24px;background: url(/microVideo/icon_comment_btn_n.png);}
  .video-body .text-con  .submitbtn:hover{background: url(/microVideo/icon_comment_btn_s.png);}
  .video-body .text-con {
    position: fixed;
    bottom: 0;
    width: 100%;
    background: #262730;
    /* left: 0; */
    width: 27%;
    right: 0;
  }

  .video-body .searchbox {position: absolute;right: 10px;top:6px;background: #030304;border-radius: 6px;z-index: 9;padding: 0 10px 20px;height: calc(100% - 51px);width: 215px;}
  .video-body .searchbox .title{color: #fff;font-size: 16px;text-align: left;height: 46px;line-height: 46px;}
  .video-body .searchbox .close{float: right;font-size: 24px;color: #fff;margin-top: 7px;cursor: pointer;}
  .video-body .searchbox .item {cursor: pointer; width: 215px;height: 125px;background: #313232;border: 1px solid #4a4b4b;margin-bottom: 15px;position: relative;}
  .video-body .searchbox .item .name{
      height: 26px;
      background: #000;
      color: #fff;
      font-size: 12px;
      line-height: 26px;
      white-space: nowrap;
      /* padding: 0 10px; */
      /* overflow: hidden; */
  }
  .video-body .searchbox .item .name div{width:95%;margin: auto;overflow: hidden;text-overflow: ellipsis;}
  .video-body .searchbox .item img{height: 96px;}
  .video-body .searchbox .item .icon_play {
    position: absolute;
    top: 45px;
    width: 40px;
    height: 40px;
    margin-top: -15px;
    left: 50%;
    margin-left: -15px;
  }
  .video-body .searchbox .el-scrollbar{height: calc(100% - 20px);}


  /*刷题*/
  .exercise-cnt{width:1200px;margin: auto;}
	.tiku-list{background: #fff;padding: 10px 0;}
	.tiku-list {
		text-align: center;
	}
	.tiku-list-box{   background: #fff;
		clear: both;
		height: 300px;margin: auto;}
	.tiku-list-box .el-scrollbar__wrap{    overflow-x: hidden !important;}
	.tiku-list .item {
		height: 40px;
		line-height: 40px;
		border-bottom: 1px solid #eee;
		width: 100%;
		margin: auto;
		color: #333;
	}
	.tiku-list .active{    color: #000;}
	.tiku-list .txt{font-size:16px;display: inline-block;width: 400px;text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    vertical-align: middle;}
	.tiku-list .txt.checked{font-weight:bold}
	.tiku-list .img-span{height:12px;    width: 30px;display: inline-block;vertical-align: middle;    margin-right: 35px;}
	.tiku-list .img-span img{height: 12px;    vertical-align: top;}
	.tiku-tab{  text-align: left; cursor: pointer;
    text-indent: 50px;  margin-top: 20px;  height:60px;line-height: 60px; background: #fff;   border-radius: 6px;}
	.tiku-tab div {display: inline-block;vertical-align: middle;	margin-right: 10px;    text-indent: 0;}

	.tiku-tab img {width: 12px;height: 7px;vertical-align: middle;}
	.report-box{text-align: center;display: inline-block;width: 900px;
    background: #fff;
    margin: 25px 20px 0 0;
    border-radius: 6px;
    padding: 35px 0;}
	.percent-box{display: inline-block;    vertical-align: middle;
    padding: 12px;
    border-radius: 50%;
	margin-left: 80px;
    border: 4px solid #e8eaf4;}
	.total-box{    vertical-align: middle;display: inline-block;    width: 570px;}
	.total-box .item-list{width: 49%;display: inline-block;}
	.total-box .item-list:first-child{border-right: 1px solid #e8eaf4;	}
	.total-box .item{    width: 100%;display: inline-block;text-align: center;}
	.total-box .item .num{font-size: 36px;color: #010101;margin-bottom: 10px;}
	.total-box .item .txt{font-size: 16px;color: #444;}
	.total-box .item .blank{height: 4px; width:50px;background: #1a74ef;margin: 20px auto;}
	.box1{margin: 25px;height: 152px;position: relative;}
	.box-list-right{display: inline-block;width: 280px;vertical-align: middle;background: #fff;border-radius: 6px;}
	.box1 img{width: 100%;height: 100%;    position: absolute; top: 0;   left: 0;}
	.box1 .txt{font-size: 30px;color: #fff;
    position: absolute;
    width: 100%;
    height: 100%;
    z-index: 3;
    text-align: center;
    line-height: 152px;}
	.box2,.box3,.box4{width: 210px;height: 60px;position: relative;margin: 27px  auto;display:flex;cursor: pointer;}
	.box2 img,.box3 img,.box4 img{width: 210px;height: 60px;position: absolute;left: 0;top: 0;}
	.box2 .txt,.box3 .txt,.box4 .txt{ font-size: 18px;color: #fff;text-align: center; position: absolute;    width: 100%; line-height: 60px;   height: 100%;    z-index: 3;}
	.record-list-box{background:#fff;margin-top: 20px;    margin-bottom: 30px;border-radius: 6px;overflow: hidden;}
	.recordlist {border-top: 1px solid #d8d8d8;margin:  0 20px ;}
	.recordlist .item {
		padding: 25px 0;
		background: #fff;
		border-bottom: 1px solid #d8d8d8;
	}

	.recordlist .item .thumb {
		width: 103px;
		height: 103px;
		display: inline-block;
		vertical-align: middle;
	}

	.recordlist .item .right {
		width:750px;
		display: inline-block;
		vertical-align: middle;
		margin-left: 20px;
	}

	.recordlist .item .title {
		font-size: 16px;
		width: 100%;
		color: #333;
		word-break: break-all;
		line-height: 20px;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
		margin-bottom:15px ;
	}
	.recordlist .item .statue {width: 80px;height: 30px;line-height: 30px;text-align: center;border-radius: 4px;margin-top: 20px; font-size: 14px;}
	.recordlist .item .statue0{background: #fbe3d9;color: #e64513;}
	.recordlist .item .statue1{background: #eaeaea;color: #666666;}
	.recordlist .item .txt {
		font-size: 16px;
		color: #666;
	}

	.recordlist .btns {
		vertical-align: middle;
		float: right;
    	margin-top: 30px;
	}

	.btns .btn {
		cursor: pointer;
		border-radius: 18px;
		font-size: 16px;
		display: inline-block;
		color: #024b9b;
		border: 2px solid ;
		width: 120px;
		height:36px;
		text-align: center;
		line-height:36px;

	}
	.btns .btn0{color: #ea2413;border-color: #ea2413;}
	.btns .btn1{color: #024b9b;border-color: #024b9b;}
	.list-top {
		border-top: 3px solid #f3f5fb;
		background: #fff;
		padding: 30px 20px 10px;
	}

	.date-time {
		font-size: 16px;
		margin-bottom: 20px;
	}

	.date-time div {
		display: inline-block;
		font-size: 36px;
		vertical-align: baseline;
	}
	.tabs a{font-size: 14px; display: inline-block; cursor: pointer;width: 90px;height: 32px;line-height: 32px;text-align: center;border-left: 1px solid #d8d8d8;}
	.tabs a:first-child{border: none;}
	.tabs a.active{color: #ea2413;}
	.tk-dialog	.el-dialog__body{padding-top: 0;min-height: 200px;border-top: 1px solid #d8d8d8;}
	.el-calendar-day .temp{height: 100%;width: 100%;text-align: center;line-height: 50px;}
	.el-calendar-day{ padding: 0 !important;  height: 64px !important;}
	.date-dialog .title ,.tk-dialog .title,.day-schedule-dialog .h_title .title{
		font-size: 18px;
    display: inline-block;
    text-align: left;
    vertical-align: middle;
    width: 140px;
    padding-left: 10px;
    border-left: 4px solid #1a74ef;
    height: 24px;
    line-height: 24px;
    margin: auto;
	 vertical-align: middle;
	}
	.date-dialog .el-dialog,.tk-dialog .el-dialog,.day-schedule-dialog .el-dialog {width:730px;padding: 0;background: #fff;    border-radius: 4px;
    overflow: hidden;}
	.tk-dialog .el-dialog{height: 490px;}
.date-dialog  .el-dialog__body,.tk-dialog  .el-dialog__body,.day-schedule-dialog .el-dialog__body{padding: 0;height: 100%;}
.date-dialog  .h_title,.tk-dialog  .h_title,.day-schedule-dialog .h_title{
		font-size: 24px;
    color: #333;
    text-align: left;
    padding: 24px 20px;
	}
  .day-schedule-dialog .h_title{text-align: left;text-indent: 10px;}
	.date-dialog  .close,.tk-dialog .close,.day-schedule-dialog .close{
		display: inline-block;
    vertical-align: middle;
    text-align: center;
    height: 28px;
    line-height: 28px;
    border-radius: 16px;
    cursor: pointer;
    color: #999;
    font-size: 41px;
    float: right;
	}
	.date-dialog  .el-dialog__header,.tk-dialog .el-dialog__header,.day-schedule-dialog  .el-dialog__header{padding:0}

  /*知识点练习*/
  .exercise-cnt1 {
		width: 1200px;
		margin:20px auto 0;

	}
	.exercise-cnt1 .h-title{height:60px;line-height: 60px;text-indent: 30px;background: #fff;border-radius: 4px;margin-top: 20px;}
	.exercise-box1 .item-box{    width: 50%;display: inline-block;}
	.exercise-box1 .item-box:nth-child(2n) .item{
		border: none;
	}
	.exercise-box1{ background: #fff;margin-top: 20px;padding-bottom: 30px;margin-bottom: 30px;border-radius: 4px;}
	.exercise-box1 .item {
		margin: 25px 0;
		cursor: pointer;
		padding: 0  45px;
		background: #fff;
		width: 505px;
		display: inline-block;
		border-right: 1px solid #d8d8d8;
	}
	.exercise-box1 .blank{height: 1px ;background: #d8d8d8;margin: 25px 0;width: 505px;    margin: auto;}
	.exercise-box1 .item img {
		display: inline-block;
		vertical-align: middle;
	}

	.exercise-box1 .item .title {
		color: #37404f;
		font-size: 16px;
		width: 295px;
		display: inline-block;
		vertical-align: middle;
		margin-left: 20px;
	}

	.exercise-box1 .item .num div {
		font-size: 30px;
		display: inline;
	}

	.exercise-box1 .item .num {
		color: #444;
		font-size: 24px;
		display: inline-block;
		vertical-align: middle;
		width: 85x;
		text-align: right;
	}
	.exercise-box1 .item .num span{font-size:16px}

  /*自主练习*/

  .exercise-cnt1 {
		width: 1200px;
		margin: 20px auto 0;
	}

  .exercise-cnt1 .h-title {
		height: 60px;
		line-height: 60px;
		text-indent: 30px;
		background: #fff;
		border-radius: 4px;
		margin-top: 20px;
	}
	.h-title .tkname{display: inline-block;max-width: 600px;}
	.h-title .total-box {
		text-align: center;
		background: #ffff;
		font-size: 16px;
		display: inline-block;
		float: right;
		margin-right: 20px;

	}

	.h-title 	.total-box .item1 {
		display: inline-block;
		margin: 0 10px;
	}

	.h-title 	.total-box .item1 div {
		display: inline;
		font-size: 14px;
	}

	.ti-box {
		text-align: left;
		margin: 20px 0;
		border-radius: 6px;
		background: #fff;

	}

	.ti-box .item2 {
		padding-bottom: 20px;
	}

	.ti-box .item2 .title {
		height: 74px;
		line-height: 74px;
		position: relative;
		margin: 0 20px;
		border-bottom: 1px solid #e8eaf4;
		text-align: left;
    	text-indent: 10px;
		font-size: 18px;
	}
	.ti-box .item2 .title div{display: inline;}
	.ti-box .item2 .score{margin-left: 20px;}
	.ti-box .item2 .score div {color: #1a74ef;}

	.ti-box .item2 .p {
		    height: 48px;
		line-height: 48px;
		display: inline-block;
		margin: 10px 80px 10px 30px;
		font-size: 16px;

	}

	.ti-box .item2 .p div {
		display: inline;
		vertical-align: middle;
	}

	.ti-box .item2 .p .kitem {
		margin: 0 10px;
	}

	.ti-box .item2 .p .txt {
		color: #666;
		cursor: pointer;
	}

	.ti-box .item2 .p input {
		display: inline-block;
		width: 100px;
		vertical-align: middle;
		margin-right: 10px;
		outline: none;
		border: none;
		border-bottom: 1px solid #d8d8d8;
		text-align: center;
		padding: 10px 0;

	}

	.tiku-list {
		background: #fff;
		padding: 10px 0;
	}

	.tiku-list {
		text-align: center;
	}

	.tiku-list .item {
		height: 50px;
		line-height: 50px;
		border-bottom: 1px dotted #eee;
		width: 100%;
		margin: auto;
		color: #333;
	}

	.tiku-list .active {
		color: #000;
	}

	.tiku-list .img-span {
		height: 14px;
		display: inline-block;
		vertical-align: middle;
		margin-right: 15px;
		width: 16px;
	}
	.tiku-list  .txt{display: inline-block;}
	.tiku-list  .txt.checked{font-weight:bold}
	.tiku-list .img-span img {
		height: 14px;
		vertical-align: top;
	}

	.txt-tab {
		height: 40px;
		line-height: 40px;
		background: #fff;
		text-align: center;
		display: inline-block;
	}

	.txt-tab div {
		display: inline-block;
		vertical-align: middle;
		margin-right: 10px;
	}

	.txt-tab img {
		width: 12px;
		height: 7px;
		vertical-align: middle;
	}

	.knowledge-con {
		background: #fff;
		position: relative;
		    height: 100%;
	}

	.knowledge-con .h_title {
		font-size: 24px;
    color: #333;
    text-align: center;
	    padding-top: 30px;
		/* padding-bottom: 20px; */
		margin: 0 20px 30px ;
	}
.knowledge-con .opr-btn{position: absolute;width: 100%;background: #ebeef2;text-align: center;
    bottom: 0;
    padding: 19px 0}
	.knowledge-con .close {
		display: inline-block;
    vertical-align: middle;
    text-align: center;
    height: 28px;
    line-height: 28px;
    border-radius: 16px;
    cursor: pointer;
    color: #999;
    font-size: 41px;
    float: right;
	}

	.knowledge-con .title {
		font-size: 18px;
    display: inline-block;
    text-align: left;
    vertical-align: middle;
    width: 140px;
    padding-left: 10px;
    border-left: 4px solid #1a74ef;
    height: 24px;
    line-height: 24px;
    margin: auto;
	 vertical-align: middle;
	}
	.knowledge-con .cancel-btn {
		 display: inline-block;
		width: 154px;
		vertical-align: middle;
		text-align: center;
		height: 42px;
		vertical-align: middle;
		text-align: center;
		border: 1px solid #d8d8d8;
		line-height: 42px;
		border-radius: 21px;
		background: #f1f1f1;
		font-size: 16px;
		cursor: pointer;
		margin: 0 50px;
	}
	.knowledge-con .confirm-btn {
		width: 154px;
		vertical-align: middle;
		text-align: center;
		height: 42px;
		line-height: 42px;
		border-radius: 21px;
		font-size: 16px;
		color: #fff;
		background-image: -moz-linear-gradient(-90deg, #005dc2 0%, #277bd6 100%);
		background-image: -webkit-linear-gradient(-90deg, #005dc2 0%, #277bd6 100%);
		background-image: -ms-linear-gradient(-90deg, #005dc2 0%, #277bd6 100%);
		background-image: linear-gradient(-90deg, #005dc2 0%, #277bd6 100%);
		cursor: pointer;
		display: inline-block;
		margin: 0 50px;
	}

	.knowledge-box {
		background: #fff;
		clear: both;
		height: 300px;
	}

	.bottom-box {
		background: #fff;
	}

	.bottom-con {
		    height: 64px;
			line-height: 64px;
			margin: 0 30px;
			padding-top: 20px;
			border-top: 1px solid #eee;
	}



	.bottom-box .btn-submit {
		width: 150px;
		height: 42px;
		line-height: 42px;
		border-radius: 32px;
		text-align: center;
		font-size: 20px;
		color: #fff;
		background-image: -moz-linear-gradient(-90deg, #005dc2 0%, #277bd6 100%);
		background-image: -webkit-linear-gradient(-90deg, #005dc2 0%, #277bd6 100%);
		background-image: -ms-linear-gradient(-90deg, #005dc2 0%, #277bd6 100%);
		background-image: linear-gradient(-90deg, #005dc2 0%, #277bd6 100%);
		margin: auto;
		cursor: pointer;
	}
.knowledges-dialog .el-dialog__header{display:none}
.knowledge-box .el-scrollbar__wrap{    overflow-x: hidden !important;}
.knowledges-dialog .el-dialog{width:730px;height: 490px;padding: 0;background: #fff;    border-radius: 4px;
    overflow: hidden;}
.knowledges-dialog  .el-dialog__body{padding: 0;height: 100%;}
/*答题后*/
.exercise-cnt1 {
  width: 1200px;
  margin:20px auto 0;

}
.h-title{height:60px;line-height: 60px;text-indent: 30px;background: #fff;border-radius: 4px;margin-top: 20px;}
.exercise-box{padding-top: 20px;background: #fff;margin-top: 20px;text-align: center;
  overflow: hidden;
  margin-bottom: 20px;
  /* margin: 25px; */
  border-radius: 6px;}
.exercise-total-box .report-box {
  width: 720px;
  margin: auto;
}

.exercise-total-box  .percent-box {
  display: inline-block;
  padding: 12px;
  border-radius: 50%;
  border: 4px solid #e8eaf4;
  /* width: 50%; */
  vertical-align: middle;
  margin-left: 20px;
}

.exercise-total-box  .right-box {
  display: inline-block;
  width: 360px;
  vertical-align: middle;
  margin-left: 25px;
}

.exercise-total-box  .total-box {
  margin: 20px 20px 0;
  border-top: 1px solid #e7e9f3;
  padding: 15px 0;
  width: auto;
}

.exercise-total-box  .report-box .item2 {
  width: 350px;
  display: inline-block;
  text-align: center;
  padding-bottom: 20px;
}



.exercise-total-box  .report-box .total-box .item2 {
  width: 210px;
}

.exercise-total-box  .report-box .right-box .item2 {
  width: 125px;
  display: inline-block;
  padding: 0 22px;
}

.exercise-total-box  .report-box .item2 image {
  width: 62px;
  height: 62px;
  margin-bottom: 30px;
}

.exercise-total-box  .report-box .item2 .num {
  font-size:36px;
  color: #010101;
  margin-bottom: 20px;
}

.exercise-total-box  .report-box .item2 .txt {
  font-size: 16px;
  color: #444;
}

.exercise-total-box  .report-box .item2 .blank {
  height: 4px;
  width: 50px;
  background: #1a74ef;
  margin: 20px auto;
}
.exercise-total-box  .total-box img {margin-bottom: 20px;}
.exercise-total-box  .total-box .item2 .txt{font-size: 18px;}
.exercise-total-box  .total-box .title {
  font-size: 24px;
  color: #333;
  text-align: center;
  margin-bottom: 40px;

}

.exercise-total-box .btn-analysis {
  clear: both;
  color: #024b9b;
  width: 200px;
  height: 42px;
  line-height: 42px;
  border-radius: 40px;
  border: 1px solid #024b9b;
  text-align: center;
  margin: 50px auto 30px;
  font-size: 16px;
cursor: pointer;
}

.exam{
  min-height: 100%;
  background: #edf2f9;
  padding-top: 20px;
}
.exam  .cnt{    margin-left: 280px;
      width: calc(100% - 560px);display: inline-block;margin-left: 288px;

}
.exam  .cnt   .questions-list-box{ background: #fff;border-radius: 10px;
  height: 80%; margin-top: 15px; box-shadow: 0px 6px 16px 0px rgba(93, 102, 111, 0.1);overflow: hidden;}
.exam  .cnt   .title{height: 54px;line-height: 54px;background: #f9fafd;text-indent: 30px;color: #37404f;font-size: 16px ;  font-weight: bold;}
  .test-item-div{line-height:45px;color: #37404f;font-size: 14px;border-bottom: 1px solid #edf2f9;padding: 20px 0;margin: 0 30px;}
  .test-item-div label{display: block;margin-bottom: 20px;}
.xuanxiang-imgs{margin-top: 10px;}
.tigan{
      font-size: 16px;
      font-weight: bold;
      color: #606266;
}
.tihao-list{
      overflow: hidden;
      position: fixed;
      z-index: 9;
      background-color: #fff;
      box-shadow: 0px 6px 16px 0px rgba(93, 102, 111, 0.1);
      border-radius: 10px;
      width: 257px;
      top: 97px;
      margin-left: 15px;
  height: calc(100% - 280px);
  margin-top: 50px;
}
.tihao-list span {
  border-radius: 10px;
  background-color: rgb(232, 239, 251);
  display: inline-block;
  text-align: center;
  line-height: 30px;
  font-size: 12px;
  color: #37404f;
  border: 1px solid #e8effb;
  width: 28px;
  height: 28px;
  margin: 12px 3px;
  cursor: pointer;
}

.tihao-list span.blue {
  background: #0096ff;
  color: #fff;
  border: 1px solid #0096ff;
}

.tihao-list span.active {
  border: 1px solid #ccc;
}

.tihao-list span.green {
  border: 1px solid #02d27a;
  background: #dff9ef;
}

.tihao-list span.red {
  border: 1px solid #fd705b;
  background: #fceae7;
}

.tihao-list span.grey {
  border: 1px solid #dcdcdc;
}

.tihao-list span.dark {
  border: 1px solid #0a61da;
  color: #37404f;
  background: #c9d7eb;
}

.tk-item {
  margin: 0 10px;
  border-bottom: 1px solid #edf2f9;
  padding: 30px 0;
}

.tk-item:last-child {
  border: none;
}

.tk-title {
  position: relative;
  margin: 0;
  height: 50px;
  line-height: 50px;
  text-align: center;
  font-size: 16px;
  background-color: #f9fafd;
}

.tk-title svg {
  position: absolute;
  left: 50%;
  margin-left: -35px;
  top: 50%;
  margin-top: -2px;
}

.tihao-list .title {
  margin: 10px 0;
}
  .submitPage-btn-box{   position: absolute;margin-left: 20px;z-index: 8;
      top: 10px;
      width: 180px;

  }
  .submitPage-btn-box .submitPage-btn {
    width: 100%;
    border-radius: 40px;
    font-size: 14px;
    cursor: pointer;
    color: #fff;
    height: 36px;
    line-height: 36px;
    border: none;
    background-image: -moz-linear-gradient(-90deg, #005dc2 0%, #277bd6 100%);
    background-image: -webkit-linear-gradient(-90deg, #005dc2 0%, #277bd6 100%);
    background-image: -ms-linear-gradient(-90deg, #005dc2 0%, #277bd6 100%);
    background-image: linear-gradient(-90deg, #005dc2 0%, #277bd6 100%);
  }

  .exam  .right-box {
    background-color: #fff;
    width: 230px;
    top: 97px;
    display: inline-block;
    vertical-align: top;
    height: 600px;
    background: none;
    position: fixed;
    height: calc(100% - 120px);
    min-height: 550px;
    float: right;
    margin-top: 40px;
    vertical-align: top;

  }


  .exam  .svg-span {
    display: inline-block;
    width: 26px;
    height: 26px;
    line-height: 24px;
    text-align: center;
    border-radius: 5px;
    margin-right: 10px;
    vertical-align: middle;
  }


  .exam  .svg-txt {
    vertical-align: middle;
  }


  .exam  .svg-span.wrong {
    background: #fd705b;
  }


  .exam  .svg-span.right {
    background: #34d570;
  }


  .exam   .el-radio-group,

  .exam  .el-checkbox-group {
    padding: 10px 15px;
    display: block;
  }


  .exam  .test-analysis-box {
    background: #edf2f9;
    padding: 10px 15px;
    border-radius: 4px;
    margin-top: 10px;
  }

  .exambody {
    max-width: 1700px;
    margin: auto;
    width: 100%;
    margin-bottom: 50px;
  }

  .submitpage {
    border-radius: 10px;
    overflow: hidden;
  }

  .submitpage .el-dialog {
    width: 600px;
    height: 360px;
  }

  .submitpage .el-dialog__header {
    display: none
  }

  .submitpage .el-dialog__body {
    padding: 0 20px;
  }

  .submitpage .col-span4 {
    display: inline-block;
    vertical-align: middle;
  }

  .submitpage .col-span8 {
    display: inline-block;
    vertical-align: middle;
  }
  .submitpage .el-dialog {width: 600px;height: 360px; border-radius: 4px;overflow: hidden;}
  .submitpage .submit-page-box{margin: 30px ;}
	.submitpage .el-dialog__header{display:none}
	.submitpage .el-dialog__body {padding:  0 ;}
  .submitpage .close {
		display: inline-block;
    vertical-align: middle;
    text-align: center;
    height: 28px;
    line-height: 28px;
    border-radius: 16px;
    cursor: pointer;
    color: #999;
    font-size: 41px;
    float: right;
	}

  .submitpage .title{
    font-size: 18px;
    display: inline-block;
    text-align: left;
    vertical-align: middle;
    width: 140px;
    padding-left: 10px;
    border-left: 4px solid #1a74ef;
    height: 24px;
    line-height: 24px;
    margin:0 auto 30px;
	 vertical-align: middle;}

  .submitpage .p {
    margin: 0 25px;
    line-height: 32px;
    margin-bottom: 20px;
  }

  .submitpage .code-box {
    margin: 0 25px;
    margin-bottom: 20px;
    height: 25px;
    line-height: 25px;
    outline: none;
  }

  .submitpage .codetxt {
    width: 100%;
    outline: none;
    height: 25px;
    border: none;
    text-align: center;
    border-bottom: 1px solid #333;
  }

  .submitpage .code-opr{margin-top: 50px;
    position: absolute;
    bottom: 0;
    height: 80px;
    line-height: 80px;
    text-align: center;
    text-align: center;
    width: 100%;
    background: #ebeef2;}

  .submitpage canvas {
    background: #eef3f9;
    margin-left: 20px;
  }

  .submitpage .submit_btn {
    cursor: pointer;
    width: 130px;
    height: 42px;
    line-height: 42px;
    text-align: center;
    vertical-align: middle;
    display: inline-block;
    border-radius: 20px;
    border: none;
    margin: 0 10px;
    font-size: 14px;
    color: #fff;
    background-image: -moz-linear-gradient(-90deg, #005dc2 0%, #277bd6 100%);
    background-image: -webkit-linear-gradient(-90deg, #005dc2 0%, #277bd6 100%);
    background-image: -ms-linear-gradient(-90deg, #005dc2 0%, #277bd6 100%);
    background-image: linear-gradient(-90deg, #005dc2 0%, #277bd6 100%);
  }

  .submitpage .continue_btn {
    cursor: pointer;
    font-size: 14px;
    width: 130px;
    height: 42px;
    line-height: 42px;
    text-align: center;
    background: #9fa9b9;
    vertical-align: middle;
    display: inline-block;
    border-radius: 20px;
    border: none;
    margin: 0 10px;
    color: #fff;
  }


  .exam-title {
    display: inline-block;
    margin-left: 80px;
    width: calc(100% - 500px);
    text-align: left;
    max-width: 1200px;
    white-space: nowrap;
  }

  .exam-title .svg-span {
    width: 30px;
    height: 30px;
    border-radius: 15px;
    background: #e8effb;
    vertical-align: middle;
    line-height: 30px;
    text-align: center;
  }

  .exam-title .examname {
    font-size: 18px;
    vertical-align: middle;
  }


  .blank-item input {
    height: 42px;
    line-height: 42px;
    border: none;
    border-bottom: 1px solid #ccc;
    width: 150px;
    padding: 0 10px;
    text-align: center;
  }

  .blank-item .blank_answer {
    height: 36px;
    line-height: 36px;
    display: inline-block;
    padding: 0 10px;
    border-bottom: 1px solid #aaa;
    margin: 0 10px;
    vertical-align: middle;
    min-width: 80px;
  }

  .blank-item .tigan {
    line-height: 48px;
  }


  .expand-item textarea {
    display: block;
    width: 700px;
    height: 120px;
    resize: none;
    border: 1px solid #ddd;
    padding: 10px 20px;
    border-radius: 4px;
  }

  .expand-item .expand-answer {
    display: block;
    width: 600px;
    min-height: 120px;
    resize: none;
    border: 1px solid #ddd;
    padding: 10px 20px;
    border-radius: 4px;
  }

  .exam .lefttime {
    width: 180px;
    border-radius: 10px;
    background-color: #e8effb;
    top: 97px;
    margin-left: 20px;
    background: #fff;
    padding-top: 20px;
    margin-top: 10px;;
  }

  .lefttime span {
    display: inline-block;
    text-align: center;
    font-size: 16px;
  }

  .lefttime .span1 {
    border-bottom: 1px solid #edf2f9;
    width: 80%;
    margin-left: 10%;
  }

  .lefttime .span1 .txt {
    color: #ff0000;
    font-size: 24px;
    margin: 20px;
    display: block;
  }

  .lefttime .span2 {
    display: block;
    width: 100%;
    margin-top: 20px;
  }

  .lefttime .span2 .timelong {
    font-size: 24px;
    display: block;
    padding: 20px 0;
    border-bottom: 1px solid #edf2f9;
    width: 80%;
    margin-left: 10%;
  }

  .lefttime .span2 .txt {
    display: inline-block;
    width: 100%;
    font-size: 16px;
    color: #37404f;
    margin-top: 20px;
  }

  .lefttime .span2 .process-box {
    width: 140px;
    height: 6px;
    display: inline-block;
    margin: 20px auto;
    background: #edf2f9;
    border-radius: 3px;
    overflow: hidden;
    position: relative;
  }

  .lefttime .span2 .process-box span {
    background-color: #0096ff;
    display: inline-block;
    border-radius: 3px;
    height: 6px;
    position: absolute;
    left: 0;
  }


  .lefttime .span3 {
    text-align: center;
    display: inline-block;
    width: 100%;
    padding: 0 0 50px 0;
  }

  .lefttime .span3 .title {
    height: 60px;
    line-height: 60px;
  }

  .lefttime .span3 .itemscore {
    font-size: 16px;
    color: #37404f;
    height: 40px;
    line-height: 40px;
  }

  .lefttime .span3 .itemscore span {
    display: inline-block;
    width: 45%;
  }
.tihao-list .el-scrollbar__wrap{    overflow-x: hidden !important;}
.exam .el-radio__input.is-disabled.is-checked .el-radio__inner{border-color: #409EFF;
  background: #409EFF;}

.exam  .el-radio__input.is-disabled.is-checked .el-radio__inner::after{background:#fff}
.exam   .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner {border-color: #409EFF;
  background: #409EFF;}
  .exam   .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner::after {border-color: #fff;}
  .exam   .el-radio, .el-radio__input ,.exam  .el-checkbox,.el-checkbox__input{
  word-break: break-all;
  white-space: pre-wrap;
  line-height: 42px;
}
.exam   .el-checkbox__input,.exam   .el-radio__input{vertical-align: top;line-height: 47px;}
.exam   .el-checkbox__label,.exam   .el-radio__label {line-height: 42px;   width: calc(100% - 30px) !important;}

/*每日练习*/
.tishi-dialog  .el-dialog{
  width: 420px;
  height: 260px;
  border-radius: 4px;
  overflow: hidden;
}

.tishi-dialog  .tishi-box {
width: 380px;
  height: 200px;
margin: auto;
background: #fff;
text-align: center;
overflow: hidden;
position: relative;
}
.tishi-dialog  .tishi-box .title {
color: #000;
font-size: 20px;
margin: 11px auto 35px;
}
.tishi-dialog  .tishi-box .content {
color: #37404f;
font-size: 16px;
text-align: left;
margin: 0 40px;
}
.tishi-dialog  .tishi-box .btns {
border-top: 1px solid #eee;
position: absolute;
left: 0;
bottom: 0;
}
.tishi-dialog  .tishi-box .b-item {
display: inline-block;
width: 187px;
height: 50px;
line-height: 50px;
cursor: pointer;
}
.tishi-dialog  .tishi-box .b-item1 {
color: #666;
font-size: 18px;
border-right: 1px solid #eee;
}
.tishi-dialog  .tishi-box .b-item2 {
color: #0078ff;
font-size: 18px;
}
.submitpage .loading{position: absolute;    text-align: center;
  top: 0;
  width: 100%;
  height: 100%;
  background: rgb(255,255,255,0.6);}
  .submitpage .loading img{height: 50px;
    margin-top: 100px;
    margin-bottom: 40px;}
  .submitpage .loading .txt{font-size:18px}

  .evaluate-cent {
    width: 1200px;
    margin: auto;
    padding: 15px 0;
}
.evaluate-center{background: #fff;border-radius: 4px;padding: 30px 0;    min-height: 750px;}
.evaluate-center .evaluate-info-div{width: 720px;margin: 0 auto;}
.evaluate-center .evaluate-opr{    width: 720px;  margin: 0 auto;  padding: 15px 0;}
.evaluate-center .evaluate-question-box{margin: 0;}
.evaluate-center .evaluate-title{margin: 10px 0; text-align: left;overflow: hidden;}
.evaluate-center .evaluate-num span{font-size: 24px;}
.evaluate-center .evaluate-title .evaluate-num{float: right;vertical-align: middle;line-height: 20px;}
.evaluate-center .evaluate-title .title{vertical-align: middle;}
.evaluate-center .evaluate-result-info{margin-top: 50px;}
.evaluate-center .evaluate-question-box,.evaluate-center .evaluate-result-header{width: 680px;margin: 10px auto;}
.b .evaluate-info{float: right;line-height: 36px;color: #003686;font-size: 16px;}

.my-total-box{margin: 0 20px;}
.my-total-box .user-info-box{height: 114px;background: #eef0f4;margin: 20px 0;}
.my-total-box .user-info-box img{border: 1px solid #c0c6d4;border-radius: 50%;vertical-align: middle;margin: 30px 20px auto 20px}
.my-total-box .user-info-box .fr{display: inline-block;width: 500px;vertical-align: middle;margin-top: 30px;}
.my-total-box .user-info-box .username{margin-bottom: 10px;font-size: 16px;font-weight: bold;}
.my-total-box .user-info-box  .studentId,
.my-total-box .user-info-box .OUName{    display: inline-block;font-size: 14px;
  margin-right: 50px;}
.my-total-box .user-info-box  .studentId span,
.my-total-box .user-info-box .OUName span{color: #666;}
.total-div-item{width: calc(50% - 12px); height: 234px; display: inline-block;margin-right: 20px; border: 1px solid #e1e4e6;border-top: 2px solid #024b9a;border-radius: 4px;vertical-align: middle;}
.total-div-item .title{height: 56px;line-height: 56px;display: inline-block;margin-left: 18px;font-size: 16px;font-weight: bold;margin-bottom: 25px;}
.total-div-item .pr{float: right; line-height: 56px;margin-right: 18px;font-size: 16px;}
.total-div-item .pr span{font-size: 20px;color: #ea2413;font-weight: bold;}
.total-div-item .item{    display: inline-block;
  width: calc(50% - 41px);
  vertical-align: middle;
  line-height: 60px;
  font-size: 16px;
  margin: 0 20px;}
.total-div-item .item .span1{font-size: 30px ;color: #ea2413;margin-right: 5px;}
.total-div-item .item  div{color: #080808;}
.total-div-item .hr{display: inline-block;height: 100px;width: 1px ;background-color: #bfc5d3;vertical-align: middle;}
.total-div-item .item .span2{color: #333;font-size: 30px;margin-right: 5px;}
.my-total-box .total-div{margin-top: 20px;}
.my-total-box .total-div .item2{height: 74px;line-height: 74px;border-bottom: 1px solid #e1e4e6;cursor: pointer;}
.my-total-box .total-div .item2 .left{display: inline-block;width: 280px;text-indent: 20px;color: #080808;font-weight: bold;vertical-align: middle;}
.my-total-box .total-div .item2 .right{display: inline-block;vertical-align: middle;    width: 380px}
.my-total-box .total-div .item2 .right span{font-size: 24px;margin: 0 2px;}
.my-total-box .total-div .item2:last-child{border: none;}
.my-total-box .total-div .item2 .opr{display: inline-block;width: 200px;    text-align: right;vertical-align: middle;}
.my-total-box .total-div .item2 .opr span{vertical-align: middle;color: #024b9a;}
.my-total-box .total-div .item2 .opr  img{vertical-align: middle;margin-left: 5px;}
.type-list{margin: 20px;    min-height:540px;position: relative;}
.type-list .item{border-bottom: 1px solid #d8d8d8;line-height: 34px;
  padding: 10px 0;}
.type-list .item .title{    display: inline-block;
  width: 490px;
  font-size: 16px;
  padding: 0 20px;
  vertical-align: middle;}
.type-list .item .num{display: inline-block;float: right;font-size: 16px;color: #024b9a;    width: 160px;vertical-align:middle}
.tabs-list a{height: 38px;
  border-bottom: 2px solid #fff;
  display: inline-block;
  margin: 0 10px ;
  cursor: pointer;
  padding: 0 10px;
  font-size: 16px;
  line-height: 38px;}
.tabs-list a.active{border-color: #014b99;color: #024b9a;}
.tabs-list {border-bottom: 1px solid #d8d8d8;}
.day-schedule-dialog .h_title .title{font-size: 24px;}
.type-list .item.grey{background: #ebeef2;height: 36px;line-height: 36px;border: none;}
.type-list .item.grey .num{color: #333;}
.type-list .txt{height: 48px;line-height: 48px;text-align: right;font-size: 16px;font-weight: bold;}
.type-list  .my_pagination {}
.myCourseList{margin-top: 20px;margin-left: 5px;min-height: 540px;}
.myCourseList .courseitem{width: 206px;height: 270px;margin:   10px 11px 20px; }
.myCourseList .courseitem .imgpan{height: 112px;}
.myCourseList .courseitem .title{margin:12px 10px 5px ;}
.myCourseList .courseitem .star-p-list{margin: 15px 10px;}
.myCourseList .courseitem .p-div{margin: 0 10px;}
.myCourseList .courseitem .teacher{width: auto;margin-right: 10px;}
.myCourseList .joincount{width: auto;margin-right: 20px;}
.myCourseList .classhour{width: auto;}
.myLiveList{margin:  20px;}
.myLiveList .live-img-box{width: 250px;height: 140px;    margin: 0;}
.myLiveList .live-img-box .coverImage{height: 100%;width: 100%;}
.myLiveList .live-r-div{width: 585px;margin-left: 24px;height: 142px;    margin-top: 0;}
.myLiveList .live-item{height: 142px;    margin: 0 10px 0;padding: 15px 0;border-bottom: 1px solid #d8d8d8;}
.myLiveList .live-r-div .m{margin-bottom: 14px;font-size: 14px;}
/* .myLiveList .live-r-div .title{margin-bottom: 15px;} */
.myLiveList  .live-r-div .b button{height: 34px;line-height: 34px;padding: 0 10px;min-width: 100px;}
.my-live-ft{    position: absolute;top: 8px;  right: 10px;}
.my-live-ft .con{
  cursor: pointer;
}
.my-live-ft{    width: 140px;
  height: 36px;
  line-height: 36px;
  margin: 12px;
  text-align: center;
  top: 10px;
  position: absolute;
  right: 20px;}
.my-live-ft .node{position: relative;background: #fff;display: block;border-radius: 4px;background-color: rgb(255, 255, 255);box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.25);width: 150px;height: 100px;text-align: center;margin-left: -1px;overflow: hidden;}
.my-live-ft svg{
  margin-left: 10px;
  cursor: pointer;
}
.my-live-ft .content{text-align:center;}
.profile_info_box .my-live-ft a{
  margin-right: 0;
  text-align: center;
  background: #fff;
  width: 100px;
  display: block;
  line-height: 30px;
  margin-top: 10px;
  padding: 0;
  height: 36px
}
.my-live-ft a.cur{
  border-bottom: 1px solid #333;
}
.myVideoList{margin: 20px 0 0 10px;min-height: 540px;}
.myVideoList .video-item{width: 206px;height: 240px;    margin:  10px;position: relative;}
.myVideoList  .video-item .infopan .p-div .title{font-size: 16px;margin: 14px 0 12px 0;}
.myVideoList .video-item .infopan .p-div1{margin: 0 15px 5px;}
.myVideoList  .video-item .infopan .p-div .count img{height: 18px;}
.myVideoList .video-item .infopan .p-div .count span{font-size: 14px;}
.myVideoList .icon-shoucang{position: absolute;left: 0;top: 0;    z-index: 9;}

.tk-list-box{display: flex;flex-wrap: wrap;margin-top: 20px;}
.ti-item{
    box-shadow: 0 5px 10px 0 rgba(0,0,0,0.10);
    border-radius: 20px;
    width: 380px;
    height: 170px;
    box-sizing: border-box;
    padding: 20px;
    background: #fff;overflow: hidden;
    display: flex;
    justify-content: space-between;
    margin-right: 30px;
    margin-bottom: 50px;
}
.ti-item:nth-child(3n+3){
    margin-right: 0;
}
.ti-item img{height: 130px;width: 130px;object-fit: cover;display: inline-block;vertical-align: middle;}
.ti-item .fr-div{ }
.ti-item .desc{line-height: 22px;
  font-size: 14px;
  color: #666;
  max-height: 64px;
  overflow: hidden;}
.ti-item .title{
    width: 100%;
    font-size: 16px;
    color: #333333;
    height:60px;
    line-height: 28px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;}
.ti-item button{margin-top:28px;width: 180px;height: 40px;border: 2px solid #096DD9;border-radius: 20px;padding: 0;background: #fff;color: #096DD9;cursor: pointer;}
.ti-item button:disabled{opacity: 0.5;}
.profile_info .title{background: #f5f7f9;height: 64px;line-height: 64px;border-bottom: 1px solid #d8d8d8;display: block;text-align: center;}
/* .myTikuList{margin: 0 20px;} */
.myTikuList .ti-item{border-bottom: 1px solid #d8d8d8;    margin: 0;}
.myTikuList .ti-item img{margin-left: 0;}
.myTikuList .ti-item .fr-div{width: calc(100% - 360px);}
.kc-cat-list {padding-top: 10px;padding-bottom: 30px;}
.kc-cat-list .title{    font-size: 16px;vertical-align: top;
  color: #000;
  font-weight: bold;
  margin-right: 30px;}
.kc-cat-list  .list{    width: 990px;}
.kc-cat-list  .list span{display: inline-block;
  width: 840px;
  vertical-align: top;}
.node-list{
  margin-left: 160px;
  display: inline-block;
  width: 960px;
  background: #f3f5fb;
  padding: 0 10px;
  line-height: 48px;
  margin-top: 20px;
}
.node-list a{line-height: 36px;margin: 0 28px;color: #505968;font-size: 14px;}
.node-list a.active{font-weight: bold;}
.notice-dialog .el-dialog__header{padding: 0;}
.notice-dialog .el-dialog__body{padding: 0;}
.notice-dialog .close{position: absolute;  right: 12px;  top: 0;  font-size: 41px;  color: #fff;  font-weight: normal;cursor: pointer;}
.notice-dialog .h_title{background: url(/images/bg-notice.png) no-repeat;position: relative;    height: 48px;}
.notice-dialog  .el-dialog{width: 720px;    border-radius: 6px;}
.notice-dialog .icon-trumpet{position:absolute;left:0;bottom:0;    width: 80px;}
.notice-dialog .title img{    vertical-align: middle;  height: 20px;  margin-left: 100px;  margin-top: 12px;}
.notice-dialog  .news-con {margin: 0 24px;}
.notice-dialog  .news-con .notice_title{line-height: 32px;font-size: 18px;color: #000;font-weight: bold;margin: 15px 0;}
.notice-dialog  .news-con .con{line-height: 22px;}
.notice-dialog .opr{border-top: 1px solid #d8d8d8;line-height: 54px;    text-align: right;}
.notice-dialog .opr a{color: #024b9a;font-size: 14px;cursor: pointer;}

.imageFlow{width: 1200px ;margin: auto;height: 310px;padding-top: 10px;position: relative;}
.imageFlow .list{position: relative;width: 100%;height: 250px;}
.item-theme{width:240px;height: 135px;display: inline-block;    overflow: hidden;vertical-align: bottom;transition: 0.5s;position: absolute;display: none;}
.item-theme img{width: 100%; height: 100%; object-fit: cover;}
.item-theme.active{
  width: 450px;
  height: 250px;
  -webkit-box-shadow: 0px 0px 3px #0064ff;
  box-shadow: 0px 0px 8px #02254e;
  display: inline-block;
  left: 250px;
  border: 1px solid #02254e;
}
.item-theme.left-item-1{display: inline-block;position: absolute;left: 0;bottom: 0;}
.item-theme.right-item-1{display: inline-block;position: absolute;right:  250px;bottom: 0;}
.item-theme.right-item-2{display: inline-block;position: absolute;right: 0;bottom: 0;}
.imageFlow-bg{
  /* background:linear-gradient( to top,#181f26,#4a5a6c); */
  background: url(/images/live-center-bg.png) bottom  #4a5a6c;
  padding-top: 10px;
  position: relative;  }
.imageFlow-bg .contralbar{     position: absolute;
  bottom: 0;
  width: 1200px;
  margin: auto;
  margin-top: 0;
  left: 50%;
  margin-left: -600px;
  z-index: 19;
  border-top: 3px solid #06111e;
  height: 60px;}
.imageFlow-bg .contralbar .content{width: 100%;font-size: 18px;color: #fff;vertical-align: middle; line-height: 60px; margin-right: 25px;text-indent: 25px;letter-spacing: 2px;}
.imageFlow-bg .live-theme-left{    display: inline-block;cursor: pointer;
  position: absolute;
  bottom: 86px;
  height: 63px;
  line-height: 63px;
  left: -28px;}
.imageFlow-bg .live-theme-right{display: inline-block;cursor: pointer;
  position: absolute;
  bottom: 86px;
  height: 63px;
  line-height: 63px;
  right: -28px;}
.imageFlow-bg .contralbar a{    cursor: pointer;
  vertical-align: middle;
  margin-top: 32px;
  display: inline-block;
  margin-left: 10px;}
.inverted-image-list{height: 60px;overflow: hidden;;position: relative;}
.inverted-image-list .cover{height: 60px;left:0;bottom: 0; background: url(/images/bar-bg.png); position: absolute;    position: absolute;
  width: 100%;
  z-index: 8;}

.inverted-image-list .item-theme{top: 0;opacity: 0.5;}
.inverted-image-list img{ transform: rotateX(180deg); }
.live-date-list .live-item{background: #fff;border: none;margin-bottom: 0;padding: 20px;    position: relative; overflow: visible;}
.live-date-list{padding-left: 40px; }
.live-list-center{width: 1200px;margin: auto ;}
.live-list-center .date-title{position: relative;}
.live-list-center .date-title .txt{
  background: linear-gradient( to right ,#1161bb,#1b35a7);
  width: 120px;height: 36px;line-height: 36px;text-align: center;display: inline-block;font-size: 20px;margin-bottom: 40px;border-radius: 18px;color: #fff;}
.live-list-center .date-dot1{

  display: inline-block;
  top: 8px;
  position: absolute;
  left: -52px;}

.live-list-center .jue{
  width: 0;
  height: 0;
  border-right: 6px solid #1161bb;
  border-top: 4px dashed transparent;
  border-bottom: 6px dashed transparent;
  border-left: 4px dashed transparent;
  padding: 0;
  margin: 0;
  display: inline-block;
  top: 13px;
  position: absolute;
  left: -9px;
}
.live-list-center .date-dot2{
  display: inline-block;
  top: 50%;
  position: absolute;
  left: -58px;
  margin-top: -5px;}
.live-list-center .jue2{
  width: 0;
  height: 0;
  border-right: 8px solid #fff;
  border-top: 8px dashed transparent;
  border-bottom: 8px dashed transparent;
  border-left: 8px dashed transparent;
  padding: 0;
  margin: 0;
  display: inline-block;
  top: 50%;
  position: absolute;
  left: -16px;
  margin-top: -5px;
}
.live-list-center  .live-item-div{padding-bottom: 20px;}
.live-list-center .myLiveList{margin: 0;position: relative;    margin-left: 15px;border-left: 4px solid #dddddb;}
.live-list-center .myLiveList  .live-r-div{width: 790px;}
.live-list-center .htitle{min-height: 75px;line-height: 75px;background: #fff;font-size: 18px;text-indent: 25px;    border-radius: 0px 0px 6px 6px;margin-bottom: 35px;position: relative;}
.live-list-center .btn_myLive{ width: 100px;
  cursor: pointer;
  height: 36px;
  line-height: 36px;
  font-size: 16px;
  display: inline-block;
  margin-top: 20px;
  margin-right: 30px;
  text-align: center;
  background: linear-gradient( to right ,#005dc2,#024b99);
  color: #fff;
  text-indent: 0;
  position: absolute;
  right: 0;
  top: 0px;
  border-radius: 18px;}
.live-list-center .btn-expand{     position: absolute;
  bottom: -36px;
  width: 120px;
  height: 30px;
  line-height: 30px;
  text-align: center;
  text-indent: 0;
  font-size: 14px;
  left: 50%;
  color: #024b9a;
  margin-left: -60px;
  background: #fff;
  border-radius: 0 0 18px 18px;
  cursor: pointer;
  padding-top: 6px;}

.live-list-center .btn-expand  span{vertical-align: middle;}
.live-list-center .btn-expand img{margin-left: 5px;
  vertical-align: middle;}
.live-list-center .cover-span{    display: inline-block;  height: 20px;  background: #ebeef2;  width: 6px;  top: -12px;  position: absolute;left: -6px;}
.live-node-list{text-indent: 0;margin: 0 20px;padding: 0 50px 0 0 ;  height: 0;  overflow: hidden; transition: 0.3s;border-top: 1px solid #fff;    line-height: 42px;}
.live-node-list.expland{border-color: #d8d8d8;height: auto;padding-bottom: 50px;     padding-top: 20px;}
.live-node-list a{color: #505968;font-size: 16px;margin: 0 40px 0 0;cursor: pointer;display: inline-block;}
.course-report-info-box{
    border: 1px solid #e1e4e6;
    border-top: 2px solid #024b9a;
    border-radius: 4px;
    margin: 0 20px;
    line-height: 42px;
}
.live-node-list a.active{color: #024b9a;font-weight: bold;}
.course-report-info-box .info{margin: 0 15px;padding-bottom: 0;border-bottom: 1px solid #bfc5d3;}
.course-report-info-box .info .title{
  height: 42px;line-height:42px;font-size: 16px;
  margin-bottom: 10px;
  border: none;
}
.course-report-info-box .info .item{
  width: 25%;display: inline-block;
  text-align: center;
}
.course-report-info-box .info .num{
font-size: 30px;
}
.course-report-info-box .info .txt{
font-size: 14px;
}
.course-report-info-box .info .txt1{
  display: block;
  margin-top: 0px;
  font-size: 16px;color: #444;
  line-height: 30px;
}

.course-report-info-box .info1{    margin: 0px 15px ;}
.course-report-info-box .info1 .title{border: none;    font-weight: bold;   width: 100%;background: #f3f5fb;height: 36px;line-height: 36px;text-align: center;font-size: 16px;color: #444;border-radius: 4px;margin-bottom: 0;}
.course-report-info-box .info1 .p-div{min-height: 28px;font-size: 14px;}
.course-report-info-box .info1 .p-div .left{width: 40%;display: inline-block;text-indent: 48px;    line-height: 32px;vertical-align: top;}
.course-report-info-box .info1 .p-div .right{    width: 60%;  display: inline-block;  line-height: 32px;  vertical-align: top;}
.course-report-info-box .info-ts{font-size: 16px;color: #024b9a;        margin: 30px 15px;}
.live-list-center-div{position: relative;}
.live-list-center-div .jue-div1{
  width: 0;height: 0;
  border-top: 30px solid #001730;
  border-right: 260px  solid transparent;
  position:absolute;
  top: 0;left: 0;
}
.live-list-center-div .jue-div2{
  width: 0;height: 0;
  border-top: 30px solid #001730;
  border-left: 260px  solid transparent;
  position:absolute;
  top: 0;right: 0;
}
.search-tab-box .tabs{    margin: 0 0 40px 0;
  height: 54px;
  line-height: 54px;
  border-bottom: 1px solid #d8d8d8;}
.search-tab-box .tabs a{    padding: 0 25px;
    height: 50px;
    margin-left: 20px;
    cursor: pointer;
    line-height: 60px;
    font-size: 16px;
    width: auto;
    border: none;}
.search-tab-box .tabs a.active{    display: inline-block;
  color: #024b9a;
  border-bottom: 4px solid #024b9a;
}
.search-tab-box{width: 1200px;margin: 0 auto;}
.search—live—list .live-item {
  background: #fff;
  border: none;
  margin-bottom: 0;
  padding: 20px;
  margin-bottom: 20px;
}
.search—live—list{width: 1200px;margin: auto;}
.search-bg-box {width: 1200px;margin:20px auto;position: relative;}
.search-bg-box  .search_input_box2{width: 720px;
  background: #fff;
  border-radius: 24px;
  position: absolute;
  left: 50%;
  top: 50%;
  margin-left: -360px;
  margin-top: -24px;
  padding: 6px 0;}
.search-bg-box  .search_input_box2 input{width: 580px;
  vertical-align: middle;
  margin-left: 28px;
  border: none;
  outline: none;
  height: 36px;}
.search-bg-box  .search_input_box2 button{cursor: pointer; font-size: 16px;color: #fff; border-radius: 18px;vertical-align: middle; border: none; width: 100px;height: 36px;line-height: 36px;background: linear-gradient( to right ,#005dc2,#024b99);text-align: center;}
.search-bg-box  .search_input_box2 button img{vertical-align: middle;margin-right: 5px;}
.search-bg-box  .search_input_box2 button span{vertical-align: middle;}
.maps{
    margin-bottOm: 20px;
    height: 30px;
    overflow: hidden;
}
.maps>span{
    font-size: 14px;
    color: #333333;
    vertical-align: middle;
}
.maps>img{
    vertical-align: middle;
    padding:0 10px;
}
.notic-top-bg{
    width: 100%;
    height: 120px;
    border-radius: 20px;
    object-fit: cover;

}
.notic-all{
    width: 1280px;
    margin: 40px auto;
    overflow: hidden;
}
.information-box{
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-top: 20px;
    overflow: hidden;
}
.information-left{
    background: #FFFFFF;
    border-radius: 10px;
    flex: 0 0 860px;
    box-sizing: border-box;
    padding: 25px;
    height: auto;
    min-height: 380px;
    overflow: hidden;
}
.information-right{
    flex: 0 0 400px;
    background: #FFFFFF;
    border-radius: 10px;
    box-sizing: border-box;
    padding: 20px;
    height: 380px;
    overflow: hidden;
}
.information-list p{margin: 0;}
    .list-item{
        display: flex;
        justify-content: space-between;
        height: 50px;
        line-height: 50px;
    }
    .list-item .list-title{
        width: 650px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 18px;
        color: #333;
        box-sizing: border-box;
        padding-left: 20px;
        height: 50px;
        line-height: 50px;
        position: relative;
        cursor: pointer;
    }
    .list-item .list-title:hover{
        color: #096DD9;
    }
    .list-item .list-title::before{
        content: '';
        position: absolute;
        left: 0;
        top: 20px;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: #096DD9;
    }
    .list-item .list-time{
        font-size: 14px;
        color: #999999;
        height: 50px;
        line-height: 50px;
    }
    .hot-title{
        height: 50px;
        border-bottom: 1px solid #CCCCCC;
        line-height: 50px;
        overflow: hidden;
    }
    .hot-title>sapn{
        vertical-align: middle;
        font-size: 20px;
        color: #333333;
        float: left;
    }
    .hot-title>img{
        vertical-align: middle;
        float: right;
        height: 18px;
        width: 18px;
        padding-top: 20px;
    }
    .hot-list{
        overflow: hidden;
    }
    .hot-item{
        display: flex;
        justify-content: space-between;
        margin-top: 20px;
        overflow: hidden;
    }
    .hot-img{display: block;width: 120px;height: 66px;}
    .hot-font{
        margin: 0;
        width: 230px;
        font-size: 14px;
        color: #333333;
        line-height: 20px;
        height: 66px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 3; /* 显示行数 */
    }
    .hot-font:hover{color: #096DD9;}

    .card-box{
    display: flex;
    /* justify-content: space-between; */
    flex-wrap: wrap;
}
.card-item{
    width: 380px;
    background: #FFFFFF;
    box-shadow: 0 5px 10px 0 rgba(0,0,0,0.10);
    border-radius: 20px;
    margin-bottom: 24px;
    margin-right: 70px;
    cursor: pointer;
    overflow: hidden;
}
.card-item:nth-child(3n+3) {
    margin-right: 0;
}
.card-img{
    width: 100%;
    height: 160px;
    display: block;
}
.card-class-box{
    padding:0 20px 30px 20px;
    box-sizing: border-box;
}
.card-class-title{font-size: 16px;color: #333333;line-height: 24px;height: 75px;overflow: hidden;
    text-overflow: ellipsis;display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;}
.card-class-info{display: flex;justify-content: space-between;}
.card-class-info-item{}
.card-class-info-item>img{vertical-align: middle;width: 16px;height: 16px;display: inline-block;}
.card-class-info-item>span{vertical-align: middle;font-size: 12px;color: #999;}
.card-teacher-box{
    padding:5px 20px 0 20px;
    box-sizing: border-box;
    position: relative;
}
.card-teacher-box>p{
    font-size: 14px;color: #999;line-height: 22px;height: 75px;overflow: hidden;
    text-overflow: ellipsis;display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
}
.card-teacher-box .teacher-detail{
    position: absolute;
    right:24px;
    bottom: 14px;
    font-size: 14px;
    color: #096DD9;
    cursor: pointer;
}
.biaozhun {
    width: 1280px;
    margin: 40px auto;
    overflow: hidden;
}
.biaozhun-item{
    margin-top: 40px;
    display: flex;
    padding-bottom: 40px;
    flex-wrap: wrap;
    overflow: hidden;
}
.biaozhun-item>img{
    display: block;
    cursor: pointer;
    width: 380px;
    height: 240px;
    margin-right: 70px;
    margin-bottom: 50px;
}
.biaozhun-item>img:nth-child(3n+3) {
    margin-right: 0;
}
.preview_3d {
    width: 100%;
    height: calc(100vh - 120px);
    border: 1px solid #eee;
}
.aibox {
    width: 1200px;
    margin: 40px auto;
    display: flex;
    flex-wrap: wrap;
    overflow: hidden;
}
.ai-item {
    width: 270px;
    height: 320px;
    background: #FFFFFF;
    box-shadow: 0 5px 10px 0 rgba(0,0,0,0.10);
    border-radius: 20px;
    margin-right: 40px;
    margin-bottom: 50px;
    cursor: pointer;
    overflow: hidden;
}
.ai-item:nth-child(4n+4) {
    margin-right: 0;
}
.ai-item>img{
    width: 100%;
    height: 240px;
    display: block;
    object-fit: cover;
}
.ai-item>p{
    padding: 0 20px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.expert-title{
    font-size: 16px;
    color: #333333;
}
.expert-summary{
    font-size: 14px;
    color: #999;
}
.train-head {
    display: block;
    height: 290px;
    width: 100%;
    object-fit: cover;
}
.train-detail{
    margin-top: 30px;
    display: flex;
    flex-wrap: wrap;
}
.train-detail>img{
    display: block;
    width:400px;
    height: 225px;
    object-fit: cover;
    border-radius: 20px;
    margin-right: 40px;
    margin-bottom: 30px;
    overflow: hidden;
    cursor: pointer;
}
.train-detail>img:nth-child(3n+3) {
    margin-right: 0;
}
