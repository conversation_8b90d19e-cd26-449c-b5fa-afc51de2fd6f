import Cookies from 'js-cookie'

const TokenKey = 'CMS-Public-Token'
const PasswordKey = 'CMS-Public-Password'
const PhoneKey = 'CMS-Public-Phone'
const IsNewUserKey = 'CMS-Public-IsNewUser'

export function getToken() {
  return Cookies.get(TokenKey)
}

export function setToken(token) {
  //console.log()
  return Cookies.set(TokenKey, token)
}

export function removeToken() {
  return Cookies.remove(TokenKey)
}

export function getpasswordValid() {
  return Cookies.get(PasswordKey)
}

export function getphoneValid() {
  return Cookies.get(PhoneKey)
}

export function setpasswordValid(flag) {
  return Cookies.set(PasswordKey,flag)
}

export function setphoneValid(flag) {
  return Cookies.set(PhoneKey,flag)
}

export function removepasswordValid() {
  return Cookies.remove(PasswordKey)
}

export function removephoneValid() {
  return Cookies.remove(PhoneKey)
}


export function getIsNewUser() {
  return Cookies.get(IsNewUserKey)
}

export function setIsNewUser(IsNewUser) {
  //console.log()
  return Cookies.set(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IsNewUser)
}

export function removeIsNewUser() {
  return Cookies.remove(IsNewUserKey)
}
