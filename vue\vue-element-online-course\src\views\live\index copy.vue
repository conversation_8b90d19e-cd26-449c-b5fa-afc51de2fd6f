<template>
  <div class="live-cent">
    <div class="menu">
      <div class="live-statue-list">
        <a :class="{active: params.LiveStreamStatue === null ||params.LiveStreamStatue === '' }" @click="changeStatue('')">全部直播</a>
        <!-- <a @click="changeStatue(0)" :class="{active:params.LiveStreamStatue===0}">未开始的直播</a> -->
        <a :class="{ active: params.LiveStreamStatue === 1 }" @click="changeStatue(1)">未开始/进行中的直播</a>
        <a :class="{ active: params.LiveStreamStatue === 2 }" @click="changeStatue(2)">已结束的直播</a>
      </div>
      <span ref="ft" class="ft">
        <span class="con" @click="isShowNode = true">
          <span class="content">{{ isMyLive === 1 ? "我发起的直播" : "我参与的直播" }}</span>
          <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="17px" height="9px">
            <path fill-rule="evenodd" fill="rgb(74, 75, 79)"
              d="M16.094,1.691 L9.137,8.647 C9.120,8.668 9.113,8.694 9.094,8.713 C8.897,8.910 8.638,9.005 8.380,9.002 C8.122,9.005 7.864,8.910 7.667,8.713 C7.647,8.694 7.640,8.668 7.623,8.647 L0.667,1.691 C0.279,1.303 0.279,0.674 0.667,0.287 C1.055,-0.101 1.683,-0.101 2.071,0.287 L8.380,6.595 L14.689,0.287 C15.077,-0.101 15.706,-0.101 16.094,0.287 C16.481,0.674 16.481,1.303 16.094,1.691 Z" />
          </svg>
        </span>
        <span v-if="isShowNode" class="node">
          <a :class="{ cur: isMyLive === 0 }" @click="changeList(0)">我参与的直播</a>
          <a :class="{ cur: isMyLive === 1 }" @click="changeList(1)">我发起的直播</a>
        </span>
      </span>
    </div>

    <div class="LiveList">
      <div v-for="item in LiveList" class="live-item" >
        <div class="live-img-box">
          <img :src="item.liveStream.coverImage" class="coverImage">
          <!-- <div class="shaw"></div> -->
          <div v-if="item.liveStream.liveStreamStatue === 0" class="live_state_default live_state_box_s">
            <img src="/images/icon_play.png" mode="widthFix">
            <span class="live_span">未开始</span>
          </div>
          <div v-if="item.liveStream.liveStreamStatue === 1" class="live_state_default live_state_box_p">
            <!-- <img src="/images/icon_liveing.png" mode="widthFix" /> -->
            <img
              src="data:image/gif;base64,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"
              alt="">
            <span class="live_span">直播中</span>
          </div>
          <div v-if="item.liveStream.liveStreamStatue === 2 && item.liveStream.allowPlayBack" class="live_state_default live_state_box_e live_play_back">
            <img src="/images/icon_play.png" mode="widthFix">
            <span class="live_span">回放</span>
          </div>
          <div v-if="item.liveStream.liveStreamStatue === 2 && !item.liveStream.allowPlayBack" class="live_state_default live_state_box_e">
            <img src="/images/icon_noplay.png" mode="widthFix">
            <span class="live_span">回放已禁</span>
          </div>
          <!-- <div class="txt">
					<span class="title">{{item.liveStreamStatue==2?'【回放】':'【直播】'}}{{item.title}}</span>
					<span class="statue" v-if="item.liveStreamStatue==0">该直播未开始（{{item.startTime | DateFromte('MM月DD日 HH:mm')}} - {{item.endTime | DateFromte('HH:mm')}}）</span>
					<span class="statue" v-if="item.liveStreamStatue==1">正在上课</span>
					<span class="statue" v-if="item.liveStreamStatue==2">该直播结束</span>
				</div> -->
        </div>
        <div class="live-r-div">
          <div class="title">
            {{ item.liveStream.title }}
          </div>
          <div class="desc">
            {{ item.liveStream.description }}
          </div>
          <div class="m">
            <span class="lecturer">主讲人：{{ item.liveStream.lecturer }}</span>
            <span class="usercount"><img src="/images/icon-usercount.png">
              {{ item.liveStream.userCount }} 人</span>
            <span class="time"><img src="/images/icon-timelong.png">
              {{ item.liveStream.startTime | DateFromte("YYYY.MM.DD HH:mm") }} -
              {{
              item.liveStream.endTime | DateFromte("YYYY.MM.DD HH:mm")
              }}</span>
          </div>
          <div class="b">
            <!-- <button
              v-if="item.liveStream.liveStreamStatue == 0"
              class="statue1"
              @click="prewLive(item.liveStream)"
            >
              未开始
            </button> -->
            <div v-if="item.liveStream.liveStreamStatue == 1|| item.liveStream.liveStreamStatue == 0" style="display: inline-block;">
              <button
                class="statue2"
                @click="prewLive(item.liveStream, 0)"
              >
                客户端进入直播
              </button>
              <button style="margin-left: 20px" class="statue2"  @click="prewLive(item.liveStream, 1)">
                网页进入直播
              </button>
            </div>

            <button v-else-if="!item.liveStream.allowPlayBack" class="statue1" disabled="disabled">
              回放已禁
            </button>
            <button v-else-if="!item.liveStream.hasPlayBack" class="statue1" disabled="disabled">
              暂无回放
            </button>
            <button v-else class="statue2"  @click="prewLive(item.liveStream)">
              观看回放
            </button>
            <button class="btn_enter" v-if="(item.liveStream.closeDate==null || (item.liveStream.closeDate!=null&& new Date()< new Date(item.liveStream.closeDate)))&&item.examinationId!=null"  @click="intoExam(item)" >进入考评</button>
            <span class="evaluate-info" >已获取{{item.classHour}}学时</span>
          </div>
        </div>
      </div>
      <NoContent v-if="LiveList.length == 0" />
    </div>
    <div v-if="params.total > LiveList.length" class="more" @click="getMore">
      加载更多
    </div>
    <el-dialog title="提示" :visible.sync="liveDialog" width="30%">
      <div>
        即将打开车博苑直播端，请稍后 <br><br>
        如果您未安装直播客户端，请<a :href="downClient">下载车博苑直播端</a>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="liveDialog = false">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
  import {
    myLiveList,
    myLiveUrl,
    myLiveBackUrl,
    initiateLiveList,
  } from "@/api/user";
  import { getByLive} from "@/api/exam";
  import NoContent from "@/components/NoContent";
  export default {
    name: "Index",
    components: {
      NoContent,
    },
    data () {
      return {
        userLogin: this.$store.getters.token != null ? true : false,
        LiveList: [],
        params: {
          Filter: "",
          LiveStreamStatue: null,
          Sorting: "",
          SkipCount: 0,
          MaxResultCount: 10,
          page: 0,
          total: 0,
          isClient: this.$route.path === '/client/lives' ? true : false,
        },
        liveDialog: false,
        isMac: false,
        downClient: "",
        isMyLive: 0,
        isShowNode: false,
      };
    },
    watch: {
      "$store.state.user.token": {
        handler: function (val, oldVal) {
          this.userLogin = this.$store.getters.token != null ? true : false;
        },
        // deep: true
      },
    },
    beforeDestroy () {
      document.removeEventListener("click", this.clickFun);
    },
    mounted () {
      document.addEventListener("click", this.clickFun);
      this.OSnow();
      if (this.isMyLive) {
        this.getInitiateLiveList();
      } else {
        this.getLiveList();
      }
    },
    methods: {
      clickFun (e) {
        //debugger;
        let _this = this;
        if (
          !!_this.$refs.ft != undefined &&
          e.target != undefined &&
          _this.$refs.ft.contains(e.target)
        )
          return;
        _this.isShowNode = false;
      },
      changeList (t) {
        this.isShowNode = false;
        this.isMyLive = t;
        this.params.page = 0;
        this.LiveList = [];
        if (t) {
          this.getInitiateLiveList();
        } else {
          this.getLiveList();
        }
      },
      changeStatue (statue) {
        this.params.LiveStreamStatue = statue;
        this.params.page = 0;
        this.LiveList = [];
        if (this.isMyLive) {
          this.getInitiateLiveList();
        } else {
          this.getLiveList();
        }
      },
      prewLive (item, t) {
        if (!this.userLogin) {
          this.$store.dispatch("user/toggleloginbox", true);
        } // 未登录
        // else if(item.liveStreamStatue==0){ //未开始
        // 	this.$message.info("直播还未开始，请耐心等待")
        // }
        else if (item.liveStreamStatue == 1 || item.liveStreamStatue == 0) {
          //进行中
          this.intoLive(item.id, t);
        } else if (!item.allowPlayBack) {
          //回放禁止
          this.$message.info("该直播已禁止回放,无法观看");
        } else if (!item.hasPlayBack) {
          //暂无回放
          this.$message.info("该直播暂时未生成回放,无法观看");
        } else {
          //console.log(item.id);
          this.viewBackVideo(item.id);
        }
      },
     async intoExam(item){
        await getByLive({liveId: item.liveStream.id}).then(res=>{
          item.isPublish = res.isPublish
        })
        if(!item.isPublish){ //未发布
          this.$message.info("测评未开始");
        } else{
          this.$router.push({ name: "LiveEvaluate", query: { ExamId: item.examinationId ,name:item.liveStream.title,passScore:item.passScore} });
        }
      },
      getMore () {
        this.params.page++;
        if (this.isMyLive) {
          this.getInitiateLiveList();
        } else {
          this.getLiveList();
        }
      },
      getLiveList () {
        this.params.SkipCount = this.params.page * this.params.MaxResultCount;
        myLiveList(this.params).then((res) => {
          this.params.total = res.totalCount;
          this.LiveList = this.LiveList.concat(res.items);
        });
      },
      getInitiateLiveList () {
        this.params.SkipCount = this.params.page * this.params.MaxResultCount;
        initiateLiveList(this.params).then((res) => {
          this.params.total = res.totalCount;
          this.LiveList = this.LiveList.concat(res.items);
        });
      },
      async viewBackVideo (id) {
        var res = await myLiveBackUrl(id);
        if (this.params.isClient) {
          window.open(res, "_self");
        } else {
          window.open(res, "_blank");
        }
      },
      async intoLive (id, t) {
        var res = await myLiveUrl(id);

        if (this.params.isClient) {
          window.open(res.webUrl, "_self");
        } else if (!this.params.isClient && t === 1) {
          window.open(res.webUrl, "_blank");
        } else {
          res.clientUrl = res.clientUrl.replace('baijiacloud', 'huizhixueyuan');
          this.getHref(res.clientUrl);
        }
        // } else {
        //   res.clientUrl = res.clientUrl.replace('baijiacloud', 'huizhixueyuan');
        //   this.getHref(res.clientUrl);
        // }
        // if(res.userRole==0){ // 0 学生 1 教师
        // 	window.open(res.webUrl,'_blank')
        // } else {
        // 	window.open(res.webUrl,'_blank')
        // 	//this.getHref(res.clientUrl)
        // }
      },
      getbackurl (id) {
        return new Promise((resolve, reject) => {
          myLiveBackUrl({ id: id }).then(
            (res) => {
              resolve(res);
            },
            (error) => { }
          );
        });
      },

      getHref (url) {
        var isFirefox = navigator.userAgent.indexOf("Firefox") > -1; // 是否是火狐  ，火狐内核Gecko
        var isWebKit = navigator.userAgent.indexOf("WebKit") > -1; // 是否是WebKit 内核
        var isChrome = navigator.userAgent.indexOf("Chrome") > -1; // 是否是谷歌
        var isTrident = navigator.userAgent.indexOf("Trident") > -1; // 是否是IE内核
        var isIeL = !!window.ActiveXObject || "ActiveXObject" in window;
        if (isFirefox || isWebKit || isChrome || isTrident || isIeL) {
          // IE和火狐用window.open打开
          // 调起客户端 5秒之后自动关闭调起窗口
          var client = window.open(url);
          setTimeout(function () {
            if (client) {
              client.close(); //关闭新打开的浏览器窗口，避免留下一个空白窗口
            }
          }, 5000);
        } else {
          //其它浏览器使用模拟<a>标签`click`事件的形式调起
          var a = document.createElement("a");
          a.setAttribute("href", url);
          document.body.appendChild(a);
          a.click();
        }
        setTimeout(() => {
          // 5秒之后不管有没有调起都弹窗提示下载客户端
          this.liveDialog = true;
          setTimeout(() => {
            // 5秒之后关闭
            this.liveDialog = false;
          }, 8000);
        }, 5000);
      },
      OSnow () {
        var agent = navigator.userAgent.toLowerCase();
        var isMac = /macintosh|mac os x/i.test(navigator.userAgent);
        if (agent.indexOf("win32") >= 0 || agent.indexOf("wow32") >= 0) {
          this.isMac = false;
          this.downClient =
            //"https://www.baijiayun.com/default/home/<USER>";
            "https://baogang.bj.bcebos.com/installer/Huizhixueyuaninstaller.exe";
        }
        if (agent.indexOf("win64") >= 0 || agent.indexOf("wow64") >= 0) {
          this.isMac = false;
          this.downClient =
            //"https://www.baijiayun.com/default/home/<USER>";
            "https://baogang.bj.bcebos.com/installer/Huizhixueyuaninstaller.exe";
        }
        // if (isMac) {
        //   this.isMac = true;
        //   this.downClient =
        //     "https://www.baijiayun.com/default/home/<USER>";
        // }
      },
    },
  };
</script>
<style scoped>
  #app {
    background: #ebeef2;
  }

  .live_state_default {
    position: absolute;
    top: 0;
    left: 0;
    height: 28px;
    border-radius: 16px;
    background-color: #0096ff;
    display: flex;
    align-items: center;
    margin: 10px 0 0 10px;
    padding: 0 10px;
  }

  .live_state_box_s {
    background-color: rgb(194, 198, 214);
  }

  .live_state_box_p {
    background-color: rgb(255, 51, 51);
  }

  .live_state_box_e {
    background: linear-gradient(to right, #005dc2, #024b9a);
  }

  .live_span {
    margin-left: 5px;
    color: #fff;
    font-size: 13px;
  }

  .live_state_default img {
    width: 15px;
    height: 15px;
  }

  .menu {
    position: relative;
  }

  .ft {
    /* border-bottom: 1px solid #333; */
    position: absolute;
    top: 20px;
    right: 0px;
    /* padding-bottom: 7px; */
  }

  .con {
    cursor: pointer;
    position: relative;
  }

  .node {
    position: absolute;
    background: #fff;
    display: block;
    margin-top: -1px;
    border-radius: 4px;
    background-color: rgb(255, 255, 255);
    box-shadow: 0px 0px 10px 0px rgb(0 0 0 / 25%);
    width: 130px;
    height: 100px;
    text-align: center;
    margin-left: -1px;
    overflow: hidden;
    top: 30px;
  }

  .ft a.cur {
    border-bottom: 1px solid #333;
  }

  .ft a {
    margin-right: 0;
    text-align: center;
    background: #fff;
    width: 110px;
    display: block;
    line-height: 30px;
    margin-left: 10px;
    margin-right: 10px;
    margin-top: 10px;
    cursor: pointer;
  }
</style>
