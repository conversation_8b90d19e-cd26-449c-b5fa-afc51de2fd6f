<template>
  <div class="profile_info">
    <span class="title">我的学习</span>

    <router-link
    :to="{ name: 'ExamCenter'}"
    :class="[routeName == 'ExamCenter'? 'menuItemActive' : 'menuItem']"
  >
    <span class="text">我的考试</span>
  </router-link>
  <router-link
      :to="{ name: 'myTraining' }"
      :class="[routeName == 'myTraining'? 'menuItemActive' : 'menuItem']"
    >
      <span class="text">我的班级</span>
    </router-link>
    <router-link
      :to="{ name: 'myCourse'}"
      :class="[routeName == 'myCourse'? 'menuItemActive' : 'menuItem']"
    >
      <span class="text">我的课程</span>
    </router-link>
    <router-link
      :to="{ name: 'myLive'}"
      :class="[routeName == 'myLive'? 'menuItemActive' : 'menuItem']"
    >
      <span class="text">我的直播</span>
    </router-link>
    <!-- <router-link
      :to="{ name: 'myKnowledge'}"
      :class="[routeName == 'myKnowledge'? 'menuItemActive' : 'menuItem']"
    >
      <span class="text">我的锦囊</span>
    </router-link> -->
    <router-link
      :to="{ name: 'myTiKu'}"
      :class="[routeName == 'myTiKu'? 'menuItemActive' : 'menuItem']"
    >
      <span class="text">我的题库</span>
    </router-link>

  </div>
</template>
<script>
  export default {
    name: "SideMenu",
    data() {
      return {
        showList: 0,
        routeName: "Profile",
        query: ''
      };
    },
    computed: {},
    watch: {
      $route: {
        handler: function (route) {
          this.routeName = route.name;
          this.query = route.query
        },
        immediate: true
      }
    },
    methods: {
      showListClick(objc) {
        this.showList = objc;
      }
    }
  };
</script>
