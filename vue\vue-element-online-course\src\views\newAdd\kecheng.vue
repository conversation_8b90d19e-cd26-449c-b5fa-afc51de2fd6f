<template>
    <div class="notic-all">
        <div class="maps">
            <img src="../../assets/image/zan/map.png" />
            <span>新闻资讯</span>
            <img src="../../assets/image/zan/left.png" />
            <span>课程资讯</span>
        </div>
        <img class="notic-top-bg" src="../../assets/image/zan/kecheng.png" />
        <div class="information-box">
            <div class="information-left">
                <div class="information-list">
                    <div class="list-item" v-for="(item,index) in noticList" :key="index" @click="gotoNoticeInfo(item)">
                        <p class="list-title">{{item.title}}</p>
                        <p class="list-time">{{ item.publishDate| DateFromte("YYYY-MM-DD") }}</p>

                    </div>
                </div>
                <el-pagination
                    v-if="parmas.totalCount > parmas.maxResultCount"
                    class="my_pagination"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="parmas.page"
                    :page-sizes="[10, 20, 60]"
                    :page-size="parmas.MaxResultCount"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="parmas.total"
                    ></el-pagination>
            </div>
            <div class="information-right">
                <div class="hot-title">
                    <span>热门资讯</span>
                    <img src="../../assets/image/zan/hot.png" />
                </div>
                <div class="hot-list">
                    <div class="hot-item" v-for="(item,index) in noticList.slice(0,3)" :key="index" @click="gotoNoticeInfo(item)">
                        <img class="hot-img" :src="item.imgUrl" />
                        <p class="hot-font">{{item.title?item.title:'--'}}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import {getNewsAll} from '@/api/newadd';
export default {
    data() {
        return {
            parmas: {
                SkipCount: 0,
                MaxResultCount: 10,
                Sorting: '',
                category:3,
                page:1,
                total:0,
            },
            noticList:[]
        };
    },
    methods: {
        gotoNoticeInfo(item) {
            const url = this.$router.resolve({
                name: "newsnoticeinfo",
                query: { id: item.id,mapname:'3' },
            }).href;

            window.open(url, "_blank");
        },
        handleSizeChange(val) {
            this.parmas.MaxResultCount = val
            this.getList();
        },
        handleCurrentChange(val) {
            this.parmas.page = val
            this.getList();
        },
        getList(){
            this.parmas.SkipCount=this.parmas.MaxResultCount*(this.parmas.page-1)
            getNewsAll(this.parmas).then(res => {
                this.noticList = res.items;
                this.parmas.total = res.totalCount;
            });
        }
    },
    mounted() {
        this.getList();
    }
};
</script>
<style scoped>

</style>
