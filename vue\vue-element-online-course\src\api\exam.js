import axios from '@/axios'
export function getExams(data) { // 获取考试信息
    return axios.gets('/api/exams/student/exams', data)
}
export function startExams(data) { //开始考试
    return axios.posts('/api/exams/student/start', data)
}
export function submitExams(data) {
    return axios.posts('/api/exams/student/submit', data)
}
export function getResult(data) {
    return axios.gets('/api/exams/student/result', data)
}
export function getCurrentTime(){
    return axios.gets('/api/exams/student/ctime')
}
export function getByLive(data){
  return axios.gets('/api/lms/public/lives/exams/get-by-live',data)
}
