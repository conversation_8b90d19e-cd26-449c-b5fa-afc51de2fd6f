<template>
  <div class="biaozhun">
    <div class="maps">
      <img src="../../assets/image/zan/map.png">
      <span>AI中心</span>
      <img src="../../assets/image/zan/left.png">
      <span>AI专家</span>
    </div>
    <div class="aibox">
      <div
        v-for="item in aiList"
        :key="item.id"
        class="ai-item"
        @click="handleClick(item.url)"
      >
        <img :src="item.picUrl">
        <p class="expert-title">
          {{ item.name }}
        </p>
        <p class="expert-summary">
          {{ item.introduce?item.introduce:'--' }}
        </p>
      </div>
    </div>
    <el-pagination
      v-if="parmas.totalCount > parmas.maxResultCount"
      class="my_pagination"
      :current-page="parmas.page"
      :page-sizes="[10, 20, 60]"
      :page-size="parmas.MaxResultCount"
      layout="total, sizes, prev, pager, next, jumper"
      :total="parmas.total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
    <el-dialog
      append-to-body
      :visible.sync="Dialog"
      width="100%"
      fullscreen
      destroy-on-close
      top="0vh"
    >
      <iframe
        sandbox="allow-forms allow-scripts allow-same-origin"
        allow="microphone; camera"
        style="border:0"
        class="preview_3d"
        :src="iframeUrl"
      />
    </el-dialog>
  </div>
</template>
<script>
import {getAI} from '@/api/newadd'
    export default {
        data(){
            return{
                parmas: {
                    SkipCount: 0,
                    MaxResultCount: 10,
                    Sorting: '',
                    page:1,
                    total:0,
                    type:0
                },
                Dialog:false,
                aiList: [],
                iframeUrl:''
            }
        },
        mounted(){
            this.getList();
        },
        methods:{

            handleSizeChange(val) {
                this.parmas.MaxResultCount = val;
                this.getList();
            },
            handleCurrentChange(val) {
                this.parmas.page = val;
                this.getList();
            },
            handleClick(url) {
                this.iframeUrl = url;
                this.Dialog = true;
            },
            getList(){
                this.parmas.SkipCount=this.parmas.MaxResultCount*(this.parmas.page-1)
                getAI(this.parmas).then(res => {
                    this.aiList = res.items;
                    this.parmas.total = res.totalCount;
                });
            }
        }


    }
</script>
<style lang="scss" scoped>

</style>
