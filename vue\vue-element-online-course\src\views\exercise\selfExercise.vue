<template>
	<div class="exercise-cnt1">
		<el-breadcrumb separator-class="el-icon-arrow-right" class="bread_con">
			<el-breadcrumb-item :to="{ path: '/' }">
				<i class="el-icon-location-outline" />
			</el-breadcrumb-item>
			<el-breadcrumb-item :to="{ path: 'exerciseCenter',query:{ id:postparams.exerciseBankId,name:postparams.exerciseBankName} }">
				课程练习
			</el-breadcrumb-item>
			<el-breadcrumb-item>自主抽题</el-breadcrumb-item>
		</el-breadcrumb>
		<div class="h-title">
			<span>自主抽题</span>
			<div class="tkname">{{postparams.exerciseBankName}}</div>
			<div class="total-box">
				<div class="item1">
					总题目：
					<div>
						{{
						parseInt(postparams.exercisePaperRoles[0].qustionCount) +
						parseInt(postparams.exercisePaperRoles[1].qustionCount) +
						parseInt(postparams.exercisePaperRoles[2].qustionCount)
						}}
					</div>
				</div>
				<div class="item1">
					<div>总分值：</div>
					<div>
						{{
						postparams.exercisePaperRoles[0].qustionCount *
						postparams.exercisePaperRoles[0].qustionScore +
						postparams.exercisePaperRoles[1].qustionCount *
						postparams.exercisePaperRoles[1].qustionScore +
						postparams.exercisePaperRoles[2].qustionCount *
						postparams.exercisePaperRoles[2].qustionScore
						}}
					</div>
				</div>
			</div>
		</div>
		<div class="exercise-box">

			<div class="ti-box">
				<div class="item2" v-for="(item, index) in postparams.exercisePaperRoles">
					<div class="title">
						<div v-if="item.questionType == 0">单选题</div>
						<div v-if="item.questionType == 1">多选题</div>
						<div v-if="item.questionType == 2">判断题</div>
						<div class="score">
							(<div>{{ item.qustionCount * item.qustionScore }}</div>分)
						</div>
					</div>
					<div class="p">
						<div>题目难度：</div>
						<div class="txt-tab" @click="showDifficulty(index)">
							<div class="txt">{{ DifficultyList[item.difficulty].value }}</div>
							<img src="/exercise/arrow-down.png" />
						</div>
					</div>
					<div class="p txt-tab" @click="showKnowledges(index)">
						<div>知识点：</div>
						<div class="txt" v-if="item.knowledges.length == 0">无</div>
						<div class="txt" v-if="item.knowledges.length == KnowledgesList.length">
							全部
						</div>
						<div v-else class="kitem txt" v-for="item1 in item.knowledges">
							{{ item1.name }}
						</div>
						<img src="/exercise/arrow-down.png" />
					</div>
					<br>
					<div class="p">
						<div>抽题数/可抽题数：</div>
						<input class="txt" @input="valid(item.qustionCount, item.questionTotal, index)"
							v-model.trim="item.qustionCount" type="number" />
						<div>/{{ item.questionTotal }}</div>
					</div>
					<div class="p">
						每题分值：<input class="txt" @input="valid1(item.qustionScore, index)"
							v-model.trim="item.qustionScore" type="number" />
						分
					</div>
				</div>
				<div class="bottom-box">
					<div class="bottom-con">
						<div class="btn-submit" @click="submitdata()">提交</div>
					</div>
				</div>
			</div>
		</div>

		<el-dialog  :visible.sync="Difficultyvisible" class="knowledges-dialog"  :show-close="false">
			<div class="knowledge-con">
				<div class="h_title">
					<div class="title">选择题目难度</div>
					<a  class="close" @click="Difficultyvisible=false">&times;</a>
				</div>
				<div class="tiku-list">
					<div class="item" v-for="item in DifficultyList" @click="chooseDifficulty(item.index)">
						<div :class="[postparams.exercisePaperRoles[currentIndex].difficulty ===item.index?'checked txt':'txt']">{{ item.value }}</div>
						<div class="img-span">
							<img v-if="postparams.exercisePaperRoles[currentIndex].difficulty ===item.index" src="/exercise/icon-s.png"/>
						</div>
					</div>
				</div>
			</div>

		</el-dialog>
		<el-dialog :visible.sync="knowledgesvisible" class="knowledges-dialog" :show-close="false">
			<div class="knowledge-con">
				<div class="h_title">
					<div class="title">选择知识点</div>
					<a  class="close" @click="cancle()">&times;</a>
				</div>
				<el-scrollbar class="knowledge-box" scroll-y="true">
					<div class="tiku-list">
						<div class="item" v-for="item in KnowledgesList" @click="chooseKnowledges(item)">
							<div :class="[item.isActived?'checked txt':'txt']">{{ item.name }}</div>
							<div class="img-span">
								<img v-if="item.isActived" src="/exercise/icon-s.png"/>
							</div>
						</div>
					</div>
				</el-scrollbar>
				<div class="opr-btn">
					<div class="cancel-btn" @click="cancle()">取消</div>
					<div class="confirm-btn" @click="cofirm()">确定</div>
				</div>
			</div>
		</el-dialog>
	</div>
</template>
<script>
	import { getQuestions, createPaper, getKnowledges } from "@/api/exercise.js";
	export default {
		data () {
			return {
				exercisePageId: "",
				DifficultyList: [
					{
						index: 0,
						value: "随意",
					},
					{
						index: 1,
						value: "易",
					},
					{
						index: 2,
						value: "较易",
					},
					{
						index: 3,
						value: "适中",
					},
					{
						index: 4,
						value: "偏难",
					},
					{
						index: 5,
						value: "难",
					},
				],
				KnowledgesList: [],
				postparams: {
					exerciseBankId: "",
					exerciseBankName:'',
					exercisePaperRoles: [
						{
							questionType: 0,
							difficulty: 0,
							knowledges: [],
							qustionCount: 0,
							questionTotal: 0, // 总题数
							qustionScore: 2,
						},
						{
							questionType: 1,
							difficulty: 0,
							knowledges: [],
							qustionCount: 0,
							questionTotal: 0, // 总题数
							qustionScore: 2,
						},
						{
							questionType: 2,
							difficulty: 0,
							knowledges: [],
							qustionCount: 0,
							questionTotal: 0, // 总题数
							qustionScore: 2,
						},
					],
				},
				currentIndex: 0,
				knowledgesvisible: false,
				Difficultyvisible: false,
			};
		},
		components: {
			// liuyunoTabs
			// radialindicator
			// skeleton
		},
		created () {
			this.postparams.exerciseBankId = this.$route.query.tkid;
			this.postparams.exerciseBankName = this.$route.query.tkname;
			this.getKnowledges(this.postparams.exerciseBankId);
		},

		methods: {
			getQuestions (data, i) {
				getQuestions(data).then((res) => {
					this.postparams.exercisePaperRoles[i].questionTotal =
						res.questionNumber;
				});
			},
			getKnowledges (id) {
				getKnowledges(id).then((res) => {
					// console.log(res)
					this.KnowledgesList = res.items;
				});
			},
			showDifficulty (index) {
				this.currentIndex = index;
				//this.$refs.Difficulty.open()
				this.Difficultyvisible = true;
			},
			chooseDifficulty (value) {
				this.postparams.exercisePaperRoles[this.currentIndex].difficulty = value;
				// this.$refs.Difficulty.close()
				this.Difficultyvisible = false;
				let Knowledges = []
				this.postparams.exercisePaperRoles[this.currentIndex].knowledges.map(item=>{
					Knowledges.push(item.id)
				})
				//console.log(this.postparams.exercisePaperRoles[this.currentIndex].knowledges)
				let data = {
					Knowledges: Knowledges.join(','),
					Difficulty:  this.postparams.exercisePaperRoles[this.currentIndex].difficulty,
					QuestionType: this.postparams.exercisePaperRoles[this.currentIndex].questionType,
				}
				this.getQuestions(data, this.currentIndex)
			},
			showKnowledges (index) {
				this.currentIndex = index;
				//this.$refs.knowledges.open()
				this.knowledgesvisible = true;
				this.KnowledgesList.map((item) => {
					//console.log(this.postparams.exercisePaperRoles[this.currentIndex].knowledges.findIndex(x=>x == item.id))
					if (
						this.postparams.exercisePaperRoles[
							this.currentIndex
						].knowledges.findIndex((x) => x.id == item.id) >= 0
					)
						item.isActived = true;
					else item.isActived = false;
				});
				//console.log(this.postparams.exercisePaperRoles[this.currentIndex].knowledges)
				//console.log(this.KnowledgesList)
				this.$forceUpdate();
			},
			cofirm () {
				this.postparams.exercisePaperRoles[this.currentIndex].knowledges = [];
				this.knowledgesvisible = false;
				//this.$refs.knowledges.close()
				let Knowledges = [];
				this.KnowledgesList.map((item) => {
					if (item.isActived) {
						this.postparams.exercisePaperRoles[this.currentIndex].knowledges.push(
							item
						);
						Knowledges.push(item.id);
					}
				});
				let data = {
					Knowledges: Knowledges.join(","),
					Difficulty:
						this.postparams.exercisePaperRoles[this.currentIndex].difficulty,
					QuestionType:
						this.postparams.exercisePaperRoles[this.currentIndex].questionType,
				};
				this.getQuestions(data, this.currentIndex);
			},
			cancle () {
				// this.$refs.knowledges.close();
				this.knowledgesvisible = false;
			},
			chooseKnowledges (item) {
				item.isActived = !item.isActived;
				// console.log(this.KnowledgesList)
				this.$forceUpdate();
			},
			valid (inputval, val, index) {
				if (!/^[0-9]*$/.test(inputval)) {
					this.$message.info("请输入数字");
					this.postparams.exercisePaperRoles[index].qustionCount = 0;
				} else if (val != undefined && inputval > val) {
					this.$message.info("抽题数目不能大于可抽题目数");
					this.postparams.exercisePaperRoles[index].qustionCount = 0;
				}
			},
			valid1 (inputval, index) {
				if (!/^[0-9]*$/.test(inputval)) {
					this.$message.info("请输入数字");
					this.postparams.exercisePaperRoles[index].qustionScore = 0;
				}
			},
			submitdata () {
				let totalCount =
					this.postparams.exercisePaperRoles[0].qustionCount +
					this.postparams.exercisePaperRoles[1].qustionCount +
					this.postparams.exercisePaperRoles[2].qustionCount;
				if (totalCount < 1) {
					this.$message.info("总题数必须大于0");
					return;
				}
				//console.log(this.postparams)
				this.postparams.exercisePaperRoles.map((item) => {
					let k = [];
					item.knowledges.map((x) => {
						k.push(x.id);
					});
					//console.log(k)
					item.knowledges = [];
					item.knowledges = k;
					item.qustionCount = parseInt(item.qustionCount);
					item.qustionScore = parseInt(item.qustionScore);
				});
				// console.log(this.postparams)
				// return
				createPaper(this.postparams).then((res) => {
					this.exercisePageId = res.id;
					this.gotoexam(this.exercisePageId);
					if (res.id != undefined) this.$message.success("创建成功");
					else this.$message.error("创建失败");
				});
			},
			gotoexam (id) {
				this.$router.push({
					name: "StartExercise",
					query: {
						examId: id,
            tkid: this.postparams.exerciseBankId ,
            tkName:this.postparams.exerciseBankName
					},
				});
			},
		},
	};
</script>

<style>
</style>
