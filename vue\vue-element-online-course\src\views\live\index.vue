<template>
  <div>
    <div class="imageFlow-bg">
      <div class="imageFlow">
        <div class="list">
          <div
            v-for="(item,index) in themes"
            :class="['item-theme',index==currentIndex?'active':'',index==currentIndex-1 ?'left-item-1':'',index==currentIndex+1 ?'right-item-1':'',index==currentIndex+2 ?'right-item-2':'']"
          >
            <img :src="item.coverImage">
          </div>
        </div>
        <div class="inverted-image-list">
          <div class="cover" />
          <div
            v-for="(item,index) in themes"
            :class="['item-theme',index==currentIndex?'active':'',index==currentIndex-1 ?'left-item-1':'',index==currentIndex+1 ?'right-item-1':'',index==currentIndex+2 ?'right-item-2':'']"
          >
            <img :src="item.coverImage">
          </div>
        </div>
        <a
          class="live-theme-left"
          @click="left"
        >
          <img src="/images/live-theme-left.png">
        </a>
        <a
          class="live-theme-right"
          @click="right"
        >
          <img src="/images/live-theme-right.png">
        </a>
      </div>
      <div class="contralbar">
        <div
          v-if="themes!=undefined&&themes.length>0"
          class="content"
        >
          当前直播: {{ themes[currentIndex].title }}
        </div>
      </div>
    </div>
    <div class="live-list-center-div">
      <div class="jue-div1" />
      <div class="jue-div2" />
      <div class="live-list-center">
        <div
          v-if="themes_cat!=undefined&&themes_cat.length>0"
          class="htitle"
        >
          直播主题分类
          <div
            class="live-node-list"
            :class="{expland:isShowNode!=0}"
          >
            <a
              :class="{'active':themes[currentIndex].id=='CurrentMonth'}"
              @click="changeIndex(themes[1])"
            >本月直播</a>
            <a
              :class="{'active':themes[currentIndex].id=='Alltheme'}"
              @click="changeIndex(themes[0])"
            >全部直播</a>

            <a
              v-for="(item,index) in themes_cat"
              :class="{'active':themes[currentIndex].id==item.id}"
              @click="changeIndex(item)"
            >{{ item.title }}</a>
          </div>
          <a
            class="btn-expand"
            @click="isShowNode=!isShowNode"
          >
            <span> {{ isShowNode==0?'更多分类':'收起' }}</span>
            <img
              v-if="!isShowNode"
              src="/images/arrow-down-list.png"
            >
            <img
              v-else
              src="/images/arrow-up-list.png"
            >
          </a>
          <a
            class="btn_myLive"
            @click="gotoMyLive"
          >我的直播</a>
        </div>
        <div
          v-if="lives.length>0"
          class="myLiveList"
        >
          <span class="cover-span" />
          <div
            v-for="dateitem in lives"
            class="live-date-list"
          >
            <div class="date-title">
              <img
                class="date-dot1"
                src="/images/dot1.png"
              >
              <span class="jue" />
              <span class="txt"> {{ dateitem.date }}</span>
            </div>
            <div
              v-for="item in dateitem.subList"
              class="live-item-div"
            >
              <div class="live-item">
                <span class="jue2" />
                <img
                  class="date-dot2"
                  src="/images/dot2.png"
                >
                <div class="live-img-box">
                  <img
                    :src="item.coverImage"
                    class="coverImage"
                  >
                  <!-- <div class="shaw"></div> -->
                  <div
                    v-if="item.liveStreamStatue === 0"
                    class="live_state_default live_state_box_s"
                  >
                    <img
                      src="/images/icon_play.png"
                      mode="widthFix"
                    >
                    <span class="live_span">未开始</span>
                  </div>
                  <div
                    v-if="item.liveStreamStatue === 1"
                    class="live_state_default live_state_box_p"
                  >
                    <!-- <img src="/images/icon_liveing.png" mode="widthFix" /> -->
                    <img
                      src="data:image/gif;base64,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"
                      alt=""
                    >
                    <span class="live_span">直播中</span>
                  </div>
                  <div
                    v-if="item.liveStreamStatue === 2 && item.allowPlayBack"
                    class="live_state_default live_state_box_e live_play_back"
                  >
                    <img
                      src="/images/icon_play.png"
                      mode="widthFix"
                    >
                    <span class="live_span">回放</span>
                  </div>
                  <div
                    v-if="item.liveStreamStatue === 2 && !item.allowPlayBack"
                    class="live_state_default live_state_box_e"
                  >
                    <img
                      src="/images/icon_noplay.png"
                      mode="widthFix"
                    >
                    <span class="live_span">回放已禁</span>
                  </div>
                </div>
                <div class="live-r-div">
                  <div class="title">
                    {{ item.title }}
                  </div>
                  <!-- <div class="desc">
                    {{ item.liveStream.description }}
                  </div> -->
                  <div class="m">
                    <span class="lecturer">主讲人：{{ item.lecturer }}</span>
                    <span class="usercount"><img src="/images/icon-usercount.png">
                      {{ item.userCount }} 人</span>
                    <span class="time"><img src="/images/icon-timelong.png">
                      {{ item.startTime | DateFromte("YYYY.MM.DD HH:mm") }} -
                      {{
                        item.endTime | DateFromte("YYYY.MM.DD HH:mm")
                      }}</span>
                    <span
                      class="time"
                      style="    border-left: 1px solid #d8d8d8; margin-left: 30px;"
                    > {{ item.classHour }} 课时</span>
                  </div>
                  <div class="b">
                    <div
                      v-if="item.liveStreamStatue == 1|| item.liveStreamStatue == 0"
                      style="display: inline-block;"
                    >
                      <button
                        class="statue2"
                        @click="prewLive(item, 0)"
                      >
                        客户端进入直播
                      </button>
                      <button
                        style="margin-left: 20px"
                        class="statue2"
                        @click="prewLive(item, 1)"
                      >
                        网页进入直播
                      </button>
                    </div>
                    <button
                      v-else-if="!item.allowPlayBack"
                      class="statue1"
                      disabled="disabled"
                    >
                      回放已禁
                    </button>
                    <button
                      v-else-if="!item.hasPlayBack"
                      class="statue1"
                      disabled="disabled"
                    >
                      暂无回放
                    </button>
                    <button
                      v-else
                      class="statue2"
                      @click="prewLive(item)"
                    >
                      观看回放
                    </button>
                    <button
                      v-if="(item.closeDate==null || (item.closeDate!=null&& new Date()< new Date(item.closeDate)))&&item.examinationId!=null"
                      class="btn_enter"
                      @click="intoExam(item)"
                    >
                      进入考评
                    </button>
                    <!-- <span class="evaluate-info" >已获取{{item.classHour}}学时</span> -->
                  </div>
                </div>
              </div>
            </div>
          </div>

          <el-pagination
            v-if="params.total>params.MaxResultCount"
            class="my_pagination"
            :current-page="params.page"
            :page-sizes="[8, 20, 40]"
            :page-size="params.MaxResultCount"
            layout="total, sizes, prev, pager, next, jumper"
            :total="params.total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
        <NoContent v-if="lives.length==0" />
      </div>
    </div>

    <el-dialog
      title="提示"
      :visible.sync="liveDialog"
      width="30%"
    >
      <div>
        即将打开车博苑直播端，请稍后 <br><br>
        如果您未安装直播客户端，请<a :href="downClient">下载车博苑直播端</a>
      </div>
      <span
        slot="footer"
        class="dialog-footer"
      >
        <el-button
          type="primary"
          @click="liveDialog = false"
        >确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
  import {
    getlives,getlivethemes
  } from "@/api/live";
  import { getByLive} from "@/api/exam";
  import {
    myLiveUrl,
    myLiveBackUrl,

  } from "@/api/user";
  // import imf from './imageFlow.js'
  import NoContent from "@/components/NoContent";
  import moment  from "moment";
  export default {
    name: "Index",
    components: {
      NoContent,
    },
    data () {
      return {
        themes:[],
        themes_cat:[],
        lives:[],
        isShowNode:0,
        currentIndex:1,
        userLogin: this.$store.getters.token != null ? true : false,
        liveDialog:false,
        downClient:'',
        params:{
          Filter:'',
          LiveThemeId:'',
          SkipCount:0,
          StartDate:'',
          EndDate:'',
          MaxResultCount:8,
          page:1,
        }
      };
    },

    watch: {
      currentIndex: {
        handler: function (val, oldVal) {
          this.params.page = 1
          this.getlives()
        },
        // deep: true
      },
    },

    mounted () {
      this.getThemes()
      this.OSnow()
    },
    methods: {
      intoExam(item){
        getByLive({liveId: item.id}).then(res=>{
          item.isPublish = res.isPublish
          item.passScore = res.passScore

          if(!item.isPublish){ //未发布
            this.$message.info("测评未开始");
          } else{
            if(res.examPass){
              this.$confirm('您本次考评已经通过，确定需要重新答题吗？',"温馨提示",{
                  confirmButtonText: '确定',
                  cancelButtonText: '取消',
                  type:"warning",
              }).then(() => {
                 this.$router.push({ name: "LiveEvaluate", query: { ExamId: item.examinationId ,name:item.title,passScore:item.passScore} });
              }).catch(() => {
              });
            } else{
              this.$router.push({ name: "LiveEvaluate", query: { ExamId: item.examinationId ,name:item.title,passScore:item.passScore} });
            }

          }
        })
      },
      // ge
      changeIndex(item){
        //this.isShowNode = false
        this.currentIndex = this.themes.findIndex(x=>x.id ==item.id)
      },
      gotoMyLive(){
        this.$router.push({name: 'myLive'})
      },
      getlives(){
        if(this.themes[this.currentIndex].id =='CurrentMonth'){ // 本月直播
          this.params.StartDate = moment().startOf('month').format('YYYY-MM-DD HH:mm:ss'),
          this.params.EndDate = moment().endOf('month').format('YYYY-MM-DD HH:mm:ss'),
          this.params.LiveThemeId = ''
        } else {
          this.params.StartDate ='',
          this.params.EndDate = '',
          this.params.LiveThemeId = this.themes[this.currentIndex].id =='Alltheme'?'':this.themes[this.currentIndex].id
        }
        this.params.SkipCount = this.params.MaxResultCount*(this.params.page-1)
        getlives(this.params).then(res=>{
          this.lives = this.GroupByDate(res.items)
          this.params.total = res.totalCount
        })
      },
      getThemes(){
        getlivethemes({
          SkipCount:0,
          MaxResultCount:100
        }).then(res=>{

          var arr =[]
          res.items.forEach(element=>{
            arr.push(element)
          })
          this.themes_cat = arr
          this.themes = res.items

          this.themes.unshift({
            coverImage: "/images/CurrentMonth.png",
            id: "CurrentMonth",
            title: "本月直播"
          })
          this.themes.unshift({
            coverImage: "/images/Alltheme.png",
            id: "Alltheme",
            title: "全部直播"
          })
          this.getlives()
        })
      },
      left(){
       if(this.currentIndex>0) this.currentIndex--
      },
      right(){
        if(this.currentIndex<this.themes.length-1) this.currentIndex++
      },

      handleSizeChange(val) {
        this.params.MaxResultCount = val
        this.getlives()
      },
      handleCurrentChange(val) {
        this.params.page = val
        this.getlives()
      },
      prewLive (item, t) {
        if (!this.userLogin) {
          this.$store.dispatch("user/toggleloginbox", true);
        } // 未登录
        // else if(item.liveStreamStatue==0){ //未开始
        // 	this.$message.info("直播还未开始，请耐心等待")
        // }
        else if (item.liveStreamStatue == 1 || item.liveStreamStatue == 0) {
          //进行中
          this.intoLive(item.id, t);
        } else if (!item.allowPlayBack) {
          //回放禁止
          this.$message.info("该直播已禁止回放,无法观看");
        } else if (!item.hasPlayBack) {
          //暂无回放
          this.$message.info("该直播暂时未生成回放,无法观看");
        } else {
          //console.log(item.id);
          this.viewBackVideo(item.id);
        }
      },
      async viewBackVideo (id) {
        var res = await myLiveBackUrl(id);
        // this.$router.push({
        //   name: 'LiveCenter'
        // })
         window.open(res, "_blank");

      },
      async intoLive (id, t) {
        var res = await myLiveUrl(id);

        if (!this.params.isClient && t === 1) {
          window.open(res.webUrl, "_blank");
        } else {
          res.clientUrl = res.clientUrl.replace('baijiacloud', 'huizhixueyuan');
          this.getHref(res.clientUrl);
        }
      },
      getbackurl (id) {
        return new Promise((resolve, reject) => {
          myLiveBackUrl({ id: id }).then(
            (res) => {
              resolve(res);
            },
            (error) => { }
          );
        });
      },
      getHref (url) {
        var isFirefox = navigator.userAgent.indexOf("Firefox") > -1; // 是否是火狐  ，火狐内核Gecko
        var isWebKit = navigator.userAgent.indexOf("WebKit") > -1; // 是否是WebKit 内核
        var isChrome = navigator.userAgent.indexOf("Chrome") > -1; // 是否是谷歌
        var isTrident = navigator.userAgent.indexOf("Trident") > -1; // 是否是IE内核
        var isIeL = !!window.ActiveXObject || "ActiveXObject" in window;
        if (isFirefox || isWebKit || isChrome || isTrident || isIeL) {
          // IE和火狐用window.open打开
          // 调起客户端 5秒之后自动关闭调起窗口
          var client = window.open(url);
          setTimeout(function () {
            if (client) {
              client.close(); //关闭新打开的浏览器窗口，避免留下一个空白窗口
            }
          }, 5000);
        } else {
          //其它浏览器使用模拟<a>标签`click`事件的形式调起
          var a = document.createElement("a");
          a.setAttribute("href", url);
          document.body.appendChild(a);
          a.click();
        }
        setTimeout(() => {
          // 5秒之后不管有没有调起都弹窗提示下载客户端
          this.liveDialog = true;
          setTimeout(() => {
            // 5秒之后关闭
            this.liveDialog = false;
          }, 8000);
        }, 5000);
      },
      OSnow () {
        var agent = navigator.userAgent.toLowerCase();
        var isMac = /macintosh|mac os x/i.test(navigator.userAgent);
        if (agent.indexOf("win32") >= 0 || agent.indexOf("wow32") >= 0) {
          this.isMac = false;
          this.downClient =
            // "https://www.baijiayun.com/default/home/<USER>";
            "https://baogang.bj.bcebos.com/installer/Huizhixueyuaninstaller.exe"
        }
        if (agent.indexOf("win64") >= 0 || agent.indexOf("wow64") >= 0) {
          this.isMac = false;
          this.downClient =
            //"https://www.baijiayun.com/default/home/<USER>";
            "https://baogang.bj.bcebos.com/installer/Huizhixueyuaninstaller.exe"
        }
        // if (isMac) {
        //   this.isMac = true;
        //   this.downClient =
        //     "https://www.baijiayun.com/default/home/<USER>";
        // }
      },
      GroupByDate(items){ // 按照类型分组
        let newArr = [];
        items.forEach((item, i) => {
          let index = -1;
          //some用来查找数组中是否存在某个值
          let isExists = newArr.some((newItem, j) => {
            if (moment(item.startTime).format('YYYY-MM') == newItem.date) {
              index = j;
              return true;
            }
          })
          if (!isExists) {
            newArr.push({
              date: moment(item.startTime).format('YYYY-MM'),
              subList: [item]
            })
          } else {
            newArr[index].subList.push(item);
          }

        })
        return newArr
      },
      // getCurrentMonthLives(){
      //   this.params.page = 1
      //   this.params.StartDate ='',
      //   this.params.EndDate = '',
      //   this.params.LiveThemeId = ''
      //   this.getlives()
      // }
    },
  };
</script>
<style scoped>
  #app {
    background: #ebeef2;
  }

  .live_state_default {
    position: absolute;
    top: 0;
    left: 0;
    height: 28px;
    border-radius: 16px;
    background-color: #0096ff;
    display: flex;
    align-items: center;
    margin: 10px 0 0 10px;
    padding: 0 10px;
  }

  .live_state_box_s {
    background-color: rgb(194, 198, 214);
  }

  .live_state_box_p {
    background-color: rgb(255, 51, 51);
  }

  .live_state_box_e {
    background: linear-gradient(to right, #005dc2, #024b9a);
  }

  .live_span {
    margin-left: 5px;
    color: #fff;
    font-size: 13px;
  }

  .live_state_default img {
    width: 15px;
    height: 15px;
  }

  .menu {
    position: relative;
  }

</style>
