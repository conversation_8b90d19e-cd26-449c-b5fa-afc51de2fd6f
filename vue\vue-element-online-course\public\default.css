html {
  height: 100%;
  font-family: "微软雅黑";
  /* min-width: 1240px; */
}
body{height: 100%;}
a {
  text-decoration: none;
}
#app,.layout-box{height: 100%; }
.layout-box{ min-width: 1240px;}
/*登录注册忘记密码*/

div.customDialog {
  max-width: 400px;
  border-radius: 20px;
}
.app-main{
  background: #ebeef2;
  overflow: hidden;
  min-height: 100%;
  }
.carousel-div{max-width: 1200px;margin: auto;padding: 20px 0;}
.carousel-div .el-carousel{height: 360px;width: 100%;border-radius: 20px;overflow: hidden;}
.school_link{display: inline-block;height: 20px;line-height: 0px;padding-right: 15px;border-right: 1px solid #999;cursor: pointer;}
.school_link svg{vertical-align:middle;margin-right: 10px;}
.school_link span{vertical-align:middle;color: #024b9a;font-size: 16px;}
.carousel-div .el-carousel__container{height: 360px;}
.carousel-div .el-image{width: 100%;}
.RecommandCourse{max-width: 1200px;margin: auto;width: 100%;}
.RecommandCourse h2 a{position:absolute;right: 16px;font-size: 16px;bottom: 7px;color: #666;cursor: pointer;}
.RecommandCourse h2{font-size: 24px;font-weight: normal;border-bottom: 1px solid #ccc;height: 40px;position: relative;}
.courseitem{width: 270px;border-radius: 20px;height: 320px;overflow: hidden;background: #fff;cursor: pointer;display: inline-block;margin-left: 35px;margin-bottom: 24px;box-shadow: 0 0 10px #e6e6e6;transition: transform .3s ease-in-out;}
.courseitem:hover {transform: translate3d(0,-10px,0);}
.courseitem:nth-child(4n+1){margin-left: 0;}
.courseitem .imgpan{height: 152px;display: inline-block;overflow: hidden;position: relative;}
.courseitem .imgpan img{width: 100%;}
.courseitem .title{font-size: 16px;color: #333;display: block;line-height: 24px;margin: 12px 20px 5px;height: 48px;overflow: hidden;}
.courseitem .p-div{margin:0 20px;line-height: 16px;font-size: 14px;}
.courseitem .teacher{/* margin-left: 20px; */font-size: 14px;line-height: 16px;color:#999;vertical-align: middle;width: 46%;display: inline-block;}
.courseitem .teacher svg{vertical-align:middle;/* margin-top: -3px; */}
.courseitem .teacher span{vertical-align:middle;}
.courseitem .free{float: right;margin-right: 20px;font-size: 16px;color: #ed570e;letter-spacing: 5px;font-weight: bold;}
.courseitem:hover .title{color: #024b9a;}

.home-live-item  .lecturer{
    font-size: 14px;
    line-height: 16px;
    color: #999;
    /* width: 64%; */
    display: inline-block;
    vertical-align: top;
}
.home-live-item  .usercount{
    font-size: 14px;
    color: #999;
    max-width: 29%;
    /* text-align: right; */
    display: inline-block;
    /* border-left: 1px solid #999; */
    /* padding-left: 15px; */
    height: 16px;
    line-height: 14px;
    /* float: right; */
    vertical-align: top;
    display: block;
    margin-top: 10px;
}
.home-live-item  .usercount img{
    vertical-align: middle;
    height: 14px;
    margin-right: 5px;
}
.home-live-item  .usercount span{
    vertical-align: middle;
}
.home-live-item  .time{
    font-size: 14px;
    color: #999;
    margin: 8px 20px;
    display: inline-block;
}
.home-live-item  .time img{vertical-align:middle}
.home-live-item  .time span{vertical-align:middle;margin-left: 5px;}
.home-live-item   .live_state_default{
    position: absolute;
    top: 0;
}
.CourseList{max-width: 1200px;margin: auto;min-height: 700px;padding-bottom: 40px;}
.catlist{max-width: 1160px;line-height: 60px;border-radius: 4px;margin: 20px auto 40px;background: #fff;padding-left: 40px;position: relative;/* transform: translate3d(0,0,0); */z-index: 9;}
.catlist a{
    font-size: 16px;
    margin-right: 56px;
    display: inline-block;
    cursor: pointer;
}
.catlist a:hover{color: #024b9a;}
.catlist .ft .con{
    cursor: pointer;
}
.catlist .ft{float:right;width: 97px;height: 36px;line-height: 36px;margin: 12px;border-bottom: 1px solid #333;text-align: center;top: 0;position: absolute;right: 0;}
.catlist .ft .node{/* display:none *//* margin-top: 10px; */position: relative;background: #fff;display: block;margin-top: -1px;border-radius: 4px;background-color: rgb(255, 255, 255);box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.25);width: 100px;height: 100px;text-align: center;margin-left: -1px;overflow: hidden;}
.catlist .ft svg{
    margin-left: 10px;
    cursor: pointer;
}
.catlist .ft .content{/* display:inline-block; *//* width:100%; */text-align:center;}
.catlist .ft a{
    margin-right: 0;
    text-align: center;
    background: #fff;
    width: 40px;
    display: block;
    margin-left: 15px;
    line-height: 30px;
    margin-top: 10px;
}
.catlist .ft a.cur{
    border-bottom: 1px solid #333;
}
.catlist a.active{color:#024b9a}
.search_box{display: inline-block;margin-left: 16px;position: relative;margin-right: 25px;}
.search_box .svg{cursor:pointer}
.search_box input{
    width: 147px;
    border: none;
    border-bottom: 1px solid #b2b2b2;
    height: 42px;
    line-height: 42px;
    text-indent: 20px;
    outline: none;
}
.search_box svg{
    position: absolute;
    right: 0;
    bottom: 40px;
    line-height: 100px;
    cursor: pointer;
}
.breadcrumb-box .bread_con {
    height: 80px;
    line-height: 80px;
    font-size: 16px;
    color: #666;
}

/*详情*/
.course_info_box{
    max-width: 1200px;
    margin: auto;
    overflow: hidden;
}
.courseInfo img{
    width: 400px;
    height: 225px;
    margin: 20px;
    border: 1px solid #eee;
}
.courseInfo{background: #fff;border-radius: 4px;height: 265px;width: 780px;display: inline-block;vertical-align: top;}
.courseInfo_right{
    display: inline-block;
    vertical-align: top;
    height: 225px;
    margin-top: 20px;
    position: relative;
    width: 304px;
}
.courseInfo_right img{
    width: auto;
    height: auto;
    border: none;
    margin: 0;
}
.courseInfo_right .name{
    color: #333;
    font-size: 20px;
    line-height: 24px;
    display: block;
    /* height: 82px; */
    margin-bottom: 5px;
    /* overflow: hidden; */
    text-align: justify;
    min-height: 40px;
    max-height: 72px;
    overflow: hidden;
}
.courseInfo_right .lecturer{display: block;margin-top: 20px;}
.courseInfo_right .opr{
    position: absolute;
    /* width: 100%; */
    display: block;
    bottom: 0;
    text-align: right;
    /* position: absolute; */
}
.courseInfo_right .opr .state{
    border-radius: 4px;
    width: 80px;
    height: 30px;
    display: inline-block;
    vertical-align: middle;
    line-height: 30px;
    text-align: center;
    float: left;
    margin-top: 10px;
    font-size: 14px;
}
.courseInfo_right .opr .unStarted{
    background: #d4f9d4;
    color: #018b3e;
}
.courseInfo_right .opr .isCompleted{background:#e1e1e1;color:#333}
.courseInfo_right .opr .isStudying{background:#ffdccc;color:#ed570e}
.courseInfo_right .free{
    color: #024b9a;
    font-size: 24px;
    vertical-align: middle;
    margin: 0 30px;
    letter-spacing: 12px;
}
.courseInfo_right .time{
    font-size: 16px;
    color: #666;
    vertical-align: middle;
    margin-right: 20px;
    margin-top: 20px;
    display: inline-block;
    color: #999;
    font-size: 14px;
}
.courseInfo_right button{
    border-radius: 4px;
    background-image: -moz-linear-gradient( 0deg, #005dc2 0%, #024b9a 100%);
    background-image: -webkit-linear-gradient( 0deg, #005dc2 0%, #024b9a 100%);
    background-image: -ms-linear-gradient( 0deg, #005dc2 0%, #024b9a 100%);
    width: 220px;
    height: 44px;
    border: 0;
    color: #fff;
    cursor: pointer;
    border-radius:  22px;
}
.courseInfo_right button:disabled{
    opacity: 0.6;
    cursor: auto;
}
.courseInfo_right  .notFree_btn{color:#e14500;background:#e3e3e3;cursor: default;}
.courseInfo_right  .isExpired_btn{background:#e1e1e1;color:#666;}

.desc-box{margin-top:20px;background:#fff;width: 780px;border-radius: 4px;padding-bottom: 20px;margin-bottom: 40px;overflow: hidden;display: inline-block;}
.desc-box .tabslist{height: 65px;line-height: 65px;margin: 0 20px;border-bottom: 1px solid #d8d8d8;}
.desc-box .tabslist a{
    font-size: 16px;
    margin: 0 20px;
     display: inline-block;
    height: 62px;
    padding: 0 15px;
    cursor:pointer;
}
.desc-box .tabslist a:hover{color: #e41c23;}
.desc-box .tabslist a.active{
    color: #e41c23;
    border-bottom: 4px solid #e41c23;
}
.desc-box .box{margin:20px;line-height: 40px;min-height: 700px;}
.desc-box .box .desc-txt{
    min-height: 800px;
    padding: 0 20px;
    overflow: hidden;
}
.desc-txt img{max-width:100%}

.Directory-div .item-dir .title{
    background-color: rgb(235, 238, 242);
    /* width: 741px; */
    height: 45px;
    color: #333;
    line-height: 45px;
    text-indent: 20px;
    font-size: 14px;
    overflow: hidden;
}
.Directory-div .Directory-div .item-dir .title{
    color: #999999;
    background: #fff;
    border-bottom: 1px solid #f0f0f0;
}
.reslist a{font-size:14px;height:48px;line-height:48px;text-indent: 18px;display: block;cursor: pointer;border-bottom: 1px solid #f0f0f0;position: relative;}
.reslist a:hover{color: #ed570e;}
.resitem.grey{color:#999!important;cursor: text;}
.resitem.grey svg path{fill: #999 !important;}
.reslist .time{display: inline-block;text-indent: 0;margin-right: 10px;vertical-align: middle;/* position: absolute; *//* right: 0; */}
.reslist .state{margin-right: 10px;vertical-align: middle;line-height: 8px;text-indent: 0;}
.reslist .state .uncomplated{
    width: 8px;
    height: 8px;
    border: 1px solid #9496a7;
    display: inline-block;
    border-radius: 50%;
    /* margin-right: 10px; */
}
.reslist .state svg{
    margin-top: -3px;
}
.reslist svg{
    vertical-align: middle;
    /* margin-top: -3px; */
}

.reslist .swf-icon{
    border-radius: 2px;
    background-color: rgb(102, 102, 102);
    width: 20px;
    height: 20px;
    display: inline-block;
    line-height: 20px;
    text-align: center;
    text-indent: 0;
}
.reslist .resname{
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  display: inline-block;
  width: 475px;
  line-height: 20px;
  height: 20px;
  vertical-align: middle;
}
.reslist .shikan_btn{
    display: inline-block;
    color: #e14500;
    border-radius: 4px;
    background-color: rgb(255, 255, 255);
    width: 37px;
    height: 16px;
    line-height: 15px;
    border: 1px solid #e14500;
    text-align: center;
    text-indent: 0;
    margin-right: 17px;
    vertical-align: middle;
}
.reslist .fr {float: right;color: #606266;position: absolute;right: 0;}
.reslist  .resitem.grey .pdf-icon,.reslist  .resitem.grey .swf-icon{background: #999;}
.reslist .pdf-icon{
    border-radius: 2px;
    background-color: rgb(102, 102, 102);
    width: 20px;
    height: 20px;
    display: inline-block;
    line-height: 20px;
    text-align: center;
    text-indent: 0;
    vertical-align: middle;
}
.Directory-div .Directory-div .Directory-div .item-dir {}
.resitem.active{
    color: #ed570e;
}
.resitem.active  svg path {
    fill: #ed570e;
}
.resitem.active  .pdf-icon svg path,.resitem.active  .swf-icon svg path{
  fill:#fff;
}
.resitem.active  .pdf-icon ,.resitem.active  .swf-icon {
  background:#ed570e;
}
.recommand_course_box{
    display: inline-block;
    background: #fff;
    width: 400px;
    margin-left: 20px;
    vertical-align: top;
    /* margin-top: 20px; */
    /* padding-bottom: 50px; */
    /* min-height: 400px; */
    /* cursor: pointer; */
    height: 265px;
    overflow: hidden;
}
.box-title{
    height: 64px;
    line-height: 64px;
    font-size: 16px;
    margin: 0 20px;
    border-bottom: 1px solid #d8d8d8;
}
.box-title span{
   vertical-align: middle;
}
.box-title .line{
   width: 5px;
   height: 20px;
   background:#024b9a;
   display: inline-block;
   border-radius: 4px;
   margin-right: 20px;
   margin-left: 20px;
}
.courseitem1{
    margin: 7px 20px 0;
}
.courseitem1 .imgpan{display: inline-block;overflow: hidden;border-radius: 4px;width: 116px;height: 80px;border: 1px solid #eee;}
.courseitem1 .imgpan img{width: 100%;}
.courseitem1 .rt{display: inline-block;vertical-align: top;height: 82px;width: 240px;}
.courseitem1 .title{font-size: 16px;color: #333;display: block;line-height: 22px;margin: 0 20px;height: 44px;cursor: pointer;}
.courseitem1 .teacher{font-size: 14px;line-height: 16px;color:#999;display: block;margin-left: 20px;margin-bottom: 10px;margin-top: 10px;}
.courseitem1 .teacher svg{vertical-align:middle;}
.courseitem1 .free{margin-right: 20px;font-size: 14px;margin-left: 20px;color: #ed570e;}
/*导航栏*/
.header {
  background: #fff;
  /* position: relative; */
  position: fixed;
  width: 100%;
  z-index: 999;
  top: 0;
  min-width: 1240px;
}
.el-popup-parent--hidden .header .h_container{padding-right: 27px;}
.head_blank{height: 100px;}
.h_container {
  margin: auto;
  line-height: 100px;
  position: relative;
  height: 100px;
  padding: 0 10px;
  overflow: hidden;
  max-width: 1250px;
}
.fixleft {
  display: inline-block;
  height: 100%;
}

.fixleft span {
  margin-left: 10px;
  font-size: 24px;
  vertical-align: middle;
  color: #333;
}
.fixright {
  text-align: center;
  float: right;
}
.down-link {
  cursor: pointer;
  color: #333;
}

/* new index */

/*修改导航*/
.logoClass {
  height: 65px;
  vertical-align: middle;
}
.menulist {
  display: inline-block;
}
.menulist a {
  display: inline-block;
  margin: 0 8px;
  text-decoration: none;
  color: #000;
  transition: color 0.25s ease-in-out, background-color 0.25s ease-in-out,
    border-color 0.25s ease-in-out;
  font-weight: bold;
  cursor: pointer;
  font-size: 16px;
  font-weight: normal;
  padding: 0 15px;
}
.menulist a:hover{color: #024b9a;}
.fixleft {
  /* margin-right: 5px; */
}
.user_box {
  display: inline-block;
  height: 44px;
  width: 171px;
  vertical-align: middle;
  line-height: 44px;
  border-radius: 4px;
  /* margin-left: 20px; */
  color: #424551;
  cursor:pointer;
}
.user_box .userImg{
    vertical-align: middle;
    margin-right: 5px;
}
.user_box .uifo{
    vertical-align: middle;
}
.user_box .uifo span{
    width: 86px;
    overflow: hidden;
    display: inline-block;
    vertical-align: middle;
    white-space: nowrap;
    text-overflow: ellipsis;
    margin-top: -5px;
}

.pageend {
  font-size: 18px;
  color: #999;
  line-height: 45px;
  text-align: center;
}

.down-link span {
  color: #333;
}

.fixleft a {
  padding: 10px;
}

.foot_nav h2 {
  text-align: left;
  font-size: 24px;
  color: #464e58;
  font-weight: normal;
}
.foot_nav .weblink a {
  color: #464e58;
  margin-right: 20px;
  cursor: pointer;
}
.menulist .router-link-exact-active {
  color: #024b9a;
  font-weight: bold;
  display: inline-block;
  position: relative;
}
.menulist .router-link-exact-active .span0 {
  display: inline-block;
  background: #024b9a;
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 4px;
  padding: 0 5px;
  margin-left: -5px;
}

.footer-box {
  overflow: hidden;
}

.footer_con1 {
  background-image: -moz-linear-gradient(
    0deg,
    rgb(49, 189, 208) 0%,
    rgb(81, 216, 143) 100%
  );
  background-image: -webkit-linear-gradient(
    0deg,
    rgb(49, 189, 208) 0%,
    rgb(81, 216, 143) 100%
  );
  background-image: -ms-linear-gradient(
    0deg,
    rgb(49, 189, 208) 0%,
    rgb(81, 216, 143) 100%
  );
  height: 240px;
  text-align: center;
  line-height: 240px;
}
.footer_div .che {
  text-align: center;
  margin-top: -70px;
}
.footer_con2 {
  margin: auto;
  padding: 40px 10px;
  max-width: 1200px;
  /* height: 300px; */
  /* overflow: hidden; */
}
.footer_con3{font-size: 14px;text-align:center;margin: auto;width: 100%;/* height: 60px; *//* line-height: 60px; */padding-bottom: 30px;}
/* .links-list {
  display: inline-block;
  vertical-align: top;
  margin-right: 78px;
  width: 460px;
  margin-left: 35px;
} */
.links-list-t .title {
  font-size: 18px;
  color: #333;
  margin-right: 60px;
}
.links-list-t{display: inline-block;}
.links-list-t a{margin: 0 20px;font-size: 14px;color: #333;}
/* .links-list a {
  display: inline-block;
  line-height: 36px;
  font-size: 18px;
  color: #333;
  margin: 0 40px;
} */
.links-list-right {
  float: right;
  margin-right: 0;
  width: 220px;
  display: inline-block;
  position: relative;
}
.links-list-right .tel {
  font-size: 30px;
  margin-top: 15px;
}

.links-list-right span{
    font-size: 14px;
    display: block;
    line-height: 40px;
}
.links-list-right span svg{vertical-align: middle;}
.links-c{text-align: center;margin-bottom: 20px;clear: both;}
.links-c a{font-size: 14px;color: #333;vertical-align: middle;}
.links-c .hr{width: 1px  ;
      display: inline-block;
      background: #333;
      height: 12px;
      vertical-align: middle;margin: 0 30px  ;}
.links-list-right .box{
   box-shadow: 0px 10px 50px 0px rgba(0, 0, 0, 0.1);
   background: #fff;
   position: absolute;
   bottom: 48px;
   left: 24px;
   width: 225px;
   height: 228px;
   text-align: center;
   padding: 10px 0;
   }
.links-list-right   .jue{
    width: 15px;
    height: 15px;
    position: absolute;
    transform: rotate(45deg);
    z-index: 9;
    background: #fff;
    left: 99px;
    bottom: -7px;
}
/*aboutus*/
.aboutus-body {
  background: #fff;
  margin: 0 auto 50px;

}
.Head-title0{
    height: 60px;
    line-height: 60px;
    padding-left: 10px;
    margin-bottom: 20px;
}
.Head-title0 span{
    vertical-align: middle;
    font-size: 20px;
}
.Head-title0 .line{
    background: #ed570e;
    display: inline-block;
    height: 30px;
    width: 3px;
    vertical-align: middle;
    margin-right: 20px;
}
.aboutus-body .breadcrumb-box .bread_con{
    max-width: 1200px;
    margin: auto;
}
.aboutus-body .breadcrumb-box {background:#ebeef2}

.aboutus-body-con{
    max-width: 1200px;
    margin: auto;
      padding: 50px 0;
}
.aboutus-body .cent {
  /* width: 1540px; */
  /* margin: 10px auto 50px; */
}
.aboutus-body .box {
  padding: 25px 10px 0 10px;
}
.aboutus-body .box h1 {
  font-size: 32px;
  color: #333;
  font-weight: normal;
  margin: 0;
}
.aboutus-body .box .title_en {
  margin: 30px 0;
  font-size: 32px;
}
.aboutus-body .box .txt-green {
  font-size: 24px;
  color: #009900;
  margin-bottom: 45px;
  text-indent: 32px;
}
.aboutus-body .box .p-txt {
  /* text-indent: 32px; */
  line-height: 36px;
  font-size: 16px;
}
.aboutus-body .box .font24 {
  font-size: 24px;
}
.aboutus-body img {
  width: 100%;
}
.zizhi {
  text-align: center;
  margin-bottom: 35px;
}
.zizhi .color-box {
  line-height: normal;
  display: inline-block;
  width: 87px;
  text-align: center;
  color: #fff;
  font-size: 18px;
  border-radius: 16px;
  padding: 16px 0;
  line-height: 26px;
  margin: 0 10px;
}
.zizhi .color-box span {
  font-size: 24px;
}
.c-c30f22 {
  background: #c30f22;
}
.c-f39700 {
  background: #f39700;
}
.c-8fc31e {
  background: #8fc31e;
}
.c-31a7e0 {
  background: #31a7e0;
}
.c-a40d5e {
  background: #a40d5e;
}
.c-601986 {
  background: #601986;
}

/*学习中心*/
.profile_info{width:240px;display:inline-block;border-radius: 4px;background: #fff;min-height: 480px;padding-bottom: 10px;}
.profile_info a{display:block;height:80px;line-height:80px;text-align:center;color: #333;border-left: 4px solid #fff;}
.profile_info a:hover{color: #024b9a;}
.profile_info .menuItemActive{
    border-left: 4px solid #024b9a;
    background: #f8f9fd;
    color: #024b9a;
}
.profile_info_box{width: calc(100% - 280px);display:inline-block;vertical-align:top;margin-left:28px;background: #fff;border-radius: 4px;padding-bottom: 180px;min-height: 550px;position: relative;}
.profile_info_con{max-width: 1200px;margin: 27px auto;overflow: hidden;}
.profile_info_title{color:#024b9a;margin: 0 20px;height: 64px;line-height: 64px;border-bottom: 1px solid #d8d8d8;}
.profile_info_title span{
    display: inline-block;
    padding: 0 30px;
    margin-left: 20px;
    border-bottom: 4px solid #024b9a;
    height: 60px;
}
.profile_info_content{
    margin: 30px 66px;
    min-height: 720px;
}
.profile_info_content .el-form-item{
    margin-bottom: 24px;
}
.profile_info_content.el-form-item label{
    font-size: 16px;
    color: #333;
}
.profile_info_content .el-form-item input{
    height: 48px;
    width: 480px;
}
.saveButton{
    margin-top: 48px;
    border-radius: 4px;
    background: linear-gradient( to right,#005dc2,#024b9a);
    /* background-image: -moz-linear-gradient( 0deg, rgb(237,114,14) 0%, rgb(243,72,19) 100%);
    background-image: -webkit-linear-gradient( 0deg, rgb(237,114,14) 0%, rgb(243,72,19) 100%);
    background-image: -ms-linear-gradient( 0deg, rgb(237,114,14) 0%, rgb(243,72,19) 100%); */
    width: 220px;
    height: 44px;
    border: 0;
    font-size: 16px;
    letter-spacing: 1px;
    color: #fff;
    margin-left: 96px;
}

/*资源详情*/
.videoBox{height: calc(100% - 200px);width: calc(100% - 40px);margin-left: 20px;background: #333;overflow: hidden;}
.Res-page{background: #3c3f4a;position: relative;min-height: 100%;width: 100%;overflow: hidden;}
.Res-page .right-box{width: 420px;position: absolute;right: 0;top: 0;background: #fff;height: 100%;overflow-y: scroll;transition: all 0.5s ease;}
.Res-page .right-box.CloseRight{width:0}
.Res-page .left-box{position: absolute;width: calc(100% - 420px);background: #3c3f4a;height: 100%;min-width: 819px;transition: all 0.5s ease;}
.Res-page .left-box.CloseRight{width:100%}
.Res-page .header-box{height: 110px;line-height: 110px;position: relative;}
.Res-page .return_btn{
  display: inline-block;
  border-width: 1px;
  border-color: rgb(49, 52, 65);
  border-style: solid;
  background-color: rgb(54, 58, 72);
  width: 129px;
  line-height: 40px;
  font-size: 14px;
  height: 38px;
  color: #aeafb3;
  border-radius: 0 17px 17px 0;
  margin-right: 30px;
  cursor: pointer;
  vertical-align: middle;
  }
.Res-page .return_btn:hover{background:#2b2e39;color:#fff}
.Res-page .return_btn:hover .svg-span-arrow{background:#fff}
.svg-span-arrow{
    display: inline-block;
    width: 16px;
    height: 16px;
    background: #9b9da4;
    line-height: 16px;
    text-align: center;
    border-radius: 100%;
    margin-left: 15px;
    margin-right: 10px;
}
.prev-box{
    position: relative;
    margin-right: 42px;
    vertical-align: middle;
}
.prev-box a,.next-box a{
    border-width: 1px;
    border-color: rgb(49, 52, 65);
    border-style: solid;
    border-radius: 4px;
    background-color: rgb(54, 58, 72);
    width: 38px;
    height: 38px;
    display: inline-block;
    line-height: 38px;
    text-align: center;
    z-index: 3;
    position: relative;
    cursor: pointer;
}
.prev-box a:hover,.next-box a:hover{background-color: #2b2e39;}
.prev-box a svg path,.next-box a svg path{fill: #9b9da4;}
.prev-box a:hover svg path,.next-box a:hover svg path{fill: #fff;}
.prev-box .line,.next-box .line{
    height: 110px;
    position: absolute;
    width: 4px;
    background: #313441;
    left: 50%;
    margin-left: -2px;
}
.next-box .line{height:90px}
.Res-title{font-size: 18px;color: #fff;width: calc(100% - 760px);display: inline-block;white-space: nowrap;text-overflow: ellipsis;overflow: hidden;vertical-align: middle;}
.right-span{float: right;/* width: 440px; */height: 40px;line-height: 40px;margin-top: 35px;background: #d8d9db;margin-right: 60px;border-radius: 4px;overflow: hidden;}
.duration{width: 108px;height: 22px;line-height: 22px;display: inline-block;text-align: center;color: #333;border-right: 1px solid #333;}
.learnedtime{color: #333;width: 170px;display: inline-block;text-align: center;font-size: 16px;}
.right-span button{
  background-image: -moz-linear-gradient( 0deg, rgb(237,114,14) 0%, rgb(243,72,19) 100%);
  background-image: -webkit-linear-gradient( 0deg, rgb(237,114,14) 0%, rgb(243,72,19) 100%);
  background-image: -ms-linear-gradient( 0deg, rgb(237,114,14) 0%, rgb(243,72,19) 100%);
  width: 160px;
  height: 40px;
  border: none;
  color: #fff;
  outline: none;
  cursor: pointer;
  }
.right-span button.disable{background-color: #999999;background-image: none;}
.shq_btn{cursor: pointer; display: inline-block;border-width: 1px;border-radius: 50% 0 0 50%;position: absolute;width: 38px;height: 38px;line-height: 38px;text-align: center;right: 0;top: 30px;z-index: 7;
  border-color: rgb(49, 52, 65);border-style: solid;background-color: #363a48;}
.shq_btn svg path{fill: #9b9da4;}
.courseinfo-box2{    border-bottom: 1px solid #b2b2b2;    width: 400px;}
.shq_btn:hover{ border-color: rgb(49, 52, 65);  background-color: #2b2e39;}
.shq_btn:hover svg path{fill: #fff;}
.directoryDiv{width: 400px;float: right;margin-bottom: 50px;}
.courseinfo-box2 img{
    width: 160px;
    margin: 16px 15px 16px 35px;
    height: 80px;
    vertical-align: middle;
}
.courseinfo-box2 span{
    display: inline-block;
    vertical-align: middle;
    /* margin-top: 17px; */
    font-size: 16px;
    color: #333;
    width: 190px;
    line-height: 28px;
}
.right-box .Directory-div{/* margin-bottom: 50px; */}
.res-app-main{height: 100%;}
.next-box{height: 90px;position: absolute;left: 161px;}
.next-box a{margin-top: 30px;}
.right-box .reslist .resname{
  margin-left: 15px;
  white-space: nowrap;
  text-overflow: ellipsis;
  width: 200px;
  display: inline-block;
  overflow: hidden;
  vertical-align: middle;
}
.videoBox .pre-img{width: 100%;max-height: 100%;}
#reader{
  height: 100% !important;
  width: 100% !important;
  overflow-y: scroll;
  background: #fff;
  text-align: center;
}
#reader .wenku-toolsbar-mod{
    width: 100% !important;
}
.videoBox iframe{height: 100%;width:100%;border: none;}
.profile_info_box .tabs{
    margin: 0 20px;
    height: 64px;
    line-height: 64px;
    border-bottom: 1px solid #d8d8d8;
}
.profile_info_box .tabs a{
    padding: 0 25px;
    height: 60px;
    margin-left: 20px;
    cursor: pointer;
}
.profile_info_box .tabs a:hover{color: #024b9a;}
.profile_info_box .tabs a.active{
    display: inline-block;
    color: #024b9a;
    border-bottom: 4px solid #024b9a;
}
.profile_info_box .titem{height: 160px;margin: 0 40px;cursor: pointer;}
.mytrains .titem .left{width: 100px;display: inline-block;vertical-align: middle;}
.mytrains .titem .line1{
    display: inline-block;
    width: 42px;
    vertical-align: middle;
    height: 100%;
    position: relative;
}
.mytrains .titem .line1 span{
    position: absolute;
    width: 8px;
    height: 8px;
    background: #8f91a3;
    border-radius: 4px;
    top: 50%;
    left: 50%;
    margin-left: -5px;
    margin-top: -4px;
}
.mytrains .titem .line1 hr{
    border: none;
    border-left: 1px dashed #c7c8d1;
    height: 100%;
    width: 0;
    padding: 0;
    margin-top: -1px;
}
.mytrains .titem .right{
    width: calc(100% - 145px);
    display: inline-block;
    border-bottom: 1px solid #f0f0f0;
    height: 100%;
    vertical-align: middle;
}
.mytrains .titem .right .con{display: inline-block;width: calc( 100% - 148px);margin-left: 20px;vertical-align: top;margin-top: 20px;}
.mytrains .titem .right img{margin: 20px 0;vertical-align: middle;width: 120px;}
.mytrains .titem .left .year{font-size: 16px;color: #333;display: block;text-align: right;}
.mytrains .titem .left .day{font-size: 14px;color: #999;text-align: right;display: block;margin-top: 10px;}
.mytrains .titem .right .con .title{
    font-size: 16px;
    margin-bottom: 14px;
    margin-top: 4px;
    height: 18px;
    line-height: 18px;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
}
.mytrains .titem .right .con:hover .title{color: #ed570e;;}
.mytrains .titem .right .con .time{
    font-size: 14px;
    color: #666;
    margin-bottom: 12px;
}
.mytrains .titem .right .con .num{
    font-size: 14px;
    color: #666;
}
.mytrains .titem .right .con  .state{display: inline-block;border-radius: 4px;width: 70px;height: 28px;text-align: center;line-height: 28px;margin-top: 10px;font-size: 14px;}
.mytrains .titem .right .con  .state.unstarted{background:#dffadf;color:#018b3e}
.mytrains .titem .right .con  .state.isfinished{background:#eaeaea;color:#666}
.mytrains .titem .right .con  .state.isTraining{background:#fceed9;color:#ea8f00}
.mytrains .titem .right .prewImg{width: 180px;height: 100px;border-radius: 4px;margin: 30px 0;}
.mytrains .titem .right  .course-con{width: calc(100% - 200px);margin-top:30px;position: relative;}
.mytrains .titem .right  .course-con:hover .title{color:#ed570e;}
.mytrains .titem .right .course-con .title{    margin-bottom: 10px;}
.mytrains .titem .right .course-con .state{    margin-top: 0px;}
.mytrains .titem .right .course-con .time{    margin-bottom: 7px;}
.mytrains .titem .right .course-con .btn{width: 120px;height: 38px;text-align: center;line-height: 34px;font-size: 14px; border-radius: 4px;border: 1px solid;background: none;float: right;vertical-align: middle;position: absolute;right: 0;top: 50%;margin-top: -24px;cursor: pointer;}
.mytrains .titem .right .course-con .btn.start{color:#ed570e;border-color:#ed570e ;}
.mytrains .titem .right .course-con .btn.end{color:#999}
.mytrains .titem .right .course-con .state.isTraining{background:#fbe3d9;color:#ed570e}


/*培训详情*/
.trainInfo{border-radius: 4px;  height: 200px;width: 840px;background: #fff;}
.trainInfo .right{display: inline-block;width: 610px;vertical-align: middle;/* margin-top: 20px; */position: relative;min-height: 148px;}
.trainInfo img{margin: 20px;width: 160px;vertical-align: middle;}
.trainInfo .right{}
.trainInfo .time{
    font-size: 16px;
    color: #666;
    margin-bottom: 12px;
}
.trainInfo .title{
    font-size: 24px;
    margin-bottom: 13px;
    max-height: 55px;
    overflow: hidden;
    min-height: 40px;
}
.trainInfo .num{
    font-size: 16px;
    color: #666;
}
.trainInfo .state{display: inline-block;border-radius: 4px;width: 80px;height: 30px;text-align: center;line-height: 30px;margin-top: 10px;position: absolute;right: 0;bottom: 0;}
.trainInfo .state.unstarted{background:#dffadf;color:#018b3e}
.trainInfo .state.isfinished{background:#eaeaea;color:#666}
.trainInfo .state.isTraining{background:#fceed9;color:#ea8f00}
.total_div{display: inline-block;width: 340px;height: 300px;background: #fff;margin-bottom: 20px;}
.left_con{width: 840px;display: inline-block;vertical-align: top;}
.right_con{width: 340px;display: inline-block;vertical-align: top;margin-left: 20px;}
.left_con .desc-box{width: 100%;}
.right_con .notic_box{background: #fff;padding-bottom:120px;}
.right_con .notic_box .notic_content{margin: 20px;line-height: 25px;}
/*圆环*/
.pie {width: 120px;height: 120px;border-radius: 50%;background: #fee6d9;position: relative;margin: 60px 30px;display: inline-block;vertical-align: middle;}
.pie .circle {position: absolute;top: 50%;left: 50%;z-index: 100;transform: translate(-50%, -50%);width: 100px;height: 100px;background: white;border-radius: 50%;line-height: 100px;text-align: center;font-size: 24px;color: #ed570e;}
.pie .circle span{font-size:24px;color:#bcc3d1}
.pie .left, .pie .right {  width: 60px;  height: 120px;  float: left;  overflow: hidden;}
.pie .left {  border-radius: 120px 0 0 120px;}
.pie .right {  border-radius: 0 120px 120px 0;}
.pie .left-deg, .pie .right-deg {  width: 60px;  height: 120px;  background: #fd5808;}
.pie .left-deg {  transform-origin: right center;  }
.pie .right-deg {  transform-origin: left center;  }


.total_div .ft{    display: inline-block;    vertical-align: middle;}
.total_div .learned{    font-size: 20px;    color: #ed570e;    text-align: center;    margin-bottom: 30px;}
.total_div .learned span{    font-size: 14px;    color: #333;    display: block;    margin-top: 10px;}
.total_div .total{    font-size: 20px;    text-align: center;    margin-top: 30px;}
.total_div .total span{    font-size: 14px;    display: block;    margin-top: 10px;}
.total_div .ft hr{    border: none;    border-top: 2px solid #ed570e;    width: 30px;}

.box .item-course{height: 160px;border-bottom: 1px solid #f0f0f0;cursor: pointer;}
.box .item-course img{width: 180px;height: 100px;border-radius: 4px;margin: 30px 19px;border: 1px solid #eee;}
.box .item-course .fr{display: inline-block;width: 580px;margin-top: 30px;vertical-align: top;position: relative;height: 100px;}
.box .item-course .fr .title{font-size: 16px;line-height: 16px;margin-bottom: 10px;width: 459px;white-space: nowrap;text-overflow: ellipsis;overflow: hidden;}
.box .item-course:hover .fr .title{color:#ed570e;}
.box .item-course .fr .time{font-size: 14px;color: #666;line-height: 20px;}
.box .item-course .fr .state{display: inline-block;border-radius: 4px;width: 70px;height: 28px;text-align: center;line-height: 28px;margin-top: 7px;position: absolute;bottom: 0;font-size: 14px;}
.box .item-course .fr .state.unstarted{background:#dffadf;color:#018b3e}
.box .item-course .fr .state.isfinished{background:#eaeaea;color:#666}
.box .item-course .fr .state.isTraining{background:#fbe3d9;color:#ed570e}
.box .item-course .fr .btn{width: 120px;height: 38px;text-align: center;line-height: 34px;font-size: 14px; border-radius: 4px;border: 1px solid;background: none;float: right;vertical-align: middle;position: absolute;right: 0;top: 50%;margin-top: -24px;cursor: pointer;}
.box .item-course .fr .btn.start{color:#ed570e;border-color:#ed570e;}
.box .item-course .fr .btn.end{color:#999;border-color:#999;}


/*学习记录*/
.tabbox{
    margin-top: 20px;
}
.tabbox .tishi{
    background: #fcf4e5;
    margin: 20px;
    color: #e14500;
    font-size: 14px;
    height: 48px;
    line-height: 48px;
    text-indent: 30px;
}
.tabbox .tishi .table_train{}
.table_default{
    margin: 0 20px;
}
.table_default .th_row{
    background: #ebeef2;
    height: 50px;
    line-height: 50px;
    font-size: 14px;
}
.table_default .th_row div{
    display: inline-block;
}
.table_default .row{
    height: 66px;
    line-height: 66px;
    border-bottom: 1px solid #f1f1f1;
}
.table_default .row:hover{background-color: #F5F7FA;}
.table_train .td1{width: 25%;text-indent: 30px;}
.table_train .td1 span{
    line-height: 19px;
    display: inline-block;
    margin-left: 30px;
    text-indent: 0;
    margin-right: 10px;
    vertical-align: middle;
    margin-top: -3px;
}
.table_train .td1 span.name{
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  width: 169px;
}
.table_train .td2{    width: 23%;}
.table_train .td3{    width: 14%;}
.table_train .td4{    width: 15%;}
.table_train .td5{    width: 13%;   }
.table_train .td6{    width: 10%;    text-align: center;}
.table_default .row div{display: inline-block;font-size: 14px;}
.table_train .td6 a,.table_default .td4 a{color: #3e54f4;cursor: pointer;}
.table_train .row .td1{    text-indent: 0;}
.table_comment .td1{width: 20%;text-indent: 30px;}
.table_comment .title {
  font-size: 14px;
    line-height: 25px;
    vertical-align: middle;
    text-align: left;
    height: 25px;
    overflow: hidden;
    -webkit-line-clamp: 1;
}
.table_comment .td2{   
    width: 45%;
    }
.table_comment .content {
  font-size: 14px;
  line-height: 25px;
  vertical-align: middle;
  text-align: left;
  max-height: 50px;
  overflow: hidden;
  -webkit-line-clamp: 2;
  word-break: break-all;
}
.table_comment .td3{width: 20%;}
.table_comment .td4{width: 15%;}
.table_course .td1{width: 45%;text-indent: 30px;}
.table_course .td1 .name {
    font-size: 14px;
    line-height: 25px;
    vertical-align: middle;
    text-align: left;
    max-height: 50px;
    overflow: hidden;
    text-indent: 0;
    width: 80%;
    -webkit-line-clamp: 2;
}
.table_course .td2{width: 15%;}
.table_course .td3{width: 15%;}
.table_course .td4{width: 25%;}

.shadow_RecordInfo{position: absolute;width: 700px;left: 50%;margin-left: -350px;top: 150px;height: 450px;background: #fff;border-radius: 20px;box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.25);}
.shadow_RecordInfo h2{
    height: 60px;
    text-indent: 20px;
    font-size: 16px;
    border-bottom: 1px solid #f1f1f1;
    line-height: 60px;
    margin: 0;
    margin-bottom: 25px;
}
.shadow_RecordInfo h2 a{
    float: right;
    margin-right: 20px;
    cursor: pointer;
    font-size: 24px;
}
.shadow_RecordInfo .table_train .th_row{
    background: #e4eaf3;
    height: 48px;
    line-height: 48px;
}
.shadow_RecordInfo .table_train .row{
    height: 42px;
    line-height: 42px;
}
.shadow_RecordInfo .table_train{
    margin: 0 10px;
}
.shadow_RecordInfo .list{height: 250px;    overflow-y: auto;}
.my_pagination{text-align: center;margin-bottom: 20px;    margin-top: 20px;}

/*登录注册*/

.title_box{text-align: center;height: 30px;font-size: 22px;color: #000;line-height: 30px;margin-bottom: 30px;}
.customDialog .loginRegistButton ,.customDialog .verificateCodeButton {
  border-radius: 4px;
  background: linear-gradient( to right,#005dc2,#024b9a);
  /* background-image: -moz-linear-gradient( 0deg, rgb(237,114,14) 0%, rgb(243,72,19) 100%);
  background-image: -webkit-linear-gradient( 0deg, rgb(237,114,14) 0%, rgb(243,72,19) 100%);
  background-image: -ms-linear-gradient( 0deg, rgb(237,114,14) 0%, rgb(243,72,19) 100%); */
  width: 100%;
  height: 44px;
  font-size: 16px;
}
.customDialog  .el-button.is-disabled, .customDialog .el-button.is-disabled:focus,  .customDialog .el-button.is-disabled:hover{background-image: -moz-linear-gradient( 0deg, rgb(237,114,14,0.5) 0%, rgb(243,72,19,0.5) 100%);background-image: -webkit-linear-gradient( 0deg, rgb(237,114,14,0.5) 0%, rgb(243,72,19,0.5) 100%);background-image: -ms-linear-gradient( 0deg, rgb(237,114,14,0.5) 0%, rgb(243,72,19,0.5) 100%);font-size: 12px;}
.customDialog .loginRegistButton span,.customDialog .verificateCodeButton span{color: #fff;}
.forget_box{
    height: 30px;
    text-align: right;
    float: right;
    cursor: pointer;
}
.tips_box{
    height: 40px;
    line-height: 40px;
    text-align: center;
}
.tips_box .tips_btn{
    cursor: pointer;
    color: #014c9c;
}
/*空白页*/
.NoContent{
    text-align: center;
    padding: 120px 0;
    color: #666;
}
.NoContent span{
    display: inline-block;
    width: 100%;
    line-height: 42px;
}
/*more*/
.moreData{
    width: 90%;
    margin: auto;
    text-align: center;
    border: 1px solid #f1f1f1;
    margin-top: 40px;
    line-height: 40px;
    position: absolute;
    left: 5%;
    bottom: 50px;
    cursor: pointer;
}
.moreData:hover{background:#f1f1f1}

/*协议*/
.aboutus-body .title{font-size: 16px;font-weight: bold;height: 36px;line-height: 36px;margin: 15px 15px;display: block;}
.aboutus-body .p-span{margin: 0 10px;font-size: 14px;line-height: 36px;display: block;}

.weui-switch {
  display: inline-block;
  position: relative;
  width: 42px;
  height: 20px;
  border: 1px solid #DFDFDF;
  outline: 0;
  border-radius: 16px;
  box-sizing: border-box;
  background-color: #DFDFDF;
  transition: background-color 0.1s, border 0.1s;
  cursor: pointer;
  vertical-align: middle;
}
.weui-switch:before {
  content: " ";
  position: absolute;
  top: 0;
  left: 0;
  width: 40px;
  height:19px;
  border-radius: 15px;
  background-color: #FDFDFD;
  transition: transform 0.35s cubic-bezier(0.45, 1, 0.4, 1);
  border-bottom: 1px solid #eee;
}
.weui-switch:after {
  content: " ";
  position: absolute;
  top: 0;
  left: 0;
  width: 20px;
  height:19px;
  border-radius: 15px;
  background-color: #FFFFFF;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);
  transition: transform 0.35s cubic-bezier(0.4, 0.4, 0.25, 1.35);
}
.weui-switch-on {
  border-color: #1AAD19;
  background-color: #1AAD19;
  border-bottom: 1px solid #f1f1f1;
}
.weui-switch-on:before {
  border-color: #1AAD19;
  background-color: #1AAD19;
}
.weui-switch-on:after {
  transform: translateX(20px);
}
.switchbox{float: right;}
.box-top{height: 50px;}
.switchbox .txt{    vertical-align: middle;  margin-left: 10px;    font-size: 14px;color: #909399;}


/*问答评论*/
.comment-list-box{}
.opr-box{height: 60px;line-height: 60px;border-bottom: 1px solid #c5c5c5;/* margin-top: -20px; */}
.opr-box .list{
    display:  inline-block;
    vertical-align:  middle;
}
.opr-box .list a{padding:0 20px; cursor: pointer; border-right: 1px solid #cecece;}
.opr-box .list a:last-child{border: none;}
.opr-box .list a.active{color: #ea2413}
.opr-box button{
    display: inline-block;
    vertical-align:  middle;
    height: 36px;
    line-height: 36px;
    padding:  0;
    float: right;
    border:  none;
    background: #ea2413;
    color: #fff;
    width: 90px;
    border-radius:  18px;
    font-size:  14px;
    text-align:  center;
    margin-top: 12px;
    margin-right: 10px;
    outline:  none;
	cursor: pointer;
}
.comment-item{padding: 25px 0;border-bottom: 1px solid #d8d8d8;cursor: pointer;}
.t-div .user-box img{vertical-align: middle;width: 36px;height: 36px;border-radius: 18px;margin-right: 18px;}
.t-div .user-box span{font-size: 14px;}
.t-div .type1{float: right;color: #024b9a;}
.t-div .type0{float: right;color: #ea2413;}
.t-div{}
.t-div .user-box{padding: 0 15px; margin-bottom: 10px;}
.m-div{}
.m-div .content-div{color: #14191e;margin-left: 67px;font-size: 16px;word-wrap: break-word;line-height: 24px;margin-top: 6px;margin-bottom: 5px;}
.m-div .sum{color: #999;margin-left: 67px;margin-top: 12px;font-size: 14px;overflow:  hidden;line-height:  24px;}
.m-div .sum .num{margin-right: 25px;font-size: 13px;}
.m-div .sum .time{float: right;margin-right: 10px;font-size: 13px;}
.comment-list-box .more,.reply-list-box .more{text-align: center;font-size: 16px;color:#ea2413 ;cursor: pointer;line-height: 60px;
    background: #f5f5f5;
    margin-top: 20px;}
.best-answer{color:#14191e;font-size: 14px;color: #7d7a7a;margin-top: 10px;margin-left:  67px;line-height: 24px;margin-bottom:  15px;}
.best-answer .best-answer-content{font-size:14px;line-height:18px;margin-top: 10px;}
.best-answer .txt{color: #537393;}
 
.pl .user-box span{font-size: 16px;}
.pl .content-div{ font-size: 14px;color: #14191e;}
.pl {cursor: auto;}

.reply-div .root{/* margin-top: -20px; */font-size: 14px;height: 60px;line-height: 60px;border-bottom: 1px solid #d8d8d8;}
.reply-div .root span{    font-family: monospace;    margin: 0 5px;}
.comment-info{border-bottom: 1px solid #d8d8d8;padding:  20px 0;}
.type-title{font-size: 16px;color: #333; margin-bottom: 20px;}
.replybtn{cursor:pointer;display: inline-block;width: 90px;margin-top: 15px;margin-left: 67px;font-size: 14px;height: 36px;line-height: 36px;background: #e51c23;color: #fff;border-radius: 18px;padding: 0;border: none;outline: none;}
.reply-box{margin: 0 ;display: block;background:#fff;}
.reply-dialog .el-dialog__header,.choose-type-dialog .el-dialog__header {padding: 0;}
.reply-dialog .el-dialog__body,.choose-type-dialog .el-dialog__body{padding: 0 ;}
.reply-box .title,.choose-type-box .title{font-size: 18px;line-height: 60px;height: 60px;color:#333;text-indent: 20px;border-bottom: 1px solid #e7e7eb;}
.reply-dialog .close,.choose-type-box .close{float: right;margin-right: 20px;line-height: 60px;cursor: pointer;}
.reply-dialog .close img,.choose-type-box .close img{width: 24px;vertical-align: middle;}
.reply-box .comment-info{margin-left:20px;border: none;}
.reply-dialog .contentbox{background: #f7f8fc;overflow: hidden;}
.reply-dialog .contentbox textarea{height: 124px;width: 660px;border: 1px solid #e8e8e8;resize: none;padding: 15px 10px;outline: none;line-height: 20px;}
.reply-dialog .contentbox .title{height: 54px;line-height: 54px;font-size: 18px;color: #333 ;border: none;}
.text-box{display: inline-block;margin:0 20px;position: relative;height: 155px;}
.len{position: absolute;right:10px;bottom: 7px;}
.reply-list-box .m-div{position:relative}
.reply-list-box .time{position:absolute;color: #999;font-size: 14px;right:0;bottom:0}
.reply-dialog button{background: #e51c23;float: right;width: 96px;height: 36px;line-height: 36px;font-size: 14px;color: #fff;margin: 10px 25px 17px;border: none;padding:  0;border-radius: 18px;cursor: pointer;}
.reply-dialog button:disabled{opacity:0.6}
.reply-list-box .comment-info{position: relative;}
.reply-list-box .bestanswer-ico{position:absolute;right:0;top: 0;}
.reply-list-box .t-div {margin-bottom: 20px;}
.setbestanswer-btn{    cursor: pointer; margin-top: 30px; border:2px solid #e51c23;background: #fff;outline: none;  color: #e51c23;font-size: 14px;border-radius: 16px; margin-left: 67px;width: 138px;padding: 5px 0;}

.choose-type-box{overflow: hidden;}
.btn-list{margin: 30px 25px;}
.choose-item{height:126px;padding:  0 28px;cursor: pointer;}
.choose-item:hover{background:#f7f8fc;}
.btn-list .imgbox{    text-align:  center;  line-height:  100px;height:100px;margin-top: 13px;display:  inline-block; border-radius: 6px; width:  100px;margin-right:  25px;}
.btn-list .imgbox img{vertical-align: middle;}
.btn-list .imgbox.type1{
    background:  #7ca1cb;
}
.btn-list .r{
    display:  inline-block;
    width:  450px;
    vertical-align:  middle;
}
.btn-list .t{
    display:  block;
    line-height:  38px;
    font-size:  18px;
}
.btn-list .desc{
    line-height:  24px;
    font-size:  14px;
    color:  #999;
}
.btn-list .imgbox.type0{
    background: #f49189;
}
.add-comment-div .imgbox-type{width: 24px;height: 24px;border-radius: 4px;text-align: center;line-height: 24px;display:  inline-block;margin-right:  10px;}
.add-comment-div .imgbox-type img{width: 14px;}
.add-comment-div .imgbox-type0{background: #f49189;}
.add-comment-div .imgbox-type1{background:  #7ca1cb;}
.add-comment-div .root {
    /* margin-top:  -20px; */
    height:  60px;
    line-height:  60px;
    border-bottom: 1px solid #d8d8d8;
    font-size:  14px;
    color:  #333;
}
.add-comment-div .root span{
    font-family:  monospace;
    margin:  0 5px;
}
.add-comment-div .root a{}
.add-comment-div .title{
    height:  60px;
    line-height:  60px;
    font-size:  16px;
    color:  #333;
}
.add-comment-div  .content-box{
    position:  relative;
    width: 360px;
    height: 260px;
    margin-bottom: 20px;
}
.add-comment-div  .content-box span{
    position:  absolute;
    right: 10px;
    bottom: 0;
    color:  #999;
}
.add-comment-div  textarea{
    border:  1px solid #e8e8e8;
    resize:  none;
    width: 320px;
    padding: 16px 20px;
    line-height: 24px;
    height: 232px;
    outline: none;
    font-size: 14px;
    color:  #333;
}
.add-comment-div .submit-comment-btn:disabled{opacity:0.6}

.add-comment-div .submit-comment-btn{
    width:  90px;
    padding: 8px 0;
    border:  none;
    border-radius:  18px;
    background: #e51c23;
    color:  #fff;
    float:  right;
    outline: none;
    cursor: pointer;
}



/* 直播 */
.live-cent{width: 1200px;margin: auto;padding: 15px 0;}
.live-statue-list{border-bottom: 1px solid #B2B2B2;height: 51px;line-height: 39px;margin-bottom: 20px; margin-top: 0px;}
.live-statue-list a{padding: 5px 20px; cursor: pointer; display: inline-block; font-size: 16px;color: #333;margin-right: 20px;}
.live-statue-list a.active{border-bottom: 3px solid #e41c23; color: #e41c23;}
.live-img-box{position: relative;margin-top: 20px;margin-left: 40px;border-radius: 4px;display: inline-block;vertical-align:  top;}
.live-img-box .shaw{width: 400px;height: 225px;position: absolute;top: 0;left: 0;border-radius: 4px;background: url(./images/opcity45.png);}
.live-img-box .coverImage{width: 400px;height: 225px;border: 1px solid #eee ;border-radius: 5px;}
.live-img-box .txt{position: absolute;top: 70px;text-align: center;width: 100%;}
.live-img-box .title{display: block;font-size: 20px;color: #fff;}
.live-img-box .statue{display: block; font-size: 12px;color: #fff;margin: 15px auto; background:url(images/opcity70.png) ;width: 240px;height: 30px;border-radius: 15px;line-height: 30px;}
.live-item{background: #fff;height: 265px;overflow: hidden;border-radius: 4px;margin-bottom: 30px;}
.live-r-div{display: inline-block;margin-left: 30px;margin-top: 20px;width: 700px;}
.live-r-div .title{
    font-size:  20px;
    color:  #333;
    line-height:  24px;
    max-height: 48px;
    overflow: hidden;
    margin-bottom: 13px;
    cursor: pointer;
}
.live-r-div .title:hover{
  color:  #024b9a;
}
.live-r-div .desc{
    font-size:  14px;
    color:  #999;
    line-height:  21px;
    height: 42px;
    overflow:  hidden;
    margin-bottom: 10px;
}
.live-r-div .m{
    font-size:  16px;
    color:  #666;
    margin-bottom: 12px;
    line-height: 18px;
}
.live-r-div .b{}
.live-r-div .b button{ cursor: pointer; padding: 0; outline: none; margin-top: 5px;  border: none;font-size: 20px;color: #fff;min-width: 154px;height: 44px;line-height: 44px;text-align: center;border-radius:  22px;padding: 0 30px;}
.live-r-div .b button.statue1{background: #c3c7d7;}
.live-r-div .b button.statue2{background: #014c9c}
.live-r-div .lecturer{
    margin-right:  40px;
    display: block;
    margin-bottom: 15px;
} 
.live-r-div .m img{vertical-align: middle;margin-top: -3px;margin-right: 6px;}
.live-r-div .usercount{
    display: inline-block;
    border-right: 1px solid #e7e7eb;
    padding-right: 30px;
}
.live-r-div .time{
    padding-left: 30px;
}
.live-cent .more{width: 100%;border-radius: 4px;margin-top: 20px;height: 64px;line-height: 64px;color: #333;font-size: 18px;background: #fff;text-align:  center;}
.fixright .live-btn{
    width:  90px;
    display: inline-block;
    line-height: 14px;
    padding: 11px 0;
    border: none;
    margin-left:  20px;
    vertical-align:  middle;
    background:linear-gradient( to right,#005dc2,#024b9a);
    color:  #fff;
    font-size:  14px;
    border-radius:18px;
	margin-right: 12px;
}

.footer_div {
  height: 115px;
}

.footer_div .footer_bg {
  /* line-height: 115px; */
  padding: 20px 0;
}

.footer_div .info_footer {
  position: relative;
  width: 100%;
  margin: 0 auto;
  display: inline-block;
  text-align: center;
}

.footer_div .footer_bg .box {
  box-shadow: 0px 10px 50px 0px rgb(0 0 0 / 10%);
  background: #fff;
  position: absolute;
  bottom: 65px;
  left: calc(50% + 64px);
  width: 225px;
  height: 228px;
  text-align: center;
  padding: 10px 0;
}
.footer_div .footer_bg .qrcode {
  position: relative;
}
.footer_div .footer_bg .box span {
      font-size: 14px;
    display: block;
    line-height: 40px;
}
.footer_div .footer_bg .box .jue {
  width: 15px;
    height: 15px;
    position: absolute;
    transform: rotate(45deg);
    z-index: 9;
    background: #fff;
    left: 99px;
    bottom: -7px;
}
.courseitem .star-p-list{display: block;margin-top: 15px;margin: 15px 20px;}
.courseitem .star-p-list img{     width: 20px; height: 20px;border: none;  margin: 0;vertical-align: middle;}
.courseitem .star-p-list .score_txt{font-size: 16px;color: #d83601;vertical-align: middle;margin-left: 10px;}
.courseitem .star-p-list  .star-a-item{vertical-align: middle;}
.joincount {color: #999;display: inline-block;vertical-align: middle;width: 30%;font-size: 14px;}
.joincount img{vertical-align: middle;height: 14px;}
.joincount span{vertical-align: middle;}
.classhour{color: #999;display: inline-block;vertical-align: middle;width: 23%;/* border-left: 1px solid #999; */text-align: right;height: 14px;line-height: 14px;font-size: 14px;}
.RecommandCourse .tabs span{
    font-size: 18px;
    margin-left: 45px;
    color: #666;
    padding-bottom: 11px;
    cursor: pointer;
    text-align: center;
}
.RecommandCourse .tabs span.active{
    color: #024b9a;
    border-bottom: 2px solid #024b9a;
}
.RecommandCourse .tabs{/* margin-left: 20px; */}
.Recommand-live-box{max-width: 1200px;    margin: auto;width: 100%;}
.Recommand-live-box h2 a{position:absolute;right: 16px;font-size: 16px;bottom: 7px;color: #666;cursor:pointer}
.Recommand-live-box h2{font-size: 24px;font-weight: normal;border-bottom: 1px solid #ccc;height: 40px;position: relative;}
.courseInfo_right .teacher{/* margin-left: 20px; */font-size: 14px;line-height: 16px;color:#999;vertical-align: middle;/* width: 33%; */display: inline-block;margin-right: 37px;}
.courseInfo_right .teacher svg{vertical-align:middle;/* margin-top: -3px; */}
.courseInfo_right .teacher span{vertical-align:middle;}

.courseInfo_right .star-p-list{display: block;/* margin-top: 15px; */margin-bottom: 10px;margin-top: 10px;}
.courseInfo_right .star-p-list img{     width: 20px; height: 20px;border: none;  margin: 0;vertical-align: middle;}
.courseInfo_right .star-p-list .score_txt{font-size: 16px;color: #d83601;vertical-align: middle;margin-left: 10px;}
.courseInfo_right .star-p-list  .star-a-item{vertical-align: middle;}


.courseInfo_right  .state{
  border-radius: 4px;
  width: 65px;
  height: 22px;
  display: inline-block;
  vertical-align: middle;
  line-height: 22px;
  text-align: center;
  /* float: left; */
  /* margin-top: 6px; */
  font-size: 14px;
  /* position: absolute; */
  /* bottom: 65px; */
}
.courseInfo_right  .unStarted{
  background: #d4f9d4;
  color: #018b3e;
}
.courseInfo_right .statue_span{
    /* position: absolute; */
    /* bottom: 50px; */
    margin-top: 9px;
    display: inline-block;
}
.courseInfo_right  .isCompleted{background:#e1e1e1;color:#333}
.courseInfo_right  .isStudying{background:#ffdccc;color:#ed570e}
.courseInfo_right  .p-div{margin: 5px 0;}
.comment_box_right{
  display: inline-block;
  background: #fff;
  width: 400px;
  margin-left: 20px;
  vertical-align: top;
  margin-top: 20px;
  padding-bottom: 50px;
  min-height: 400px;
  overflow: hidden;
  }
  .comment_box_con{margin: 0 20px;}
.couldLearn-txt{    color: #333;
  display: inline-block;
  padding: 5px 10px;
border-radius: 4px;
  opacity: 0.8;
  background: #eee;
  font-size: 14px;}
.hme-message{max-width: 1200px;margin: 0 auto;border-radius:6px ; padding: 3px;background: rgb(254, 249, 230);color: rgb(207, 128, 84);}
.footer_div .copyright-txt{width: 1000px ;margin:0 auto 15px; line-height: 36px;text-align: center;color: #999;}

/*mircoVideo*/
.video-item{width: 270px;height: 320px;position: relative;}
.video-item .imgpan{height: 100%;}
.video-item .imgpan .bg-op06{ background: rgb(0,0,0,0.4); display: block; height: 100%;width: 100%;position: absolute; top: 0; left: 0;}
.video-item .infopan{position: absolute;bottom: 0;border-radius:  0 20px 20px 0;width: 100%;}
.video-item .infopan .p-div1{margin: 0 15px;}
.video-item .infopan .p-div{margin: 0;background: rgb(0 0 0 / 50%);overflow: hidden;padding-bottom: 20px;padding: 0 20px 20px;}
.video-item .infopan .p-div1 .teacher-name{width: 50%;display: inline-block;color: #fff;}
.video-item .infopan .p-div1 .teacher-name span{vertical-align: middle;margin-left: 5px;}
.video-item .infopan .p-div1 .teacher-name img{vertical-align: middle;width: 32px;height: 32px;border-radius: 50%;border: 1px solid #fff;}
.video-item .infopan .p-div1 .time{width: 50%;text-align: right;display: inline-block;color: #fff;}
.video-item .infopan .p-div .title{font-size: 18px;color: #fff;margin: 10px 0 20px 0;height: 26px;overflow: hidden;white-space: nowrap;text-overflow: ellipsis;}
.video-item .infopan .p-div .count{width: 33.3%;display: inline-block;text-align: left;}
.video-item .infopan .p-div .count img{vertical-align: middle;height: 21px;}
.video-item .infopan .p-div .count span{font-size: 16px;color: #fff;margin-left: 3px;vertical-align: middle;}
.video-item .icon_play{position: absolute;right: 20px;top: 20px;}

/*info*/
.video-body {
  height: 100%;
  width: 100%;
  background: #262626;
}

.video-body .top {
  height: 60px;
  line-height: 60px;
  background: #070605;
}

.video-body .top .title {
  font-size: 22px;
  color: #fff;
  display: inline-block;
  margin-left: 20px;
  width: calc(100% - 400px);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.video-body .top .search-box {
  float: right;
  margin-right: 20px;
}

.video-body .top .search-box span {
  border-bottom: 2px solid #fff;
  padding-bottom: 10px;
}

.video-body .top .search-box input {
  border: none;
  background: none;
  outline: none;
  color: #fff;
  vertical-align: middle;
}

.video-body .top .search-box img {
  cursor: pointer;
  vertical-align: middle;
  margin-left: 8px;
}
.video-body .right {width: 27%;display: inline-block;vertical-align: top;position: absolute;top: 0;height: 100%;overflow: hidden;}

.video-body .video-con {
  width: 100%;
  height: calc(100% - 60px);
  text-align: center;
  display: inline-block;
  position: relative;
}

.video-body video {
  height: 100%;
  max-width: 100%;
  background: #353435 !important;
}

.video-body .jw-icon-inline,
.video-body .jw-icon-tooltip,
.video-body .jw-slider-horizontal,
.video-body .jw-text-duration,
.video-body .jw-text-elapsed {
  height: 12px !important;
  line-height: 12px !important;
}

.video-body .jw-skin-bce .jw-controlbar {
  height: 1.5em !important;
}

.video-body .jw-skin-bce .jw-controlbar-right-group .jw-icon-playrate .jw-playrate-label {
  line-height: 10px !important;
}

.video-body .control-div {
  position: absolute;
  right: 20px;
  bottom: 20px;
  text-align: center;
}
.video-body .control-div .arrows{width: 40px;margin: 0 auto 20px;}
.video-body .control-div .arrow-prev{cursor: pointer; display: block;  background: url(/microVideo/icon_arrow_t_d.png) no-repeat;   background-position: center; width: 40px;height: 48px;line-height: 48px;background-color: #383a48; border-radius:  20px 20px 0 0;}
.video-body .control-div .arrow-prev:hover{background: url(/microVideo/icon_arrow_t_s.png) no-repeat;background-position: center;background-color: #383a48; }
.video-body .control-div .arrow-next{cursor: pointer; display: block;  background: url(/microVideo/icon_arrow_b_d.png) no-repeat;   background-position: center; width: 40px;height: 48px;line-height: 48px;background-color: #383a48; border-radius:  0 0 20px 20px; }
.video-body .control-div .arrow-next:hover{ background: url(/microVideo/icon_arrow_b_s.png) no-repeat; background-position: center;background-color: #383a48; }
.video-body .control-div .user{text-align: center;color: #fff;margin-bottom: 30px;font-size: 18px;}
.video-body .control-div .user img{margin-bottom: 10px;width: 48px;height: 48px;border-radius: 50%;}
.video-body .nums .top2{    margin-bottom: 18px;text-align: center;}
.video-body .nums .top2 img{height: 24px;}
.video-body .nums .top2 .font_t{font-size: 16px;color:#fff;margin-top: 5px;}

.video-body .right .con{width: 100%;position: relative;height: 100%;}
.video-body .comment-con-box {
  height: 100%;
  background: #262730;
  position: relative;
  border-radius: 10px;
  margin-bottom: 68px;
}
.video-body .comment-con-box .close{    font-size: 30px;
  color: #fff;
  float: right;
  margin-right: 20px;
  margin-top: 10px;
  cursor: pointer;
}
.video-body .comment-list {
  /* margin: 10px 20px; */
  height: calc(100% - 110px);
}
.video-body .comment-list .el-scrollbar{    height: calc(100% - 15px);
  margin: 0 20px;}
.video-body .comment-list .item {
  margin-top: 20px;
}
.video-body .comment-list .item .m {
  width: calc(100% - 60px);
  display: inline-block;
  margin-left: 15px;
  vertical-align: top;
  font-size: 14px;
  line-height: 24px;
  color: #fff;
}

.video-body .comment-list .item .img_commnet_user {
  width: 40px;
  height: 40px;
  vertical-align: middle;
  border-radius: 50%;
}

.video-body .comment-list .item .icon_comment_like {
  width: 16px;
  height: 16px;
  vertical-align: middle;
}

.video-body .comment-list .item .user-icon-box {
  line-height: 24px;
  vertical-align: middle;
}

.video-body .comment-list .item .username {
  font-size: 18px;
  vertical-align: middle;
  color: #d8d8d8;
}

.video-body .comment-list .item .time {
  color: #999;
  font-size: 14px;
  vertical-align: middle;
  margin-left: 10px;
}



.video-body .comment-list .item  .content {
  margin-top: 10px;
  font-size: 16px;
  color: #f5f5f5;
  padding-bottom: 15px;
  border-bottom: 1px solid #5c5d5e;
  word-break: break-all;
}

.video-body .comment-list .item  .icon_arrow_up {
  position: absolute;
  top: 12px;
  left: 10px;
}
.video-body .comment-list .item .ft .num {
  color: #999;
  font-size: 16px;
  margin-right: 6px;
  vertical-align: middle;
}

.video-body .comment-list .item  .ft {
  vertical-align: middle;
  float: right;
}
.video-body .comment-list .item  .ft img{vertical-align: middle;}
.video-body .comment-con-box .htitle {
  text-align: left;
  height: 56px;
  line-height: 56px;
  color: #fff;
  text-indent: 15px;
  font-size: 20px;
}
.video-body .comment-con-box .more{
  text-align: center;
  cursor: pointer;
  font-size: 14px;
  background: #999;
  border-radius: 4px;
  padding: 4px;
  margin: 10px 0;
  }
.video-body .text-con input {
  /* width: 430px; */
  height: 42px;
  margin: 10px 3%;
  line-height: 42px;
  text-indent: 10px;
  color: #a1a1a1;
  outline: none;
  background: #000;
  border: none;
  padding-right: 56px;
  width: calc(94% - 56px);
  border-radius: 6px;
}

.video-body .text-con  .submitbtn{cursor: pointer; display:inline-block;position: absolute; right: 32px; bottom: 20px;width: 24px;height: 24px;background: url(/microVideo/icon_comment_btn_n.png);}
.video-body .text-con  .submitbtn:hover{background: url(/microVideo/icon_comment_btn_s.png);}
.video-body .text-con {
  position: fixed;
  bottom: 0;
  width: 100%;
  background: #262730;
  /* left: 0; */
  width: 27%;
  right: 0;
}

.video-body .searchbox {position: absolute;right: 10px;top:6px;background: #030304;border-radius: 6px;z-index: 9;padding: 0 10px 20px;height: calc(100% - 51px);width: 215px;}
.video-body .searchbox .title{color: #fff;font-size: 16px;text-align: left;height: 46px;line-height: 46px;}
.video-body .searchbox .close{float: right;font-size: 24px;color: #fff;margin-top: 7px;cursor: pointer;}
.video-body .searchbox .item {cursor: pointer; width: 215px;height: 125px;background: #313232;border: 1px solid #4a4b4b;margin-bottom: 15px;position: relative;}
.video-body .searchbox .item .name{
    height: 26px;
    background: #000;
    color: #fff;
    font-size: 12px;
    line-height: 26px;
    white-space: nowrap;
    /* padding: 0 10px; */
    /* overflow: hidden; */
}
.video-body .searchbox .item .name div{width:95%;margin: auto;overflow: hidden;text-overflow: ellipsis;}
.video-body .searchbox .item img{height: 96px;}
.video-body .searchbox .item .icon_play {
  position: absolute;
  top: 45px;
  width: 40px;
  height: 40px;
  margin-top: -15px;
  left: 50%;
  margin-left: -15px;
}
.video-body .searchbox .el-scrollbar{height: calc(100% - 20px);}
