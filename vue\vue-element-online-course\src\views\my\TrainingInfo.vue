<template>
    <div>
        <div v-if="trainInfo != undefined" class="course_info_box">
            <div class="breadcrumb-box">
                <el-breadcrumb
                    separator-class="el-icon-arrow-right"
                    class="bread_con"
                >
                    <el-breadcrumb-item :to="{ path: '/' }">
                        <i class="el-icon-location-outline" />
                    </el-breadcrumb-item>
                    <el-breadcrumb-item>
                        我的培训
                    </el-breadcrumb-item>
                    <el-breadcrumb-item>{{
                        trainInfo.name
                    }}</el-breadcrumb-item>
                </el-breadcrumb>
            </div>
            <div class="left_con">
                <div class="trainInfo">
                    <img src="/images/icon-train.png" />
                    <div class="right">
                        <div class="title">
                            {{ trainInfo.name }}
                        </div>
                        <div class="time">
                            培训时间：{{
                                trainInfo.startDate | DateFromte("YYYY-MM-DD")
                            }}
                            至
                            {{ trainInfo.endDate | DateFromte("YYYY-MM-DD") }}
                        </div>
                        <div class="num">
                            参与人数：{{ trainInfo.userCount }}人
                        </div>

                        <span
                            v-if="trainInfo.trainState == 0"
                            class="state unstarted"
                            >未开始</span
                        >
                        <span
                            v-if="trainInfo.trainState == 1"
                            class="state isTraining"
                            >培训中</span
                        >
                        <span
                            v-if="trainInfo.trainState == 2"
                            class="state isfinished"
                            >已结束</span
                        >
                    </div>
                </div>
                <div class="desc-box">
                    <div class="tabslist">
                        <a
                            v-if="trainInfo.isShowCourse"
                            :class="tabIndex == 0 ? 'active' : ''"
                            @click="tabIndex = 0"
                            >培训课程</a
                        >
                        <a
                            v-if="trainInfo.isShowLive"
                            :class="tabIndex == 1 ? 'active' : ''"
                            @click="tabIndex = 1"
                            >直播课程</a
                        >
                        <a
                            v-if="trainInfo.isShowExam"
                            :class="tabIndex == 2 ? 'active' : ''"
                            @click="tabIndex = 2"
                            >培训考试</a
                        >
                        <a
                            :class="tabIndex == 3 ? 'active' : ''"
                            @click="tabIndex = 3"
                            >培训公告</a
                        >
                    </div>
                    <div v-show="tabIndex == 0" class="box">
                        <div
                            v-for="item in trainInfo.trainCourses"
                            class="item-course"
                            @click="prewCourse(item.courseId, item.isExpire)"
                        >
                            <img :src="item.coverUrl" />
                            <div class="fr">
                                <div class="title">
                                    {{ item.courseName }}
                                </div>
                                <div class="info-box">
                                    <span class="item">
                                        <svg
                                            xmlns="http://www.w3.org/2000/svg"
                                            xmlns:xlink="http://www.w3.org/1999/xlink"
                                            width="16px"
                                            height="16px"
                                        >
                                            <path
                                                fill-rule="evenodd"
                                                fill="rgb(146, 146, 146)"
                                                d="M15.012,16.010 C14.469,16.010 14.031,15.563 14.025,15.010 L13.846,15.010 C12.923,12.152 8.112,10.038 8.009,9.993 C8.006,9.993 8.003,9.994 8.000,9.994 C7.997,9.994 7.993,9.993 7.991,9.993 C7.888,10.038 3.077,12.152 2.154,15.010 L1.975,15.010 C1.969,15.563 1.531,16.010 0.988,16.010 C0.443,16.010 -0.000,15.558 -0.000,15.000 C-0.000,14.640 0.195,14.339 0.472,14.160 C1.475,12.065 3.938,10.293 5.777,9.195 C4.379,8.340 2.985,6.853 2.985,4.997 C2.985,2.025 5.008,-0.000 8.000,-0.000 C10.992,-0.000 13.015,2.025 13.015,4.997 C13.015,6.853 11.621,8.340 10.223,9.195 C12.062,10.293 14.525,12.065 15.528,14.160 C15.805,14.339 16.000,14.640 16.000,15.000 C16.000,15.558 15.557,16.010 15.012,16.010 ZM10.998,5.009 C10.998,3.168 9.845,2.009 8.000,2.009 C6.155,2.009 5.002,3.168 5.002,5.009 C5.002,6.850 7.012,8.037 8.000,8.009 C8.988,7.982 10.998,6.850 10.998,5.009 Z"
                                            />
                                        </svg>
                                        <span>{{ item.lecturer }}</span>
                                    </span>
                                    <span class="item">
                                        {{ item.classHour }}学时</span
                                    >
                                    <span class="item classhour"
                                        >已获学时:{{
                                            item.recordInfo != undefined
                                                ? item.recordInfo.classHour
                                                : 0
                                        }}</span
                                    >
                                </div>
                                <div class="type">
                                    <span
                                        :class="[
                                            item.isRequired ? 'type1' : 'type2',
                                        ]"
                                        >{{
                                            item.isRequired
                                                ? "必修课"
                                                : "选修课"
                                        }}</span
                                    >
                                </div>
                                <button v-if="item.isExpire" class="btn end">
                                    已到期
                                </button>
                                <button
                                    v-else-if="item.lastLearnTime == null"
                                    class="btn start"
                                >
                                    开始学习
                                </button>
                                <button
                                    v-else-if="item.isComplete"
                                    class="btn start"
                                >
                                    学习回顾
                                </button>
                                <button v-else class="btn start">
                                    继续学习
                                </button>
                            </div>
                        </div>
                        <NoContent
                            v-if="
                                trainInfo.trainCourses == null ||
                                trainInfo.trainCourses.length == 0
                            "
                        />
                    </div>
                    <div v-show="tabIndex == 1" class="box">
                        <div
                            v-for="item in trainLivelist"
                            class="train-live-item"
                        >
                            <div class="con">
                                <img
                                    :src="item.liveStream.coverImage"
                                    class="coverImage"
                                />
                                <div
                                    v-if="
                                        item.liveStream.liveStreamStatue === 0
                                    "
                                    class="live_state live_state_box_s"
                                >
                                    <img src="/images/icon_play.png" />
                                    <span class="live_span">未开始</span>
                                </div>
                                <div
                                    v-if="
                                        item.liveStream.liveStreamStatue === 1
                                    "
                                    class="live_state live_state_box_p"
                                >
                                    <img
                                        src="data:image/gif;base64,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"
                                        alt=""
                                    />
                                    <span class="live_span">直播中</span>
                                </div>
                                <div
                                    v-if="
                                        item.liveStream.liveStreamStatue ===
                                            2 && item.liveStream.allowPlayBack
                                    "
                                    class="live_state live_state_box_e live_play_back"
                                >
                                    <img src="/images/icon_play.png" />
                                    <span class="live_span">回放</span>
                                </div>
                                <div
                                    v-if="
                                        item.liveStream.liveStreamStatue ===
                                            2 && !item.liveStream.allowPlayBack
                                    "
                                    class="live_state live_state_box_e"
                                >
                                    <img src="/images/icon_noplay.png" />
                                    <span class="live_span">回放已禁</span>
                                </div>
                            </div>
                            <div class="live-right-div">
                                <div class="title">
                                    {{ item.liveStream.title }}
                                </div>

                                <div class="info-box">
                                    <span class="lecturer"
                                        >主讲人：{{
                                            item.liveStream.lecturer
                                        }}</span
                                    >
                                </div>
                                <div class="info-box">
                                    <span class="time">
                                        <img src="/images/icon-timelong.png" />
                                        <span>
                                            {{
                                                item.liveStream.startTime
                                                    | DateFromte(
                                                        "YYYY.MM.DD HH:mm"
                                                    )
                                            }}
                                            -
                                            {{
                                                item.liveStream.endTime
                                                    | DateFromte(
                                                        "YYYY.MM.DD HH:mm"
                                                    )
                                            }}</span
                                        >
                                    </span>
                                </div>
                                <div class="opr-btns">
                                    <button
                                        v-if="
                                            item.liveStream.liveStreamStatue ==
                                                1 ||
                                            item.liveStream.liveStreamStatue ==
                                                0
                                        "
                                        class="statue2"
                                        @click="prewLive(item.liveStream, 0)"
                                    >
                                        客户端入口
                                    </button>
                                    <button
                                        v-if="
                                            item.liveStream.liveStreamStatue ==
                                                1 ||
                                            item.liveStream.liveStreamStatue ==
                                                0
                                        "
                                        class="statue2"
                                        @click="prewLive(item.liveStream, 1)"
                                    >
                                        网页入口
                                    </button>
                                    <button
                                        v-else-if="
                                            !item.liveStream.allowPlayBack
                                        "
                                        class="statue1"
                                        disabled="disabled"
                                    >
                                        回放已禁
                                    </button>
                                    <button
                                        v-else-if="!item.liveStream.hasPlayBack"
                                        class="statue1"
                                        disabled="disabled"
                                    >
                                        暂无回放
                                    </button>
                                    <button
                                        v-else
                                        class="statue2"
                                        @click="prewLive(item.liveStream)"
                                    >
                                        观看回放
                                    </button>
                                    <button
                                        class="btn_enter"
                                        v-if="
                                            (item.liveStream.closeDate ==
                                                null ||
                                                (item.liveStream.closeDate !=
                                                    null &&
                                                    new Date() <
                                                        new Date(
                                                            item.liveStream.closeDate
                                                        ))) &&
                                            item.examinationId != null
                                        "
                                        @click="intoExam(item)"
                                    >
                                        进入考评
                                    </button>
                                    <span class="classhour"
                                        >已获学时：{{ item.classHour }}</span
                                    >
                                </div>
                            </div>
                        </div>
                        <NoContent
                            v-if="
                                trainLivelist == null ||
                                trainLivelist.length == 0
                            "
                        />
                    </div>
                    <div v-show="tabIndex == 2" class="box">
                        <div
                            v-for="item in examlist"
                            class="item-course"
                            @click="prewExam(item)"
                        >
                            <img
                                src="/images/icon-exam.png"
                                style="width: 100px; height: 100px"
                            />
                            <div class="fr fr1">
                                <div class="title">
                                    {{ item.name }}
                                </div>
                                <div class="time">
                                    考试时间：{{
                                        item.startDate
                                            | DateFromte("YYYY.MM.DD HH:mm")
                                    }}
                                    -
                                    {{
                                        item.endDate
                                            | DateFromte("YYYY.MM.DD HH:mm")
                                    }}
                                </div>
                                <span
                                    v-if="item.status == 1"
                                    class="state unstarted"
                                    >未开始</span
                                >
                                <span
                                    v-if="item.status == 2"
                                    class="state isTraining"
                                    >进行中</span
                                >
                                <span
                                    v-else-if="item.status == 3"
                                    class="state isfinished"
                                    >已结束</span
                                >
                                <span class="examState">
                                    <span v-if="item.submitTimes == 0"
                                        >未参与</span
                                    >
                                    <img
                                        v-else-if="
                                            item.lastScore <
                                            trainInfo.examPassScore
                                        "
                                        src="/images/img_fail.png"
                                    />
                                    <img v-else src="/images/img_pass.png" />
                                </span>
                                <button
                                    v-if="item.status == 2"
                                    class="btn start"
                                >
                                    进入考试
                                </button>
                                <button
                                    v-if="item.status == 3"
                                    class="btn start"
                                >
                                    查看结果
                                </button>
                            </div>
                        </div>
                        <NoContent
                            v-if="examlist == null || examlist.length == 0"
                        />
                    </div>
                    <div v-show="tabIndex == 3" class="box">
                        <div
                            class="notic_content"
                            v-html="trainInfo.notice"
                        ></div>
                    </div>
                    <el-dialog
                        :visible.sync="dayVisiable"
                        class="day-schedule-dialog"
                        :show-close="false"
                    >
                        <div class="h_title">
                            <div class="title">课程安排</div>
                            <a class="close" @click="dayVisiable = false"
                                >&times;</a
                            >
                        </div>
                        <span class="day-span">
                            {{ chooseDay }}
                        </span>
                        <el-scrollbar
                            class="day-schedule-list-scroll"
                            scroll-y="true"
                        >
                            <div class="day-schedule-list">
                                <div
                                    v-for="item in schedule.liveList"
                                    class="train-live-item"
                                >
                                    <div class="con">
                                        <img
                                            :src="item.liveStream.coverImage"
                                            class="coverImage"
                                        />
                                        <div
                                            v-if="
                                                item.liveStream
                                                    .liveStreamStatue === 0
                                            "
                                            class="live_state live_state_box_s"
                                        >
                                            <img src="/images/icon_play.png" />
                                            <span class="live_span"
                                                >未开始</span
                                            >
                                        </div>
                                        <div
                                            v-if="
                                                item.liveStream
                                                    .liveStreamStatue === 1
                                            "
                                            class="live_state live_state_box_p"
                                        >
                                            <img
                                                src="data:image/gif;base64,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"
                                                alt=""
                                            />
                                            <span class="live_span"
                                                >直播中</span
                                            >
                                        </div>
                                        <div
                                            v-if="
                                                item.liveStream
                                                    .liveStreamStatue === 2 &&
                                                item.liveStream.allowPlayBack
                                            "
                                            class="live_state live_state_box_e live_play_back"
                                        >
                                            <img src="/images/icon_play.png" />
                                            <span class="live_span">回放</span>
                                        </div>
                                        <div
                                            v-if="
                                                item.liveStream
                                                    .liveStreamStatue === 2 &&
                                                !item.liveStream.allowPlayBack
                                            "
                                            class="live_state live_state_box_e"
                                        >
                                            <img
                                                src="/images/icon_noplay.png"
                                            />
                                            <span class="live_span"
                                                >回放已禁</span
                                            >
                                        </div>
                                    </div>
                                    <div class="live-right-div">
                                        <div class="title">
                                            {{ item.liveStream.title }}
                                        </div>
                                        <div class="info-box">
                                            <span class="lecturer"
                                                >主讲人：{{
                                                    item.liveStream.lecturer
                                                }}</span
                                            >
                                        </div>
                                        <!-- <div class="info-box">
                      <span class="time">
                        <img src="/images/icon-timelong.png">
                        <span> {{ item.liveStream.startTime | DateFromte("YYYY.MM.DD HH:mm") }} - {{item.liveStream.endTime | DateFromte("YYYY.MM.DD HH:mm")}}</span>
                      </span>
                      <span class="classhour">已获学时：{{item.classHour}}</span>
                    </div> -->
                                        <div class="opr-btns">
                                            <button
                                                v-if="
                                                    item.liveStream
                                                        .liveStreamStatue ==
                                                        1 ||
                                                    item.liveStream
                                                        .liveStreamStatue == 0
                                                "
                                                class="statue2"
                                                @click="
                                                    prewLive(item.liveStream, 0)
                                                "
                                            >
                                                客户端入口
                                            </button>
                                            <button
                                                v-if="
                                                    item.liveStream
                                                        .liveStreamStatue ==
                                                        1 ||
                                                    item.liveStream
                                                        .liveStreamStatue == 0
                                                "
                                                class="statue2"
                                                @click="
                                                    prewLive(item.liveStream, 1)
                                                "
                                            >
                                                网页入口
                                            </button>
                                            <button
                                                v-else-if="
                                                    !item.liveStream
                                                        .allowPlayBack
                                                "
                                                class="statue1"
                                                disabled="disabled"
                                            >
                                                回放已禁
                                            </button>
                                            <button
                                                v-else-if="
                                                    !item.liveStream.hasPlayBack
                                                "
                                                class="statue1"
                                                disabled="disabled"
                                            >
                                                暂无回放
                                            </button>
                                            <button
                                                v-else
                                                class="statue2"
                                                @click="
                                                    prewLive(item.liveStream)
                                                "
                                            >
                                                观看回放
                                            </button>
                                            <button
                                                class="btn_enter"
                                                v-if="
                                                    (item.liveStream
                                                        .closeDate == null ||
                                                        (item.liveStream
                                                            .closeDate !=
                                                            null &&
                                                            new Date() <
                                                                new Date(
                                                                    item.liveStream.closeDate
                                                                ))) &&
                                                    item.examinationId != null
                                                "
                                                @click="intoExam(item)"
                                            >
                                                进入考评
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div
                                    v-for="item in schedule.examList"
                                    class="item-course"
                                    @click="prewExam(item)"
                                >
                                    <img
                                        src="/images/icon-exam.png"
                                        style="width: 100px; height: 100px"
                                    />
                                    <div class="fr fr1">
                                        <div class="title">
                                            {{ item.name }}
                                        </div>
                                        <div class="time">
                                            考试时间：{{
                                                item.startDate
                                                    | DateFromte(
                                                        "YYYY.MM.DD HH:mm"
                                                    )
                                            }}
                                            -
                                            {{
                                                item.endDate
                                                    | DateFromte(
                                                        "YYYY.MM.DD HH:mm"
                                                    )
                                            }}
                                        </div>
                                        <span
                                            v-if="item.status == 1"
                                            class="state unstarted"
                                            >未开始</span
                                        >
                                        <span
                                            v-if="item.status == 2"
                                            class="state isTraining"
                                            >进行中</span
                                        >
                                        <span
                                            v-else-if="item.status == 3"
                                            class="state isfinished"
                                            >已结束</span
                                        >
                                        <button
                                            v-if="item.status == 2"
                                            class="btn start"
                                        >
                                            进入考试
                                        </button>
                                        <button
                                            v-if="item.status == 3"
                                            class="btn start"
                                        >
                                            查看结果
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </el-scrollbar>
                        <div class="ts-box">
                            温馨提示：课程安排仅包含直播课程、培训考试，培训课程请参见培训课程列表
                        </div>
                    </el-dialog>
                </div>
            </div>
            <div class="right_con">
                <div class="total_div">
                    <h2 class="box-title">
                        <span class="line"></span><span>学习进度</span>
                    </h2>
                    <div class="ft">
                        <div class="item">
                            {{ userClassHour }}
                            <span>已获学时</span>
                        </div>
                        <div class="h-line"></div>
                        <div class="item total">
                            {{ totalClassHour }}
                            <span>总学时</span>
                        </div>
                    </div>
                </div>
                <div class="sign-box">
                    <img src="/images/sign-bg.png" />
                    <button
                        class="sign-btn"
                        @click="kcSign()"
                        :disabled="IsfindRecord(today)"
                    >
                        {{ IsfindRecord(today) ? "今日已签到" : "学习签到" }}
                    </button>
                </div>
                <div class="notic_box">
                    <h2 class="box-title">
                        <span class="line"></span><span>课程日程</span>
                    </h2>
                    <div>
                        <el-calendar>
                            <!-- 这里使用的是 2.5 slot 语法，对于新项目请使用 2.6 slot 语法-->
                            <template
                                slot="dateCell"
                                slot-scope="{ date, data }"
                            >
                                <div
                                    :class="[
                                        data.isSelected ? 'is-selected' : '',
                                        'day-box',
                                    ]"
                                    @click="dayclick(data.day)"
                                >
                                    <span>{{
                                        parseInt(data.day.split("-")[2])
                                    }}</span>
                                    <img
                                        v-if="IsfindRecord(data.day)"
                                        src="/images/sign-actived.png"
                                    />
                                    <img
                                        v-else
                                        src="/images/sign-unactived.png"
                                    />
                                </div>
                            </template>
                        </el-calendar>
                    </div>
                </div>
            </div>
        </div>
        <el-dialog title="提示" :visible.sync="liveDialog" width="30%">
            <div>
                即将打开车博苑直播端，请稍后 <br /><br />
                如果您未安装直播客户端，请<a :href="downClient"
                    >下载车博苑直播端</a
                >
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button type="primary" @click="liveDialog = false"
                    >确 定</el-button
                >
            </span>
        </el-dialog>
    </div>
</template>
<script>
import {
    getTrainsDetail,
    getTrainExams,
    getTrainUserRecord,
    getMyTrainLives,
    signTrain,
    getSignsRecord,
    getExamInfo,
} from "@/api/course";
import { myLiveUrl, myLiveBackUrl } from "@/api/user";
import { getByLive } from "@/api/exam";
import config from "@/config";
import NoContent from "@/components/NoContent";
import moment from "moment";
export default {
    name: "MyTraining",
    components: {
        NoContent,
    },
    data() {
        return {
            trainInfo: {},
            examlist: [],
            record: {},
            trainLivelist: [],
            signRecord: [],
            tabIndex: 0, // 切换tab
            exam_query: {
                TrainId: this.$route.query.id,
                ExaminationType: 1,
                SkipCount: 0,
                MaxResultCount: 20,
            },
            userClassHour: 0,
            totalClassHour: 0,
            today: "",
            dayVisiable: false,
            schedule: {}, // 日程
            chooseDay: "",
            liveDialog: false,
            downClient: "",
            isClient: false,
        };
    },
    async mounted() {
        this.today = moment().format("YYYY-MM-DD");
        await getTrainUserRecord(this.$route.query.id).then((res) => {
            // 培训课程记录
            this.record = res;
        });
        await getTrainsDetail(this.$route.query.id).then((res) => {
            // 培训详情
            this.trainInfo = res;
            for (
                let index = 0;
                index < this.trainInfo.trainCourses.length;
                index++
            ) {
                // 培训课程
                const element = this.trainInfo.trainCourses[index];
                var obj = this.record.trainUserCourseRecords.find(
                    (x) => x.courseId == element.courseId
                );
                this.trainInfo.trainCourses[index].recordInfo = obj;
                if (obj != null) this.userClassHour += obj.classHour; // 用户总学时
                this.totalClassHour +=
                    this.trainInfo.trainCourses[index].classHour; // 总学时
            }
            if (this.trainInfo.isShowCourse) this.tabIndex = 0;
            else if (this.trainInfo.isShowLive) this.tabIndex = 1;
            else if (this.trainInfo.isShowExam) this.tabIndex = 2;
            else this.tabIndex = 3;
        });

        await getMyTrainLives({ TrainId: this.$route.query.id }).then((res) => {
            this.trainLivelist = res.items;
            for (let index = 0; index < this.trainLivelist.length; index++) {
                this.userClassHour += this.trainLivelist[index].classHour; // 用户总学时
                this.totalClassHour +=
                    this.trainLivelist[index].liveStream.classHour; // 总学时
            }
        });
        getTrainExams(this.exam_query).then((res) => {
            this.examlist = res.items;
        });
        // 获取签到记录
        getSignsRecord({ trainId: this.$route.query.id }).then((res) => {
            this.signRecord = res.items;
        });
        this.OSnow();
    },
    methods: {
        IsfindRecord(day) {
            // 判断是否签到
            var record = this.signRecord.find(
                (x) => moment(x.signTime).format("YYYY-MM-DD") == day
            );
            if (record != undefined) return true;
            else return false;
        },
        dayclick(day) {
            this.chooseDay = moment(day).format("YYYY年MM月DD日");
            this.schedule.liveList = this.trainLivelist.filter((x) => {
                if (
                    new Date(moment(x.startTime).format("YYYY-MM-DD")) <=
                        new Date(day) &&
                    new Date(day) <= new Date(x.endTime)
                )
                    return true;
                else return false;
            });
            this.schedule.examList = this.examlist.filter((x) => {
                if (
                    new Date(moment(x.startDate).format("YYYY-MM-DD")) <=
                        new Date(day) &&
                    new Date(day) <= new Date(x.endDate)
                ) {
                    return true;
                } else return false;
            });

            if (
                this.schedule.liveList.length != 0 ||
                this.schedule.examList.length != 0
            )
                this.dayVisiable = true;
            else {
                this.$message.info(
                    "暂无直播课和考试日程安排，培训课程请查看列表页"
                );
            }
        },
        prewCourse(id, isExpire) {
            if (this.trainInfo.trainState == 0) {
                this.$message.info("培训还未开始,无法查看课程");
            } else if (isExpire) {
                this.$message.info("培训已到期,无法查看课程");
            } else {
                if (this.trainInfo.learnInOrder) {
                    const courseInfo = this.trainInfo.trainCourses.find(
                        (item) => item.courseId == id
                    );
                    let resultItem = this.trainInfo.trainCourses.filter(
                        function (item) {
                            return item.order < courseInfo.order;
                        }
                    );
                    const complete = resultItem.find(
                        (item) =>
                            item.recordInfo == undefined ||
                            !item.recordInfo.isComplete
                    );
                    if (complete) {
                        this.$message.info("请先学完前面的课程");
                        return;
                    }
                }
                this.$router.push({
                    name: "CourseInfo",
                    query: { id: id, trainId: this.$route.query.id },
                });
            }
        },
        kcSign() {
            signTrain({
                trainId: this.$route.query.id,
                signTime: moment().format("YYYY-MM-DD"),
            }).then((res) => {
                this.$message.success("签到成功");
                this.signRecord.push({
                    trainId: this.$route.query.id,
                    signTime: moment().format("YYYY-MM-DD"),
                });
            });
        },
        prewLive(item, t) {
            // console.log(item)
            // if (!this.userLogin) {
            //   this.$store.dispatch("user/toggleloginbox", true);
            // } // 未登录
            // else if(item.liveStreamStatue==0){ //未开始
            // 	this.$message.info("直播还未开始，请耐心等待")
            // }
            if (item.liveStreamStatue == 1 || item.liveStreamStatue == 0) {
                //进行中
                this.intoLive(item.id, t);
            } else if (!item.allowPlayBack) {
                //回放禁止
                this.$message.info("该直播已禁止回放,无法观看");
            } else if (!item.hasPlayBack) {
                //暂无回放
                this.$message.info("该直播暂时未生成回放,无法观看");
            } else {
                //console.log(item.id);
                this.viewBackVideo(item.id);
            }
        },
        async viewBackVideo(id) {
            var res = await myLiveBackUrl(id);
            if (this.isClient) {
                window.open(res, "_self");
            } else {
                window.open(res, "_blank");
            }
        },
        async intoLive(id, t) {
            var res = await myLiveUrl(id);

            if (this.isClient) {
                window.open(res.webUrl, "_self");
            } else if (!this.isClient && t === 1) {
                window.open(res.webUrl, "_blank");
            } else {
                res.clientUrl = res.clientUrl.replace(
                    "baijiacloud",
                    "huizhixueyuan"
                );
                this.getHref(res.clientUrl);
            }
            // } else {
            //   res.clientUrl = res.clientUrl.replace('baijiacloud', 'huizhixueyuan');
            //   this.getHref(res.clientUrl);
            // }
            // if(res.userRole==0){ // 0 学生 1 教师
            // 	window.open(res.webUrl,'_blank')
            // } else {
            // 	window.open(res.webUrl,'_blank')
            // 	//this.getHref(res.clientUrl)
            // }
        },
        getbackurl(id) {
            return new Promise((resolve, reject) => {
                myLiveBackUrl({ id: id }).then(
                    (res) => {
                        resolve(res);
                    },
                    (error) => {}
                );
            });
        },
        getHref(url) {
            var isFirefox = navigator.userAgent.indexOf("Firefox") > -1; // 是否是火狐  ，火狐内核Gecko
            var isWebKit = navigator.userAgent.indexOf("WebKit") > -1; // 是否是WebKit 内核
            var isChrome = navigator.userAgent.indexOf("Chrome") > -1; // 是否是谷歌
            var isTrident = navigator.userAgent.indexOf("Trident") > -1; // 是否是IE内核
            var isIeL = !!window.ActiveXObject || "ActiveXObject" in window;
            if (isFirefox || isWebKit || isChrome || isTrident || isIeL) {
                // IE和火狐用window.open打开
                // 调起客户端 5秒之后自动关闭调起窗口
                var client = window.open(url);
                setTimeout(function () {
                    if (client) {
                        client.close(); //关闭新打开的浏览器窗口，避免留下一个空白窗口
                    }
                }, 5000);
            } else {
                //其它浏览器使用模拟<a>标签`click`事件的形式调起
                var a = document.createElement("a");
                a.setAttribute("href", url);
                document.body.appendChild(a);
                a.click();
            }
            setTimeout(() => {
                // 5秒之后不管有没有调起都弹窗提示下载客户端
                this.liveDialog = true;
                setTimeout(() => {
                    // 5秒之后关闭
                    this.liveDialog = false;
                }, 8000);
            }, 5000);
        },
        OSnow() {
            var agent = navigator.userAgent.toLowerCase();
            var isMac = /macintosh|mac os x/i.test(navigator.userAgent);
            if (agent.indexOf("win32") >= 0 || agent.indexOf("wow32") >= 0) {
                // this.isMac = false;
                this.downClient =
                    "https://baogang.bj.bcebos.com/installer/Huizhixueyuaninstaller.exe";
            }
            if (agent.indexOf("win64") >= 0 || agent.indexOf("wow64") >= 0) {
                //this.isMac = false;
                this.downClient =
                    "https://baogang.bj.bcebos.com/installer/Huizhixueyuaninstaller.exe";
            }
        },
        async intoExam(item) {
            await getByLive({ liveId: item.liveStream.id }).then((res) => {
                item.isPublish = res.isPublish;
                if (!item.isPublish) {
                    //未发布
                    this.$message.info("测评未开始");
                } else {
                    if (res.examPass) {
                        this.$confirm(
                            "您本次考评已经通过，确定需要重新答题吗？",
                            "温馨提示",
                            {
                                confirmButtonText: "确定",
                                cancelButtonText: "取消",
                                type: "warning",
                            }
                        )
                            .then(() => {
                                this.$router.push({
                                    name: "LiveEvaluate",
                                    query: {
                                        ExamId: item.examinationId,
                                        name: item.liveStream.title,
                                        passScore: item.passScore,
                                    },
                                });
                            })
                            .catch(() => {});
                    } else {
                        this.$router.push({
                            name: "LiveEvaluate",
                            query: {
                                ExamId: item.examinationId,
                                name: item.liveStream.title,
                                passScore: item.passScore,
                            },
                        });
                    }
                }
            });
        },
        async prewExam(item) {
            await getExamInfo(item.examinationId).then((res) => {
                item.submitTimes = res.submitTimes;
                // console.log(res)
            });
            // console.log(item)

            var returnUrl =
                config.returnIP + "/#/TrainingInfo?id=" + this.$route.query.id;
            // if (item.status == 1) {
            //   this.$message.info('考试还没有开始，请耐心等待')
            //   return
            // }

            if (
                item.status == 1 ||
                (item.status == 2 &&
                    (item.submitTimes == 0 ||
                        (item.allowRepeatSubmit &&
                            item.submitTimes < item.allowSubmitTimes)))
            ) {
                //开始考试
                var url1 =
                    "/start?examId=" +
                    item.examinationId +
                    "&starttime=" +
                    item.startDate +
                    "&submitLimitTime=" +
                    item.submitLimitTime +
                    "&examName=" +
                    item.name +
                    "&timeLong=" +
                    item.timeLong +
                    "&returnUrl=" +
                    encodeURIComponent(returnUrl);
                var urlencode = encodeURIComponent(url1);
                var url =
                    config.examIP +
                    "/#/auth-redirect?redirect=" +
                    urlencode +
                    "&token=" +
                    this.$store.getters.token;
                window.open(url, "_blank");
            } else if (
                item.status == 2 &&
                item.submitTimes > 0 &&
                !(
                    item.allowRepeatSubmit &&
                    item.submitTimes < item.allowSubmitTimes
                )
            ) {
                //进行中 已提交 不允许重复提交， 无法重复进入考核
                this.$message.info("您进入考试次数达到上限");
                return;
            } else if (item.status == 3) {
                // 查看结果
                var url1 =
                    "/result?examId=" +
                    item.examinationId +
                    "&returnUrl=" +
                    encodeURIComponent(returnUrl);
                var urlencode = encodeURIComponent(url1);
                var url =
                    config.examIP +
                    "/#/auth-redirect?redirect=" +
                    urlencode +
                    "&token=" +
                    this.$store.getters.token;
                window.open(url, "_blank");
            }
        },
    },
};
</script>
