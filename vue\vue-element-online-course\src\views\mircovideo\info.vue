<template>
  <div class="video-body">
    <div
      class="top"
      :style="{width:(!IsshowCommentbox?'100%':'73%')}"
    >
      <div class="title">
        {{ info.name }}
      </div>
      <div class="search-box">
        <span>
          <input
            v-model.trim="params.Filter"
            placeholder="搜索视频"
            @keyup.enter="SearchResult()"
          >
          <img
            src="/microVideo/icon-search.png"
            @click="SearchResult()"
          >
        </span>
      </div>
    </div>
    <div
      class="video-con"
      :style="{width:(!IsshowCommentbox?'100%':'73%')}"
    >
      <!-- <img class="icon_play" v-if="!isPlay" src="/mircovideo/icon_play.png" /> -->
      <!-- <video :id="'myVideo'+id"  loop="true" autoplay :src="src" @click="changePlay($event)"></video> -->
      <video id="myPlayer" />
      <div
        v-if="IsshowSearchResult"
        class="searchbox"
      >
        <a
          class="close"
          @click="IsshowSearchResult=false"
        >&times; </a>
        <div class="title">
          搜索结果（{{ params.total }}）
        </div>
        <el-scrollbar>
          <div
            v-for="item in videoList"
            class="item"
            @click="view(item)"
          >
            <img :src="item.thumbnailUrl">
            <img
              class="icon_play"
              src="/microVideo/icon_play1_n.png"
            >
            <div class="name">
              <div>{{ item.name }}</div>
            </div>
          </div>
          <NoContent v-if="videoList.length==0" />
        </el-scrollbar>
      </div>
    </div>
    <div
      class="control-div"
      :style="{'margin-right':(!IsshowCommentbox?'0':'27%')}"
    >
      <div
        class="top2"
        style="cursor: pointer"
        @click="handleBack"
      >
        <img
          class="t_img"
          src="/microVideo/icon_back_n.png"
        >
        <div style="font-size: 16px;color: #fff;margin-bottom: 10px;">
          返回
        </div>
      </div>
      <div class="arrows">
        <span
          class="arrow-prev"
          @click="getPrevOne()"
        />
        <span
          class="arrow-next"
          @click="getNextOne()"
        />
      </div>
      <div class="right_box">
        <div class="user">
          <img
            v-if="info.profilePhoto==null"
            class="avatar_img"
            src="/images/user.png"
          >
          <img
            v-else
            class="avatar_img"
            :src="info.profilePhoto"
          >
          <div class="font_t">
            {{ info.author }}
          </div>
        </div>
        <div
          v-if="info!=undefined"
          class="nums"
        >
          <div
            class="top2"
            style="cursor: pointer"
            @click="like_change"
          >
            <img
              v-if="!info.isLike"
              class="t_img"
              src="/microVideo/icon_like_pc_n.png"
            >
            <img
              v-else
              class="t_img"
              src="/microVideo/icon_like_pc_s.png"
            >
            <div class="font_t">
              {{ info.likeCount | showNum }}
            </div>
          </div>
          <div class="top2">
            <img
              class="t_img"
              src="/microVideo/icon_person_pc.png"
            >
            <div class="font_t">
              {{ info.viewCount | showNum }}
            </div>
          </div>
          <div
            class="top2"
            style="cursor: pointer"
            @click="showCommentbox"
          >
            <img
              class="t_img"
              src="/microVideo/icon_comment_pc.png"
            >
            <div class="font_t">
              {{ info.commentCount | showNum }}
            </div>
          </div>
          <div
            class="top2"
            style="cursor: pointer"
            @click="collect_change"
          >
            <img
              v-if="!info.isCollection"
              class="t_img"
              src="/microVideo/icon_Collection_n.png"
            >
            <img
              v-else
              class="t_img"
              src="/microVideo/icon_Collection_s.png"
            >
            <div class="font_t">
              {{ info.collectionCount | showNum }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      v-if="IsshowCommentbox"
      class="right"
    >
      <div class="con">
        <div class="comment-con-box">
          <a
            class="close"
            @click="IsshowCommentbox=false"
          >&times;</a>
          <div class="htitle">
            全部评论({{ info.commentCount | showNum }})
          </div>
          <div class="comment-list">
            <el-scrollbar>
              <div
                v-for="item in commentList"
                class="item"
                @click="agreeIt(item)"
              >
                <img
                  class="img_commnet_user"
                  src="/images/user.png"
                >
                <div class="m">
                  <div class="user-icon-box">
                    <span class="username">{{ item.author | userNameFormat }}</span>
                    <span class="time">{{ item.creationTime | showTime }} </span>
                    <span class="ft">
                      <span class="num">{{ item.agreeCount }}</span>
                      <img
                        v-if="!item.isAgree"
                        class="like"
                        src="/microVideo/icon_like2_n.png"
                      >
                      <img
                        v-else
                        mode="contain"
                        class="like"
                        src="/microVideo/icon_like2_s.png"
                      >
                    </span>
                  </div>
                  <div class="content">
                    {{ item.content }}
                  </div>
                </div>
              </div>
              <NoContent v-if="commentList.length==0" />
              <div
                v-if="commentList.length < commentparams.total"
                class="more"
                @click="getmore()"
              >
                加载更多
              </div>
            </el-scrollbar>
          </div>
        </div>
        <div class="text-con">
          <input
            v-model.trim="postparams.content"
            placeholder="请输入评论"
            @keyup.enter="submitdata()"
          >
          <span
            class="submitbtn"
            @click="submitdata()"
          />
        </div>
      </div>
    </div>
  </div>
</template>
<script>
  import {
    getVideoInfo,
    getVideoList,
    getVideolike,
    getVideoview,
    collectVideo,
    agreeComment,
    getCommentList,
    getFileDownloadInfo,
    addComment
  } from '@/api/microVideo'
import { getBaoCloudResourceUrl } from "@/api/file";
  import {
    getPresignedUrl,
  } from "@/api/course";
  import NoContent from '@/components/NoContent'
  export default {
    name: "Index",
    components: {
      NoContent
    },
    data () {
      return {
        id: "",
        //isPlay:true,
        src: "", // url地址
        params: {
          Filter: '',
          KnowledgeCategoryId: '',
          Author: '',
          SkipCount: 0,
          MaxResultCount: 10,
          page: 0,
          total: 0,
          Sorting: '' //ViewCount 热度
        },
        commentparams: {
          Filter: '',
          KnowledgeResourceId: '',
          ParentId: '',
          Sorting: '',
          SkipCount: 0,
          MaxResultCount: 10,
          total: 0,
          page: 0
        },
        postparams: {
          knowledgeResourceId: "",
          content: "",
          author: "",
          agreeCount: 0,
          parentId: ""
        },
        videoList: [],
        commentList: [],
        info: {},
        currentId: '',
        IsshowCommentbox: false,
        IsshowSearchResult: false,
        AllVideoList: [],
      };
    },
    watch: {
      $route: "initdata",
    },
    mounted () {
      this.initdata()
    },
    destroyed () { },
    methods: {
      handleBack () {
        this.$router.push({
          name: 'mircovideo'
        })
      },
      async initdata () {
        this.currentId = this.$route.query.id;
        this.currentIndex = this.$route.query.index
        await this.getInfo()
        // 获取播放视频的地址
        await this.getFileDownloadInfo()
        // 获取全部视频列表，并找到当前视频的索引
        this.getAllVideoList()
        // 观看数目
        let data = { id: this.currentId }
        getVideoview(data)
        this.$nextTick(() => {
          this.player = cyberplayer("myPlayer").setup({
            width: "100%",
            height: "100%",
            backcolor: "#2b2b2b",
            stretching: "uniform",
            file: this.info.fileSrc,
            starttime: 0,
            ak: "5a8cf05266684131a0bed321d0032848",
            controls: true,
            playRate: false, // 默认h5播放器有倍速功能，如不需要，可以设置为false
            // 可配置倍速值数组，格式如下所示
            // playRateConfig: [
            //   { label: "×1" },
            //   { label: "×1.25" },
            //   { label: "×1.5" },
            //   { label: "×2" },
            // ],
            autoStart: true,
            repeat: false,
            volume: 100,
            skin: {
              name: "bce",
              inactive: "#FFF", // 未激活时的颜色
              active: "#f34813", // 悬浮或激活的颜色
            },
            controls: "over",
          });
        })

        this.commentparams.page = 0
        this.commentList = []
        this.getCommentList()
      },
      getNextOne () {
        if (this.currentIndex == this.AllVideoList.length - 1) {
          this.$message.info('已经是最后一个了')
        } else {
          this.currentIndex++
          this.$router.push({ name: 'mircovideoinfo', query: { id: this.AllVideoList[this.currentIndex].id } })
        }

      },
      getPrevOne () {
        if (this.currentIndex == 0) {
          this.$message.info('已经是第一个了')
        } else {
          this.currentIndex--
          this.$router.push({ name: 'mircovideoinfo', query: { id: this.AllVideoList[this.currentIndex].id } })
        }

      },
      getAllVideoList () {
        let data = {
          Filter: '',
          KnowledgeCategoryId: '',
          Author: '',
          SkipCount: 0,
          MaxResultCount: 1000,
          Sorting: ''
        }
        getVideoList(data).then(res => {
          this.AllVideoList = res.items
          this.currentIndex = this.AllVideoList.findIndex(x => x.id == this.currentId)
          // console.log(this.currentIndex)
        })
      },
      view (item) {
        this.$router.push({ name: 'mircovideoinfo', query: { id: item.id } })
      },
      async getInfo () {
        await getVideoInfo(this.currentId).then(res => {
          this.info = res
        })
      },
      async getFileDownloadInfo () {
        // await getFileDownloadInfo(this.info.url).then(res => {
        //   this.info.fileSrc = res.downloadUrl
        // });
       await getPresignedUrl(this.info.url).then(res => {
          this.info.fileSrc = res
        });
        // await getBaoCloudResourceUrl(this.info.url).then(res => {
        //  // this.info.fileSrc = res.replace('https://pbktgf0006.s3-qos.baocloud.cn/', 'https://s3-qos.baocloud.cn/pbktgf0006/')
        //   this.info.fileSrc = res.replace('https://pbktgf0006.s3-qos.baocloud.cn/', 'https://qcfile.ciep-pimp.com/pbktgf0006/')
        // });
      },
      like_change () { // 点赞
        let obj = this.info
        obj.isLike = !obj.isLike
        obj.isLike == true ? obj.likeCount = parseInt(obj.likeCount) + 1 : obj.likeCount = parseInt(obj.likeCount) - 1
        let data = {
          isLike: obj.isLike,
          id: obj.id
        }
        getVideolike(data)
      },
      collect_change () { // 收藏
        let obj = this.info
        obj.isCollection = !obj.isCollection
        obj.isCollection == true ? obj.collectionCount = parseInt(obj.collectionCount) + 1 : obj.collectionCount = parseInt(obj
          .collectionCount) - 1
        let data = {
          knowledgeResourceId: obj.id,
          isCollection: obj.isCollection
        }
        collectVideo(data)
      },
      agreeIt (item) {
        agreeComment({ id: item.id, isAgree: !item.isAgree }).then(res => {
          item.isAgree = !item.isAgree
          item.isAgree == true ? item.agreeCount = parseInt(item.agreeCount) + 1 : item.agreeCount = parseInt(item
            .agreeCount) - 1
        })
        this.$forceUpdate()
      },
      showCommentbox () {
        //console.log(this.popupHeight)
        this.IsshowCommentbox = true
        this.commentparams.page = 0
        this.commentList = []
        this.getCommentList()
      },
      SearchResult () {
        this.IsshowSearchResult = true
        this.getVideoList()
      },
      getCommentList () {
        this.commentparams.KnowledgeResourceId = this.info.id
        this.commentparams.SkipCount = this.commentparams.page * this.commentparams.MaxResultCount
        getCommentList(this.commentparams).then(res => {
          this.commentparams.total = res.totalCount
          this.commentList = this.commentList.concat(res.items)
          // console.log(this.commentList)
          // this.commentList.map(item => {
          //   item.isAgree = false
          // })
        })
      },
      getmore () {
        this.commentparams.page++
        this.getCommentList(false)
      },
      submitdata () {
        this.postparams.knowledgeResourceId = this.info.id
        this.postparams.author = this.info.author
        if (this.postparams.content.length == 0) {
          this.$message.info('请输入评论内容')
        } else {
          addComment(this.postparams).then(res => {
            if (res) {
              this.postparams.content = '' // 清空输入框
              this.$message.success('评论添加成功')
              this.commentparams.page = 0
              this.commentList = []
              this.getCommentList();
              this.info.commentCount++
            }
          }, error => {
            this.$message.error('评论添加失败')
          })
        }

      },
      getVideoList () {
        this.params.SkipCount = this.params.page * this.params.MaxResultCount
        getVideoList(this.params).then(res => {
          this.videoList = res.items
          this.params.total = res.totalCount
        })
      },
    },
  };
</script>
<style>
  .el-scrollbar__wrap {
    overflow-x: hidden !important;
  }

  .sidebar-wrapper .el-scrollbar__wrap {
    overflow-x: hidden;
  }

  .is-horizontal {
    display: none;
  }

  .comment-con-box .NoContent img {
    width: 60px;
  }

  .searchbox .NoContent img {
    width: 40px;
  }
</style>
