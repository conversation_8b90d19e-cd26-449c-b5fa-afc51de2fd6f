<template>
  <div class="home-div">
    <div
      class="carousel-div"
      style="max-width: 1280px; !important"
    >
      <el-carousel
        :interval="5000"
        :style="{ height: 360 }"
        :autoplay="true"
      >
        <el-carousel-item
          v-for="(img, index) in imgs"
          :key="'carousel' + index"
          class="carousel-item"
        >
          <a
            style="cursor: pointer"
            target="_blank"
            @click="handlehrefClick(img)"
          >
            <el-image
              :src="img.url"
              class="carousel-item-imgbox"
            />
          </a>
        </el-carousel-item>
      </el-carousel>
    </div>
    <!-- <div class="hme-message">
        <notice :message="message" />
        </div> -->
    <!-- 精选课程 -->
    <div
      class="RecommandCourse"
      style="max-width: 1280px !important;"
    >
      <h2>
        <span>精选课程</span>
        <!-- <span class="tabs">
                    <span
                        v-for="(item, index) in courseList"
                        :class="{ active: course_index == index }"
                        @click="course_index = index"
                    >{{
                        item.name
                    }}</span>
                </span> -->
        <a href="/#/coursecenter"> 更多 >></a>
      </h2>
      <div
        v-for="(item, index) in courseList"
        v-if="course_index == index"
        class="box"
      >
        <div
          v-for="item1 in item.courses"
          :key="'list' + (index + 1) + item1.courseId"
          class="courseitem"
          @click="prewCourse(item1.courseId)"
        >
          <span class="imgpan"> <img :src="item1.coverUrl"></span>
          <span
            class="title"
            :title="item1.courseName"
          >{{
            item1.courseName
          }}</span>
          <span class="star-p-list">
            <span
              v-for="(i, index) in 5"
              :key="index"
              class="star-a-item"
            >
              <img
                :src="
                  Math.round(item1.courseScore) > index
                    ? '/images/liang.png'
                    : '/images/an.png'
                "
              >
            </span>
            <span
              class="score_txt"
            >{{ Math.round(item1.courseScore) }}分</span>
          </span>
          <div class="p-div">
            <span class="teacher">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                xmlns:xlink="http://www.w3.org/1999/xlink"
                width="16px"
                height="16px"
              >
                <path
                  fill-rule="evenodd"
                  fill="rgb(146, 146, 146)"
                  d="M15.012,16.010 C14.469,16.010 14.031,15.563 14.025,15.010 L13.846,15.010 C12.923,12.152 8.112,10.038 8.009,9.993 C8.006,9.993 8.003,9.994 8.000,9.994 C7.997,9.994 7.993,9.993 7.991,9.993 C7.888,10.038 3.077,12.152 2.154,15.010 L1.975,15.010 C1.969,15.563 1.531,16.010 0.988,16.010 C0.443,16.010 -0.000,15.558 -0.000,15.000 C-0.000,14.640 0.195,14.339 0.472,14.160 C1.475,12.065 3.938,10.293 5.777,9.195 C4.379,8.340 2.985,6.853 2.985,4.997 C2.985,2.025 5.008,-0.000 8.000,-0.000 C10.992,-0.000 13.015,2.025 13.015,4.997 C13.015,6.853 11.621,8.340 10.223,9.195 C12.062,10.293 14.525,12.065 15.528,14.160 C15.805,14.339 16.000,14.640 16.000,15.000 C16.000,15.558 15.557,16.010 15.012,16.010 ZM10.998,5.009 C10.998,3.168 9.845,2.009 8.000,2.009 C6.155,2.009 5.002,3.168 5.002,5.009 C5.002,6.850 7.012,8.037 8.000,8.009 C8.988,7.982 10.998,6.850 10.998,5.009 Z"
                />
              </svg>
              <span>
                {{ item1.lecturer }}
              </span>
            </span>
            <span class="joincount">
              <img src="/images/group.png">
              <span class="txt">
                {{
                  item1.courseJoinCount == null
                    ? 0
                    : item1.courseJoinCount | showNum
                }}</span>
            </span>
            <span class="classhour">{{ item1.classHour }}课时</span>
          </div>
        </div>
      </div>
    </div>
    <!-- 知识锦囊 -->
    <!-- <div class="RecommandCourse">
            <h2>
                <span>知识锦囊</span>
                <a href="/#/mircovideo"> 更多 >></a>
            </h2>
            <div
                class="CourseList"
                style="min-height: auto;"
            >
                <div
                    v-for="item in knowledgeList"
                    :key="item.id"
                    class="courseitem video-item"
                    @click="view(item)"
                    >
                    <span class="imgpan">
                        <img :src="item.thumbnailUrl">
                        <span class="bg-op06" />
                    </span>
                    <div class="infopan">
                        <div class="p-div1">
                            <span class="teacher-name">
                                <img
                                v-if="item.profilePhoto == null"
                                src="/images/user.png"
                                class="userImg"
                                >
                                <img
                                v-else
                                :src="item.profilePhoto"
                                class="userImg"
                                >
                                <span>{{ item.author }}</span>
                            </span>
                            <span class="time">{{ item.duration | timeFromte2 }}</span>
                        </div>
                        <div class="p-div">
                            <span
                                class="title"
                                :title="item.name"
                            >{{ item.name }}</span>
                            <span class="count">
                                <img src="/microVideo/icon_like_pc_n.png">
                                <span>{{ item.likeCount | showNum }}</span>
                            </span>
                            <span class="count">
                                <img src="/microVideo/icon_person_pc.png">
                                <span>{{ item.viewCount | showNum }}</span>
                            </span>
                            <span class="count">
                                <img src="/microVideo/icon_comment_pc.png">
                                <span>{{ item.commentCount | showNum }} </span>
                            </span>
                        </div>
                    </div>
                    <img
                        class="icon_play"
                        src="/microVideo/icon_play2_n.png"
                    >
                </div>
            </div>
        </div> -->
    <!-- 培训班级 -->
    <div
      class="RecommandCourse"
      style="max-width: 1280px !important;"
    >
      <h2>
        <span>培训班级</span>
        <a href="/#/classTrain"> 更多 >></a>
      </h2>
      <div class="card-box">
        <div
          v-for="(item, index) in trains"
          :key="index"
          class="card-item"
          @click="prewTraining(item.id)"
        >
          <img
            class="card-img"
            :src="item.imgUrl"
          >
          <div class="card-class-box">
            <p class="card-class-title">
              {{ item.name }}
            </p>
            <div class="card-class-info">
              <div class="card-class-info-item">
                <img src="../../assets/image/zan/class-num.png">
                <span>{{ item.userCount }}</span>
              </div>
              <!-- <div class="card-class-info-item">
                                <img src="../../assets/image/zan/class-type.png" />
                                <span>骨干教师</span>
                            </div> -->
              <div class="card-class-info-item">
                <img src="../../assets/image/zan/class-time.png">
                <span>
                  {{
                    item.startDate | DateFromte("YYYY-MM-DD")
                  }}
                  至 {{ item.endDate | DateFromte("YYYY-MM-DD") }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 名师风采 -->
    <div
      class="RecommandCourse"
      style="max-width: 1280px !important;"
    >
      <h2>
        <span>名师风采</span>
        <a href="/#/teacherTrain"> 更多 >></a>
      </h2>
      <div class="card-box">
        <div
          v-for="(item, index) in teacherList"
          :key="index"
          class="card-item"
          @click="goDetail(item.id)"
        >
          <img
            class="card-img"
            :src="item.profilePhoto"
          >
          <div class="card-teacher-box">
            <p>{{ item.introduce }}</p>
            <div class="teacher-detail">
              详情
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 精彩直播 -->
    <div
      class="Recommand-live-box"
      style="max-width: 1280px !important;"
    >
      <h2>
        <span>精彩直播</span>
        <a href="/#/livecenter"> 更多 >></a>
      </h2>
      <div class="live-carousel">
        <el-carousel
          :interval="5000"
          :autoplay="false"
        >
          <el-carousel-item
            v-for="i in liveList.length % 4 === 0
              ? liveList.length / 4
              : Math.floor(liveList.length / 4) + 1"
            :key="'carousel' + i"
            class="carousel-item"
          >
            <div
              v-for="item in liveList.slice((i - 1) * 4, i * 4)"
              :key="item.courseId"
              class="courseItem"
              style="width: 25%; display: inline-block"
            >
              <el-divider>
                <div
                  style="
                                        height: 100%;
                                        display: flex;
                                        align-items: center;
                                    "
                >
                  <img src="/images/icon_clock.png">
                  <span style="margin-left: 5px">
                    {{
                      item.startTime
                        | DateFromte("MM-DD HH:mm")
                    }}
                  </span>
                </div>
              </el-divider>
              <div
                class="courseitem home-live-item liveItem"
                @click="prewLive(item)"
              >
                <span class="imgpan">
                  <img :src="item.coverImage">
                  <div
                    v-if="item.liveStreamStatue === 0"
                    class="live_state_default live_state_box_s"
                  >
                    <img
                      src="/images/icon_play.png"
                      mode="widthFix"
                    >
                    <span class="live_span">未开始</span>
                  </div>
                  <div
                    v-if="item.liveStreamStatue === 1"
                    class="live_state_default live_state_box_p"
                  >
                    <img
                      src="data:image/gif;base64,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"
                      alt=""
                    >
                    <span class="live_span">直播中</span>
                  </div>
                  <div
                    v-if="
                      item.liveStreamStatue === 2 &&
                        item.allowPlayBack
                    "
                    class="live_state_default live_state_box_e live_play_back"
                  >
                    <img
                      src="/images/icon_play.png"
                      mode="widthFix"
                    >
                    <span class="live_span">回放</span>
                  </div>
                  <div
                    v-if="
                      item.liveStreamStatue === 2 &&
                        !item.allowPlayBack
                    "
                    class="live_state_default live_state_box_e"
                  >
                    <img
                      src="/images/icon_noplay.png"
                      mode="widthFix"
                    >
                    <span class="live_span">回放已禁</span>
                  </div>
                </span>
                <span
                  class="title"
                  :title="item.title"
                >{{
                  item.title
                }}</span>
                <div class="p-div">
                  <span class="lecturer">
                    主讲人：{{
                      item.lecturer | Substr(25)
                    }}</span>
                  <span class="usercount">
                    <img src="/images/icon-usercount.png">
                    <span>{{
                      item.userCount | showNum
                    }}人</span>
                  </span>
                </div>
                <span class="time">
                  <img src="/images/icon-timelong.png">
                  <span>
                    {{
                      item.startTime
                        | DateFromte("YYYY.MM.DD HH:mm")
                    }}
                    -
                    {{ item.endTime | DateFromte("HH:mm") }}
                  </span>
                </span>
              </div>
            </div>
          </el-carousel-item>
        </el-carousel>
      </div>
    </div>
    <el-dialog
      title="提示"
      :visible.sync="liveDialog"
      width="30%"
    >
      <div>
        即将打开车博苑直播端，请稍后 <br><br>
        如果您未安装直播客户端，请<a
          :href="downClient"
        >下载车博苑直播端</a>
      </div>
      <span
        slot="footer"
        class="dialog-footer"
      >
        <el-button
          type="primary"
          @click="liveDialog = false"
        >确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { getHomeImgs, getRecommendCourses, getNotices } from "@/api/course";
import { getVideoList } from "@/api/microVideo";
import { getlives, getlivesdetail } from "@/api/live";
import notice from "@/components/notice";
import moment from "moment";

import { myLiveUrl, myLiveBackUrl } from "@/api/user";
import {getTeachers} from "@/api/newadd"
import { getMyTrains } from "@/api/course";
export default {
    name: "Index",
    components: {
        notice,

    },
    data() {
        return {
            vdaH: 600, // 高度
            imgs: [],
            courseList: [],
            knowledgeList: [],
            course_index: 0,
            liveDialog: false,
            liveList: [],
            downClient: "",
            message: "",

            teacherList: [], // 名师风采
            teacherParmas:{
                Filter: "",
                Sorting: "",
                SkipCount: 0,
                MaxResultCount: 3,
            },
            train_query: {
                TrainState: null, // 0 未开始,1 进行中,2 已结束
                SkipCount: 0,
                MaxResultCount: 3
            },
            trains: [],
        };
    },
    mounted() {
        getMyTrains(this.train_query).then((res) => {
            this.trains = res.items;
        });
        getTeachers(this.teacherParmas).then((res) => {
            this.teacherList = res.items;
        });
        getHomeImgs().then((res) => {
            this.imgs = res.items;
            if (res.items.length > 0) {
                res.items.forEach((element) => {
                    if (element.introduce != "" && element.introduce != null)
                        this.message += element.introduce;
                });
            }
        });
        getRecommendCourses().then((res) => {
            this.courseList = res.items;
        });
        getVideoList({
            SkipCount: 0,
            MaxResultCount: 4,
        }).then((res) => {
            this.knowledgeList = res.items;
        });
        getlives({
            SkipCount: 0,
            MaxResultCount: 12,
        }).then((res) => {
            this.liveList = res.items;
        });
    },
    methods: {
        goDetail(id){
            const url= this.$router.resolve({
                name: "teacherTrainDetail",
                query: { id: id },
            }).href;
            window.open(url, "_blank");
        },
        prewTraining(id) {
            const url = this.$router.resolve({
                name: "TrainingInfo",
                query: {
                    id: id },
            }).href;

            window.open(url, "_blank");
        },
        formatText(text) {
            if (!text) return '';
            return text.length > 66 ? text.substring(0, 66) + '...' : text;
        },

        prewCourse(id) {
            this.$router.push({ name: "CourseInfo", query: { id: id } });
        },
        view(item) {
            this.$router.push({
                name: "mircovideoinfo",
                query: { id: item.id },
            });
        },
        async handlehrefClick(info) {
            if (info.href.match(RegExp(/bglives/))) {
                var arr = info.href.split("?zbid=");
                console.log(arr[1]);
                const item = await getlivesdetail(arr[1]);
                if (moment(item.endTime).diff(moment(), "s") > 0) {
                    //进行中
                    const res = await myLiveUrl(item.id);
                    window.open(res.webUrl, "_blank");
                } else if (!item.allowPlayBack) {
                    //回放禁止
                    this.$message.info("该直播已禁止回放,无法观看");
                } else if (!item.hasPlayBack) {
                    //暂无回放
                    this.$message.info("该直播暂时未生成回放,无法观看");
                } else {
                    // this.viewBackVideo(item.id);
                    const res = await myLiveBackUrl(item.id);
                    window.open(res, "_blank");
                }
            } else {
                window.open(info.href, "_blank");
            }
        },
        prewLive(item) {
            // console.log(item)
            // if (!this.userLogin) {
            //   this.$store.dispatch("user/toggleloginbox", true);
            // } // 未登录
            // else if(item.liveStreamStatue==0){ //未开始
            // 	this.$message.info("直播还未开始，请耐心等待")
            // }
            //else
            if (item.liveStreamStatue == 1 || item.liveStreamStatue == 0) {
                //进行中
                this.intoLive(item.id);
            } else if (!item.allowPlayBack) {
                //回放禁止
                this.$message.info("该直播已禁止回放,无法观看");
            } else if (!item.hasPlayBack) {
                //暂无回放
                this.$message.info("该直播暂时未生成回放,无法观看");
            } else {
                // console.log(item.id);
                this.viewBackVideo(item.id);
            }
        },
        async viewBackVideo(id) {
            var res = await myLiveBackUrl(id);
            this.$router.push({
                name: "LiveCenter",
            });
            // window.open(res, "_blank");
        },
        async intoLive(id) {
            var res = await myLiveUrl(id);
            this.$router.push({
                name: "LiveCenter",
            });
            // res.clientUrl=res.clientUrl.replace('baijiacloud','huizhixueyuan');
            // this.getHref(res.clientUrl);
        },
        getbackurl(id) {
            return new Promise((resolve, reject) => {
                myLiveBackUrl({ id: id }).then(
                    (res) => {
                        resolve(res);
                    },
                    (error) => {}
                );
            });
        },

        getHref(url) {
            var isFirefox = navigator.userAgent.indexOf("Firefox") > -1; // 是否是火狐  ，火狐内核Gecko
            var isWebKit = navigator.userAgent.indexOf("WebKit") > -1; // 是否是WebKit 内核
            var isChrome = navigator.userAgent.indexOf("Chrome") > -1; // 是否是谷歌
            var isTrident = navigator.userAgent.indexOf("Trident") > -1; // 是否是IE内核
            var isIeL = !!window.ActiveXObject || "ActiveXObject" in window;
            if (isFirefox || isWebKit || isChrome || isTrident || isIeL) {
                // IE和火狐用window.open打开
                // 调起客户端 5秒之后自动关闭调起窗口
                var client = window.open(url);
                setTimeout(function () {
                    if (client) {
                        client.close(); //关闭新打开的浏览器窗口，避免留下一个空白窗口
                    }
                }, 5000);
            } else {
                //其它浏览器使用模拟<a>标签`click`事件的形式调起
                var a = document.createElement("a");
                a.setAttribute("href", url);
                document.body.appendChild(a);
                a.click();
            }
            setTimeout(() => {
                // 5秒之后不管有没有调起都弹窗提示下载客户端
                this.liveDialog = true;
                setTimeout(() => {
                    // 5秒之后关闭
                    this.liveDialog = false;
                }, 8000);
            }, 5000);
        },
        OSnow() {
            var agent = navigator.userAgent.toLowerCase();
            var isMac = /macintosh|mac os x/i.test(navigator.userAgent);
            if (agent.indexOf("win32") >= 0 || agent.indexOf("wow32") >= 0) {
                this.isMac = false;
                this.downClient =
                    // "https://www.baijiayun.com/default/home/<USER>";
                    "https://baogang.bj.bcebos.com/installer/Huizhixueyuaninstaller.exe";
            }
            if (agent.indexOf("win64") >= 0 || agent.indexOf("wow64") >= 0) {
                this.isMac = false;
                this.downClient =
                    //"https://www.baijiayun.com/default/home/<USER>";
                    "https://baogang.bj.bcebos.com/installer/Huizhixueyuaninstaller.exe";
            }
            // if (isMac) {
            //   this.isMac = true;
            //   this.downClient =
            //     "https://www.baijiayun.com/default/home/<USER>";
            // }
        },
    },
};
</script>
<style scoped>
.home-div {
    /* -webkit-filter: grayscale(100%);
  filter: grayscale(100%); */
}
.home-live-item .live_state_default {
    position: absolute;
    top: 15px;
    left: 0;
    height: 28px;
    border-radius: 0 16px 16px 0;
    background-color: #0096ff;
    display: flex;
    align-items: center;
    padding: 0 10px;
}

.home-live-item .live_state_box_s {
    background-color: rgb(194, 198, 214);
}

.home-live-item .live_state_box_p {
    background-color: rgb(255, 51, 51);
}

.home-live-item .live_state_box_e {
    background: linear-gradient(to right, #005dc2, #024b9a);
}

.home-live-item .live_span {
    margin-left: 5px;
    color: #fff;
    font-size: 10px;
}

.home-live-item .live_state_default img {
    width: 12px;
    height: 12px;
}

/* .courseItem ::v-deep .el-divider {
  background-color: rgb(229, 229, 229);
} */
.courseItem ::v-deep .el-divider__text {
    border-radius: 19px;
    background-color: rgb(229, 229, 229);
    height: 38px;
}

.liveItem {
    margin: 0 auto;
    display: block;
    height: 325px;
    width: 270px;
    margin-top: 40px;
}

/* .liveItem .lecturer {
  border-right: 1px solid #e7e7eb;
  padding-right: 30px;
  display: inline-block;
}
.liveItem .usercount {
  display: inline-block;
  margin-left: 30px;
  margin-top: 0;

} */
/* .live-carousel {
  padding-bottom: 40px;
} */
.live-carousel ::v-deep .el-carousel {
    height: 400px;
}

.live-carousel ::v-deep .el-carousel__container {
    height: 400px;
}

.live-carousel ::v-deep .el-carousel__indicators--horizontal {
    bottom: 0px;
}

.live-carousel ::v-deep .el-carousel__button {
    height: 6px;
    background: transparent;
    border-radius: 3px;
    border: 2px solid #005dc2;
    width: 25px;
}

.live-carousel ::v-deep .is-active .el-carousel__button {
    background: #005dc2;
    width: 50px;
}
</style>
