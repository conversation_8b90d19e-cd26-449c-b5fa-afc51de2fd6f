<template>
  <div class="Directory-div">
    <div v-for="item in directorylist" class="item-dir">
      <div class="title">
        {{ item.courseDirectoryName }}
      </div>
      <div v-for="r in item.resources" class="reslist">
        <a v-if="!unShowFinished || (unShowFinished&&!r.isFinished)"
          :class="[resourceId==r.id?'active':'','resitem',freeModel==1&&r.isTrial==0?'grey':'']" :disable="isExpire"
          @click="clickRes(r)">
          <span class="state">
            <!-- 已完成-->
            <svg v-if="findRes(r)==2" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
              width="10px" height="10px">
              <path fill-rule="evenodd" fill="rgb(237, 87, 14)"
                d="M5.000,-0.000 C7.761,-0.000 10.000,2.239 10.000,5.000 C10.000,7.761 7.761,10.000 5.000,10.000 C2.239,10.000 -0.000,7.761 -0.000,5.000 C-0.000,2.239 2.239,-0.000 5.000,-0.000 Z" />
            </svg>
            <!-- 未完成-->
            <span v-if="findRes(r)==1" class="uncomplated">
              <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="5px"
                height="10px">
                <path fill-rule="evenodd" fill="rgb(237, 87, 14)"
                  d="M5.000,-0.000 C5.000,4.272 5.000,5.562 5.000,10.000 C2.239,10.000 -0.000,7.761 -0.000,5.000 C-0.000,2.239 2.239,-0.000 5.000,-0.000 Z" />
              </svg>
            </span>
            <!-- 未开始-->
            <span v-if="findRes(r)==0" class="uncomplated" />
          </span>
          <!--video-->
          <svg v-if="r.fileType=='.mp4'" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
            width="20px" height="12px">
            <path fill-rule="evenodd" fill="rgb(102, 102, 102)"
              d="M19.998,11.065 C19.998,11.258 19.890,11.433 19.718,11.525 C19.546,11.615 19.336,11.606 19.172,11.501 L14.408,8.372 L14.408,10.213 C14.406,11.199 13.590,11.998 12.583,12.000 L1.823,12.000 C1.339,12.000 0.875,11.812 0.532,11.477 C0.479,11.422 0.434,11.361 0.387,11.302 C0.143,10.991 -0.000,10.612 -0.000,10.213 L-0.000,9.477 L-0.000,9.475 L-0.000,5.245 L-0.000,1.787 C0.002,0.799 0.819,-0.000 1.825,-0.000 L12.585,-0.000 C13.066,-0.000 13.500,0.184 13.827,0.482 C14.183,0.808 14.408,1.272 14.408,1.787 L14.408,2.626 L14.408,2.628 L14.408,3.988 L19.177,0.904 C19.342,0.801 19.550,0.793 19.723,0.885 C19.893,0.975 20.000,1.152 20.000,1.342 L19.998,11.065 ZM3.889,1.980 C3.693,1.785 3.424,1.677 3.145,1.680 C2.720,1.680 2.337,1.931 2.174,2.315 C2.013,2.698 2.102,3.140 2.402,3.434 C2.702,3.729 3.154,3.817 3.545,3.657 C3.937,3.497 4.194,3.123 4.196,2.709 C4.196,2.435 4.086,2.172 3.889,1.980 Z" />
          </svg>
          <!--pdf-->
          <span v-else-if="r.fileType=='.pdf' || r.resType =='pdfh5'" class="pdf-icon">
            <img src="/images/pdf-icon.png">
          </span>
          <span v-else-if="r.fileType=='.png'" class="pdf-icon">
            <img src="/images/img-icon.png">
          </span>
          <!--swf-->
          <span v-else-if="r.resType=='geektime-html'" class="pdf-icon">
            <img width="8px" src="/images/html-icon.png">
          </span>
          <span
            v-else-if="r.fileType=='.zip'||r.resType=='hundun-video'||r.resType=='geektime-video' || r.resType=='ximalaya' || r.resType=='pdfh5'|| r.resType=='videoh5'">
            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="20px"
              height="12px">
              <path fill-rule="evenodd" fill="rgb(102, 102, 102)"
                d="M19.998,11.065 C19.998,11.258 19.890,11.433 19.718,11.525 C19.546,11.615 19.336,11.606 19.172,11.501 L14.408,8.372 L14.408,10.213 C14.406,11.199 13.590,11.998 12.583,12.000 L1.823,12.000 C1.339,12.000 0.875,11.812 0.532,11.477 C0.479,11.422 0.434,11.361 0.387,11.302 C0.143,10.991 -0.000,10.612 -0.000,10.213 L-0.000,9.477 L-0.000,9.475 L-0.000,5.245 L-0.000,1.787 C0.002,0.799 0.819,-0.000 1.825,-0.000 L12.585,-0.000 C13.066,-0.000 13.500,0.184 13.827,0.482 C14.183,0.808 14.408,1.272 14.408,1.787 L14.408,2.626 L14.408,2.628 L14.408,3.988 L19.177,0.904 C19.342,0.801 19.550,0.793 19.723,0.885 C19.893,0.975 20.000,1.152 20.000,1.342 L19.998,11.065 ZM3.889,1.980 C3.693,1.785 3.424,1.677 3.145,1.680 C2.720,1.680 2.337,1.931 2.174,2.315 C2.013,2.698 2.102,3.140 2.402,3.434 C2.702,3.729 3.154,3.817 3.545,3.657 C3.937,3.497 4.194,3.123 4.196,2.709 C4.196,2.435 4.086,2.172 3.889,1.980 Z" />
              <!-- <path
                fill-rule="evenodd"
                fill="rgb(255, 255, 255)"
                d="M8.016,2.548 C8.332,2.436 8.666,2.379 9.000,2.379 L9.000,0.003 C7.488,-0.053 6.047,0.677 5.133,1.949 C4.746,2.473 4.412,3.053 4.166,3.670 L3.445,5.560 C3.269,6.122 3.041,6.720 2.795,7.263 C2.619,7.712 2.390,8.123 2.109,8.535 C1.881,8.872 1.582,9.134 1.230,9.302 C0.844,9.489 0.422,9.602 -0.000,9.602 L-0.000,11.997 C1.512,12.053 2.935,11.323 3.867,10.051 C4.166,9.620 4.430,9.171 4.658,8.685 L5.000,7.000 L8.000,7.000 L8.000,5.000 L6.000,5.000 C6.123,4.645 6.486,4.120 6.680,3.783 C6.838,3.483 7.031,3.259 7.260,3.034 C7.488,2.810 7.734,2.641 8.016,2.548 L8.016,2.548 Z"
              /> -->
            </svg>
          </span>
          <span class="resname">{{ r.name }}</span>
          <span class="fr">
            <a v-if="r.isTrial&&freeModel==1" class="shikan_btn">试看</a>
            <!-- <span class="time" v-if="findResDuration(r)!=null&&IsShowRecord"> 已学习 {{findResDuration(r) | timeFromte}}  {{r.duration!=0?"|":''}}  </span> -->
            <span v-if="findResLearnProgress(r)!=null&&IsShowRecord" class="time">
              已学习 {{ findResLearnProgress(r) }}%
              <span> {{ r.duration!=0?"|":'' }} </span> </span>
            <span
              v-if="r.duration > 0 && (r.fileType=='.mp4' || r.resType=='html'|| r.fileType=='.pdf' || r.resType =='pdfh5' || r.resType=='hundun-video'|| r.resType=='geektime-video'|| r.resType=='geektime-html'|| r.resType=='ximalaya'|| r.resType=='pdfh5'|| r.resType=='videoh5') "
              class="time">{{ r.duration | timeFromte }}</span>
          </span>
        </a>
      </div>
      <Directory :directorylist="item.directories" :record-list="recordList" :course-id="courseId"
        :resource-id="resourceId" :train-id="trainId" :free-model="freeModel" :is-show-record="IsShowRecord"
        :is-expire="isExpire" :un-show-finished="unShowFinished" :cloud-learn="cloudLearn" />
    </div>
  </div>
</template>
<script>
  import { updateCourseRecord } from '@/api/course';
  export default {
    name: 'Directory',
    props: {
      directorylist: Array,
      recordList: {},
      courseId: '',
      resourceId: '',
      trainId: '',
      freeModel: 0,//免费 = 0,收费 = 1,
      IsShowRecord: true, // 是否显示已学记录
      isExpire: false,
      cloudLearn: true,  // 是否能够学习
      unShowFinished: false, //是否只显示未完成
    },
    methods: {
      findRes (item) {
        if (this.recordList != null && this.recordList.resUserRecords != undefined) {
          var res = this.recordList.resUserRecords.find(x => x.courseResourceId == item.id)
          if (res == undefined) return 0  // 未开始
          else if (res.isComplete) return 2 //已完成
          else return 1 // 未完成
        } else {
          return 0  // 未开始
        }
      },
      findResDuration (item) { //资源已学时长
        if (this.recordList != null && this.recordList.resUserRecords != undefined) {
          var res = this.recordList.resUserRecords.find(x => x.courseResourceId == item.id)
          if (res == undefined) return null  // 未开始
          else return res.resLearnDuration //已完成 || 未完成
        } else {
          return null // 未开始
        }
      },
      findResLearnProgress (item) {
        if (this.recordList != null && this.recordList.resUserRecords != undefined) {
          var res = this.recordList.resUserRecords.find(x => x.courseResourceId == item.id)
          if (res == undefined) return null  // 未开始
          else return res.lastLearnProgress.toFixed(2) //已完成 || 未完成
        } else {
          return null // 未开始
        }

      },
      clickRes (item) { //点击资源

        if (this.isExpire && !item.isTrial) {
          this.$message.info("当前课程学习已过期");
          return
        }

        if (this.$store.getters.token != null) {
          if (this.cloudLearn) {
            let data = {
              trainId: this.trainId, //培训ID
              courseId: this.courseId,
              lastLearnResourceId: item.id
            }
            if (this.freeModel == 0) {
              updateCourseRecord(data) // 收费 不更新
            }
            // 免费课程或者试看资源，方可打开
            if (this.freeModel == 0 || item.isTrial) {
              this.$router.push({ name: 'ResourceInfo', query: { id: item.id, courseId: this.courseId, trainId: this.trainId, trialUrl: item.trialUrl } })
            }
          } else {
            this.$message.info("您没有权限学习此课程");
          }
        } else {
          this.$store.dispatch("user/toggleloginbox", true);
        }

      }
    }
  }
</script>
