<template>
  <div class="profile_info_con">

    <SideMenu></SideMenu>
    <!-- <div class="profile_info_box" >
      <div class="NoContent">
        <img src="/images/img-nocontent.png" />
        <span>即将发布，敬请期待</span>
      </div>
    </div> -->
    <div class="profile_info_box" >
      <div class="tabs">
        <a @click="changeState()" :class="{active:course_query.CourseLearnState==undefined}">全部</a>
        <a @click="changeState(0)" :class="{active:course_query.CourseLearnState==0}">未开始</a>
        <a @click="changeState(1)" :class="{active:course_query.CourseLearnState==1}">学习中</a>
        <a @click="changeState(2)" :class="{active:course_query.CourseLearnState==2}">已完成</a>
      </div>
      <div class="CourseList myCourseList">
        <div v-for="item in courseList" class="courseitem" :key="'course' + item.id" @click="prewCourse(item.id)">
          <span class="imgpan"> <img :src="item.coverUrl" /></span>
          <span class="title"  :title="item.name">{{item.name}}</span>
          <span class="star-p-list">
            <span v-for="(i,index) in 5" :key="index" class="star-a-item">
              <img :src="Math.round(item.courseScore) >index?'/images/liang.png':'/images/an.png'" />
            </span>
            <span class="score_txt">{{Math.round(item.courseScore)}}分</span>
          </span>
          <div class="p-div">
            <span class="teacher">
              <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="16px" height="16px">
                <path fill-rule="evenodd" fill="rgb(146, 146, 146)"
                  d="M15.012,16.010 C14.469,16.010 14.031,15.563 14.025,15.010 L13.846,15.010 C12.923,12.152 8.112,10.038 8.009,9.993 C8.006,9.993 8.003,9.994 8.000,9.994 C7.997,9.994 7.993,9.993 7.991,9.993 C7.888,10.038 3.077,12.152 2.154,15.010 L1.975,15.010 C1.969,15.563 1.531,16.010 0.988,16.010 C0.443,16.010 -0.000,15.558 -0.000,15.000 C-0.000,14.640 0.195,14.339 0.472,14.160 C1.475,12.065 3.938,10.293 5.777,9.195 C4.379,8.340 2.985,6.853 2.985,4.997 C2.985,2.025 5.008,-0.000 8.000,-0.000 C10.992,-0.000 13.015,2.025 13.015,4.997 C13.015,6.853 11.621,8.340 10.223,9.195 C12.062,10.293 14.525,12.065 15.528,14.160 C15.805,14.339 16.000,14.640 16.000,15.000 C16.000,15.558 15.557,16.010 15.012,16.010 ZM10.998,5.009 C10.998,3.168 9.845,2.009 8.000,2.009 C6.155,2.009 5.002,3.168 5.002,5.009 C5.002,6.850 7.012,8.037 8.000,8.009 C8.988,7.982 10.998,6.850 10.998,5.009 Z" />
              </svg>
              <span>
                {{item.lecturer}}
              </span>
            </span>
            <span class="joincount">
              <img src="/images/group.png" />
              <span> {{item.courseJoinCount==null?0:item.courseJoinCount | showNum}}</span>
            </span>
            <span class="classhour">{{item.classHour}}课时</span>
          </div>
        </div>
        <NoContent v-if="courseList.length==0"></NoContent>
      </div>
      <el-pagination
        v-if="course_query.total>course_query.MaxResultCount"
        class="my_pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="course_query.page"
        :page-sizes="[8, 20, 40]"
        :page-size="course_query.MaxResultCount"
        layout="total, sizes, prev, pager, next, jumper"
        :total="course_query.total"
      ></el-pagination>
    </div>
  </div>
</template>
<script>
import {getMyCourse} from '@/api/course';
import SideMenu from "@/layout/SideMenu2.vue";
import NoContent from '@/components/NoContent'
export default {
  name: 'myCourse',
  data() {
   return {
    course_query:{
      CourseLearnState:null, // 0 未开始,1 进行中,2 已结束
      SkipCount:0,
      MaxResultCount:8,
      page:1,
      total:0
     },
     courseList:[]
   }
  },
  components: {
      SideMenu,
      NoContent
  },
  mounted(){
    if (this.$store.getters.token) {
      this.getList()
    } else{
      this.$store.dispatch("user/toggleloginbox", true);
    }
  },
  methods: {
    prewCourse(id){
      this.$router.push({name: 'CourseInfo', query:{id: id}})
    },
    changeState(state){
      this.course_query.page = 1
      this.courseList = []
      this.course_query.CourseLearnState = state
      this.getList()
    },
    handleSizeChange(val) {
      this.course_query.MaxResultCount = val
      this.getList()
    },
    handleCurrentChange(val) {
      this.course_query.page = val
      this.getList()
    },
    getList(){
      let data = {
        CourseLearnState:this.course_query.CourseLearnState, // 0 未开始,1 进行中,2 已结束
        SkipCount:(this.course_query.page-1)*this.course_query.MaxResultCount,
        MaxResultCount:this.course_query.MaxResultCount,
      }
      getMyCourse(data).then(res=>{
        this.courseList = res.items
        this.course_query.total = res.totalCount
      })
    },
    getMore(){
      this.course_query.page++
      //console.log(this.train_query)
      this.getList()
    }
  }
}
</script>

