<template>
  <div class="biaozhun">
    <div class="maps">
      <img src="../../assets/image/zan/map.png">
      <span>技术中心</span>
      <img src="../../assets/image/zan/left.png">
      <span>技术文章</span>
    </div>
    <div class="qiye-box">
      <div
        v-for="item in list"
        :key="item.id"
        class="qiye-item"
        @click="go(item.id)"
      >
        <img
          :src="item.imgUrl"
          class="imgs"
        >
        <p class="title">
          {{ item.title }}
        </p>
      </div>
    </div>
    <el-pagination
      v-if="parmas.totalCount > parmas.maxResultCount"
      class="my_pagination"
      :current-page="parmas.page"
      :page-sizes="[10, 20, 60]"
      :page-size="parmas.MaxResultCount"
      layout="total, sizes, prev, pager, next, jumper"
      :total="parmas.total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>
<script>
import {getTechnologys} from '@/api/technology'
export default {
    data() {
        return {
            parmas: {
                    SkipCount: 0,
                    MaxResultCount: 100,
                    Sorting: 'sort',
                    page:1,
                    total:0,
                    state:1
                },
            list: [],
        };
    },
    mounted(){
            this.getList();
        },
    methods: {
        handleSizeChange(val) {
                this.parmas.MaxResultCount = val;
                this.getList();
        },
         handleCurrentChange(val) {
             this.parmas.page = val;
             this.getList();
         },
        //  handleClick(url) {
        //      this.iframeUrl = url;
        //      this.Dialog = true;
        //  },
         getList(){
             this.parmas.SkipCount=this.parmas.MaxResultCount*(this.parmas.page-1)
             getTechnologys(this.parmas).then(res => {
                 this.list = res.items;
                 this.parmas.total = res.totalCount;
             });
         },
        go(_id) {
            // if (id === 1) {
                const url = this.$router.resolve({
                    name: "jishuDetail",
                    query: {
                    id: _id,
                    mapname:'0' },
                }).href;

                window.open(url, "_blank");
            // }
        },
    },

};
</script>
<style scoped>
.qiye-box {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    margin-top: 20px;
}

.qiye-item {
    box-shadow: 0 5px 10px 0 rgba(0, 0, 0, 0.1);
    border-radius: 20px;
    width: 260px;
    /* height: 150px; */
    -webkit-box-sizing: border-box;
    background: #fff;
    margin-right: 60px;
    margin-bottom: 50px;
    cursor: pointer;
}

.imgs {
    width: 100%;
    height: 220px;
    display: block;
}

.title {
    color: #a9aaab;
    padding: 0 20px;
    box-sizing: border-box;
    border-radius: 3px;
    margin-right: 10px;
    font-size: 14px;
    line-height: 20px;
}
</style>
