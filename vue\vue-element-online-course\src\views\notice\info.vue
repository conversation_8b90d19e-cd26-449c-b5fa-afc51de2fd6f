<template>
  <div class="notice-cent">
    <div class="breadcrumb-box">
      <el-breadcrumb separator-class="el-icon-arrow-right" class="bread_con">
        <el-breadcrumb-item :to="{ path: '/' }">
          <i class="el-icon-location-outline" />
        </el-breadcrumb-item>
        <el-breadcrumb-item :to="{ path: 'NoticeCenter' }">
          通知公告
        </el-breadcrumb-item>
        <el-breadcrumb-item>{{NoticeInfo.title}}</el-breadcrumb-item>
      </el-breadcrumb>
    </div>
    <div class="notice-box ">
        <div class="title">通知公告 </div>
        <div class="notice-info-box">
          <div class="notice-title">{{NoticeInfo.title}}</div>
          <div class="notice-time">{{NoticeInfo.publishDate | DateFromte("YYYY-MM-DD HH:mm:ss")  }}</div>
          <div class="notice-content" v-html="NoticeInfo.content"></div>
        </div>
    </div>
</div>
</template>
<script>
import {getNoticeInfo} from '@/api/course';
import NoContent from '@/components/NoContent'
export default {
  name: 'index',
  components:{
    NoContent
  },
  data() {
   return {
    NoticeInfo:{},
   }
  },
  watch: {
      $route: {
        handler: function (route) {
          this.getInfo()
        },
        immediate: true
      }

   },
  created(){

  },
  methods: {
    getInfo(){
      getNoticeInfo({id:this.$route.query.id}).then(res=>{
        this.NoticeInfo = res
      })
    },
  }
}
</script>

