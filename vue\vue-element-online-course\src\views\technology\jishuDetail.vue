<template>
  <div>
    <!-- <img
      style="width: 1920px;height: 100%;display: block;margin: 50px auto 0 auto;"
      src="../../assets/image/static/jishudetail.png"
    > -->
    <div v-html="detail.content" />
  </div>
</template>
<script>
import { getTechnologyDetail } from "@/api/technology";
export default {
  data() {
    return {
      detail: {},
    };
  },
  mounted() {},
  created() {
    const id = this.$route.query.id;
    getTechnologyDetail(id).then((res) => {
      this.detail = res;
    });
  },
  methods: {},
};
</script>
