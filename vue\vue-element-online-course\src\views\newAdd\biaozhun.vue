<template>
  <div class="biaozhun">
    <div class="maps">
      <img src="../../assets/image/zan/map.png">
      <span>新闻资讯</span>
      <img src="../../assets/image/zan/left.png">
      <span>标准解读</span>
    </div>
    <img
      class="notic-top-bg"
      src="../../assets/image/zan/biaozhun.png"
    >
    <div class="biaozhun-item">
      <img
        v-for="(item,index) in noticList"
        :key="index"
        :src="item.imgUrl"
        @click="gotoNoticeInfo(item)"
      >
    </div>
  </div>
</template>
<script>
import {getNewsAll,getNewsInfo4Pdf} from '@/api/newadd';
import {resourcePath,} from "@/api/file";
export default {
    data() {
        return {
            parmas: {
                SkipCount: 0,
                MaxResultCount: 10,
                Sorting: '',
                category:0,
                page:1,
                total:0,
            },
            noticList:[]
        };
    },
    mounted() {
        this.getList();
    },
    methods: {
        gotoNoticeInfo(item) {

            // if(item.isPdf){
            //     getNewsInfo4Pdf({ id: item.id }).then((res) => {
            //     resourcePath({
            //         url: res.announcementAttachments[0].url
            //         })
            //         .then((res) => {
            //             window.open('./pdfjs/web/viewer.html?file=' + encodeURIComponent(res), "_blank");
            //         })
            //         .catch(() => {
            //             this.$message.error('获取资源地址失败')
            //         })
            // });
            //     return
            // }
            const url = this.$router.resolve({
                name: "newsnoticeinfo",
                query: {
                    id: item.id,
                    isPdf:item.isPdf,
                    mapname:'0' },
            }).href;

            window.open(url, "_blank");
        },
        handleSizeChange(val) {
            this.parmas.MaxResultCount = val
            this.getList();
        },
        handleCurrentChange(val) {
            this.parmas.page = val
            this.getList();
        },
        getList(){
            this.parmas.SkipCount=this.parmas.MaxResultCount*(this.parmas.page-1)
            getNewsAll(this.parmas).then(res => {
                this.noticList = res.items;
                this.parmas.total = res.totalCount;
            });
        }
    }
};
</script>
<style scoped>

</style>
