import axios from '@/axios'

export function getFileDownloadInfo(id) {
  return axios.gets(`/api/file-management/file/${id}/download-info`)
}

export function getFileContent(url) {
  return axios.localFileContent(url)
}

export function getBaoCloudResourceUrl(url) {
  return axios.gets('api/cms/baocloud/get-presigned-url?url=' + url)
}
// 资源预览下载获取地址
// url
export function resourcePath(data) {
  return axios.gets(`/api/cloud-resource/bce/get-presigned-url`, data)
}
