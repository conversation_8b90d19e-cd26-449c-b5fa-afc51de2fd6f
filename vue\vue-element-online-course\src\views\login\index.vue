<template>
    <div class="login_container">
        <div class="left_container">
            <img class="login_bg" src="/images/login_bg.png" />
            <!-- <img class="login_icon" src="/images/logo.png" /> -->
            <!-- <img class="right_icon" src="/images/right_icon.png" /> -->
        </div>
        <div class="right_container">
            <!-- <div class="login_content"> -->
            <el-form
                ref="loginForm"
                class="form_style login_form"
                :model="loginForm"
                :rules="loginRules"
            >
                <div class="login_type">
                    <div
                        :class="{
                            btn: true,
                            active: loginForm.loginType === 0,
                        }"
                        type="text"
                        @click="changeLoginType(0)"
                    >
                        密码登录
                    </div>
                    <!-- <div
                        :class="{
                            btn: true,
                            active: loginForm.loginType === 1,
                        }"
                        type="text"
                        @click="changeLoginType(1)"
                    >
                        手机号登录
                    </div> -->
                </div>
                <div v-if="loginForm.loginType === 0">
                    <div class="form_label">用户名</div>
                    <el-form-item prop="username" class="login_user_item">
                        <el-input
                            v-model="loginForm.username"
                            class="input_style login_input login_user"
                            autocomplete="on"
                            placeholder="请输入用户名"
                        >
                            <i
                                slot="prefix"
                                style="
                                    display: flex;
                                    align-items: center;
                                    height: 100%;
                                "
                            >
                                <span class="svg-container">
                                    <svg-icon icon-class="user" />
                                </span>
                            </i>
                        </el-input>
                    </el-form-item>
                    <div class="form_label">密码</div>
                    <el-form-item prop="password" class="login_user_item">
                        <el-input
                            v-model="loginForm.password"
                            class="input_style login_input login_pass"
                            show-password
                            autocomplete="on"
                            placeholder="请输入密码"
                        >
                            <i
                                slot="prefix"
                                style="
                                    display: flex;
                                    align-items: center;
                                    height: 100%;
                                "
                            >
                                <span class="svg-container">
                                    <svg-icon icon-class="password" />
                                </span>
                            </i>
                        </el-input>
                    </el-form-item>
                    <div class="form_label">验证码</div>
                    <el-form-item prop="validCode" class="valid_form_item">
                        <el-input
                            v-model="loginForm.validCode"
                            style="width: 70%"
                            class="input_style login_input"
                            placeholder="请输入验证码"
                            @keyup.enter.native="handleLogin"
                        >
                            <i
                                slot="prefix"
                                style="
                                    display: flex;
                                    align-items: center;
                                    height: 100%;
                                "
                            >
                                <span class="svg-container">
                                    <svg-icon icon-class="code" />
                                </span>
                            </i>
                        </el-input>

                        <valid-code
                            ref="validCode"
                            style="width: 30%; padding: 0"
                            @input="createValidCode"
                        />
                    </el-form-item>
                </div>
                <div v-if="loginForm.loginType === 1">
                    <div class="form_label">工号</div>
                    <el-form-item
                        prop="studentIDNumber"
                        class="login_user_item"
                    >
                        <el-input
                            v-model="loginForm.studentIDNumber"
                            class="input_style login_input login_user"
                            autocomplete="on"
                            placeholder="请输入工号"
                        >
                            <i
                                slot="prefix"
                                style="
                                    display: flex;
                                    align-items: center;
                                    height: 100%;
                                "
                            >
                                <span class="svg-container">
                                    <svg-icon icon-class="number" />
                                </span>
                            </i>
                        </el-input>
                    </el-form-item>
                    <div class="form_label">手机号</div>
                    <el-form-item prop="phoneNumber" class="login_user_item">
                        <el-input
                            v-model="loginForm.phoneNumber"
                            class="input_style login_input login_user"
                            autocomplete="on"
                            placeholder="请输入手机号"
                        >
                            <i
                                slot="prefix"
                                style="
                                    display: flex;
                                    align-items: center;
                                    height: 100%;
                                "
                            >
                                <span class="svg-container">
                                    <svg-icon icon-class="phone" />
                                </span>
                            </i>
                        </el-input>
                    </el-form-item>
                    <div class="form_label">验证码</div>
                    <el-form-item prop="code" class="valid_form_item">
                        <el-input
                            v-model="loginForm.code"
                            class="input_style login_input"
                            placeholder="请输入验证码"
                            @keyup.enter.native="handleLogin"
                        >
                            <i
                                slot="prefix"
                                style="
                                    display: flex;
                                    align-items: center;
                                    height: 100%;
                                "
                            >
                                <span class="svg-container">
                                    <svg-icon icon-class="code" />
                                </span>
                            </i>
                        </el-input>
                        <el-button
                            type="primary"
                            round
                            style="margin-left: 20px"
                            class="color_btn verificateCodeButton"
                            :disabled="loginverificateCodeButtonDisabled"
                            @click="getVerificateCode(0)"
                        >
                            {{ loginverificateCodeButtonTitle }}
                        </el-button>
                    </el-form-item>
                </div>
                <el-link
                    class="link_style"
                    :underline="false"
                    style="float: right"
                    @click="handleForgetPasswordClick"
                >
                    忘记密码?
                </el-link>
                <el-button
                    class="color_btn login_btn"
                    round
                    :loading="loadingForm"
                    type="primary"
                    @click.native.prevent="handleLogin"
                >
                    登 录
                </el-button>
                <div style="margin-top: 20px">
                    <!-- <span style="color: red; font-size: 14px">
                        温馨提示：<br />
                        因弱口令升级，请10月15日后首次登录时用手机号登录的方式，登录后按提示更改为强密码。
                    </span> -->
                </div>
            </el-form>
            <!-- </div> -->
            <!-- <div class="footer">
        <div class="footer_span">
          上海景格科技股份有限公司 版权所有
        </div>
        <div class="footer_span">
          Copyright © 2020 - 2021 Jingge. All Rights Reserved.
        </div>
      </div> -->
        </div>

        <el-dialog
            :visible.sync="findPassword"
            :close-on-click-modal="false"
            width="600px"
        >
            <div class="find_title_item">
                <div class="find_icon" />
                <span class="find_title">找回密码</span>
            </div>
            <div v-if="step === 0">
                <div class="find_item" @click="handleFindPassword(0)">
                    <span class="svg-container">
                        <svg-icon icon-class="user" />
                    </span>
                    <span>用户名 + </span>
                    <span class="svg-container">
                        <svg-icon icon-class="phone" />
                    </span>
                    <span>手机验证码找回密码</span>
                </div>
                <div class="find_item" @click="handleFindPassword(1)">
                    <span class="svg-container">
                        <svg-icon icon-class="number" />
                    </span>
                    <span>工 号 + </span>
                    <span class="svg-container">
                        <svg-icon icon-class="phone" />
                    </span>
                    <span>手机验证码找回密码</span>
                </div>
            </div>
            <div v-if="step === 1">
                <el-form
                    ref="findPasswordForm"
                    :model="findPasswordForm"
                    label-width="80px"
                    class="info_form form_style"
                    :rules="findPasswordFormRules"
                >
                    <el-form-item
                        v-if="findType === 1"
                        label="工号"
                        prop="studentIDNumber"
                    >
                        <el-input
                            v-model="findPasswordForm.studentIDNumber"
                            class="input_style login_input login_user"
                            autocomplete="on"
                            placeholder="请输入工号"
                        >
                            <i
                                slot="prefix"
                                style="
                                    display: flex;
                                    align-items: center;
                                    height: 100%;
                                "
                            >
                                <span class="svg-container">
                                    <svg-icon icon-class="number" />
                                </span>
                            </i>
                        </el-input>
                    </el-form-item>
                    <el-form-item
                        v-if="findType === 0"
                        label="用户名"
                        prop="username"
                    >
                        <el-input
                            v-model="findPasswordForm.username"
                            class="input_style login_input login_user"
                            autocomplete="on"
                            placeholder="请输入用户名"
                        >
                            <i
                                slot="prefix"
                                style="
                                    display: flex;
                                    align-items: center;
                                    height: 100%;
                                "
                            >
                                <span class="svg-container">
                                    <svg-icon icon-class="user" />
                                </span>
                            </i>
                        </el-input>
                    </el-form-item>
                </el-form>
            </div>
            <div v-if="step === 2">
                <el-form
                    ref="findPasswordForm"
                    :model="findPasswordForm"
                    label-width="80px"
                    class="info_form form_style"
                    :rules="findPasswordFormRules"
                >
                    <el-form-item label="手机号" prop="phoneNumber">
                        <el-input
                            v-model="findPasswordForm.phoneNumber"
                            class="input_style login_input login_user"
                            autocomplete="on"
                            placeholder="请输入手机号"
                        >
                            <i
                                slot="prefix"
                                style="
                                    display: flex;
                                    align-items: center;
                                    height: 100%;
                                "
                            >
                                <span class="svg-container">
                                    <svg-icon icon-class="phone" />
                                </span>
                            </i>
                        </el-input>
                    </el-form-item>
                    <el-form-item
                        label="验证码"
                        prop="resetToken"
                        class="valid_form_item"
                    >
                        <el-input
                            v-model="findPasswordForm.resetToken"
                            class="input_style login_input"
                            placeholder="请输入验证码"
                        >
                            <i
                                slot="prefix"
                                style="
                                    display: flex;
                                    align-items: center;
                                    height: 100%;
                                "
                            >
                                <span class="svg-container">
                                    <svg-icon icon-class="code" />
                                </span>
                            </i>
                        </el-input>

                        <el-button
                            type="primary"
                            round
                            style="margin-left: 20px"
                            class="color_btn verificateCodeButton"
                            :disabled="verificateCodeButtonDisabled"
                            @click="getVerificateCode(1)"
                        >
                            {{ verificateCodeButtonTitle }}
                        </el-button>
                    </el-form-item>
                </el-form>
            </div>
            <div v-if="step === 3">
                <el-form
                    ref="findPasswordForm"
                    :model="findPasswordForm"
                    label-width="80px"
                    class="info_form form_style"
                    :rules="findPasswordFormRules"
                >
                    <el-form-item label="新密码" prop="password">
                        <el-input
                            v-model="findPasswordForm.password"
                            class="input_style login_input login_pass"
                            show-password
                            autocomplete="on"
                            placeholder="请输入密码"
                        >
                            <i
                                slot="prefix"
                                style="
                                    display: flex;
                                    align-items: center;
                                    height: 100%;
                                "
                            >
                                <span class="svg-container">
                                    <svg-icon icon-class="password" />
                                </span>
                            </i>
                        </el-input>
                    </el-form-item>
                    <el-form-item label="确认密码" prop="checkPassword">
                        <el-input
                            v-model="findPasswordForm.checkPassword"
                            class="input_style login_input login_pass"
                            show-password
                            autocomplete="on"
                            placeholder="请再次输入密码"
                        >
                            <i
                                slot="prefix"
                                style="
                                    display: flex;
                                    align-items: center;
                                    height: 100%;
                                "
                            >
                                <span class="svg-container">
                                    <svg-icon icon-class="password" />
                                </span>
                            </i>
                        </el-input>
                    </el-form-item>
                    <el-form-item>
                        <span
                            >注：请将密码设置为8—20位，且由大写字母、小写字母、符号、数字组成</span
                        >
                    </el-form-item>
                </el-form>
            </div>
            <div v-if="step !== 0" class="bottom_box">
                <el-button round plain @click="stepPrev"> 上一步 </el-button>
                <el-button
                    v-if="step !== 3"
                    class="color_btn sure_btn"
                    round
                    type="primary"
                    @click="stepNext"
                >
                    下一步
                </el-button>
                <el-button
                    v-if="step === 3"
                    class="color_btn sure_btn"
                    round
                    type="primary"
                    @click="handleChangePasswordSure"
                >
                    确 定
                </el-button>
            </div>
        </el-dialog>
    </div>
</template>
<script>
import config from "@/config/index";
import {
    findTenant,
    sendPasswordResetCode,
    sendLoginCode,
    checkPasswordResetCode,
    checkJobNumber,
    resetPassword,
} from "@/api/user";
import ValidCode from "@/components/ValidCode.vue";
export default {
    name: "Login",
    components: { ValidCode },
    data() {
        const validatePassword = (rule, value, callback) => {
            var Regx = new RegExp(
                /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=(.*\W)).{8,}$/
            );
            if (
                value.length > 7 &&
                value.length < 21 &&
                Regx.test(this.findPasswordForm.password)
            ) {
                callback();
            } else {
                callback(
                    new Error(
                        "请输入不少于8位,不超过20位且包含大写字母、小写字母、符号、数字的密码"
                    )
                );
            }
        };
        const validatePhoneNumber = (rule, value, callback) => {
            if (value == null && value === "" && value.length === 0) {
                callback(new Error("请输入手机号"));
            } else {
                var reg_tel = /^(1[3-9])\d{9}$/;
                var reg_Tel = new RegExp(reg_tel);
                if (!reg_Tel.test(value)) {
                    callback(new Error("请输入正确的手机号"));
                } else {
                    callback();
                }
            }
        };
        const validateCode = (rule, value, callback) => {
            if (value.length !== 6) {
                callback(new Error("请输入正确的验证码"));
            } else {
                var numReg = /^[0-9]+$/;
                var numRe = new RegExp(numReg);
                if (!numRe.test(value)) {
                    callback(new Error("请输入正确的验证码"));
                } else {
                    callback();
                }
            }
        };
        const validateCheckPassword = (rule, value, callback) => {
            if (value == null && value === "" && value.length === 0) {
                callback(new Error("请再次输入密码"));
            } else if (value !== this.findPasswordForm.password) {
                callback(new Error("两次输入密码不一致!"));
            } else {
                callback();
            }
        };
        return {
            loading: false,
            loginForm: {
                tenant: undefined,
                username: "",
                password: "",
                client_id: config.client.client_id,
                client_secret: config.client.client_secret,
                grant_type: config.client.grant_type,
                loginType: 0,
                code: "",
                validCode: "",
                studentIDNumber: "",
                phoneNumber: "",
            },
            loginRules: {
                username: [
                    {
                        required: true,
                        message: "请输入账号",
                        trigger: "blur",
                    },
                ],
                password: [
                    {
                        required: true,
                        message: "请输入密码",
                        trigger: "blur",
                        // validator: validatePassword
                    },
                ],
                phoneNumber: [
                    {
                        required: true,
                        trigger: "blur",
                        validator: validatePhoneNumber,
                    },
                ],
                studentIDNumber: [
                    {
                        required: true,
                        message: "请输入工号",
                        trigger: "blur",
                    },
                ],
                code: [
                    {
                        required: true,
                        trigger: "blur",
                        validator: validateCode,
                    },
                ],
                validCode: [
                    {
                        required: true,
                        message: "请输入验证码",
                        trigger: "blur",
                    },
                ],
            },
            // 验证码重新发送间隔时间
            resendVerificateCodeTime: 120,
            // 发送短信按钮是否禁用
            verificateCodeButtonDisabled: false,
            loadingForm: false,
            // 按钮标题
            verificateCodeButtonTitle: "发送验证码",

            // 验证码重新发送间隔时间
            loginresendVerificateCodeTime: 120,
            // 发送短信按钮是否禁用
            loginverificateCodeButtonDisabled: false,
            // 按钮标题
            loginverificateCodeButtonTitle: "发送验证码",

            validCode: "",
            findPassword: false,
            findType: 0,
            step: 0,
            findPasswordForm: {
                username: "",
                studentIDNumber: "",
                phoneNumber: "",
                resetToken: "",
                resetTokenId: "",
                password: "",
            },
            findPasswordFormRules: {
                username: [
                    {
                        required: true,
                        message: "请输入用户名",
                        trigger: "blur",
                    },
                ],
                studentIDNumber: [
                    {
                        required: true,
                        message: "请输入工号",
                        trigger: "blur",
                    },
                ],
                phoneNumber: [
                    {
                        required: true,
                        trigger: "blur",
                        validator: validatePhoneNumber,
                    },
                ],
                password: [
                    {
                        required: true,
                        trigger: "blur",
                        validator: validatePassword,
                    },
                ],
                resetToken: [
                    {
                        required: true,
                        trigger: "blur",
                        validator: validateCode,
                    },
                ],
                checkPassword: [
                    {
                        required: true,
                        trigger: "blur",
                        validator: validateCheckPassword,
                    },
                ],
            },
        };
    },
    watch: {
        $route: {
            handler: function (route) {
                const query = route.query;
                if (query) {
                    this.redirect = query.redirect;
                    this.otherQuery = this.getOtherQuery(query);
                }
            },
            immediate: true,
        },
    },
    mounted() {},
    methods: {
        changeLoginType(t) {
            this.loginForm.loginType = t;
            if (this.$refs.loginForm) {
                this.$refs.loginForm.resetFields();
            }
        },
        handleLogin() {
            this.$refs.loginForm.validate((valid) => {
                if (valid) {
                    if (this.loginForm.loginType === 0) {
                        if (
                            this.loginForm.validCode.toLowerCase() !==
                            this.validCode.toLowerCase()
                        ) {
                            this.$message.error("验证码错误");
                            this.$refs.validCode.refreshCode();
                            return;
                        }
                        this.loadingForm = true;
                        // findTenant(this.loginForm)
                        //   .then((res) => {
                        //     if (res != "") {
                        this.loginForm.tenant = "ct"; //res;
                        this.$store
                            .dispatch("user/login", this.loginForm)
                            .then(async () => {
                                //this.loginflag = false;
                                this.loadingForm = false;
                                this.$store.dispatch(
                                    "user/toggleloginbox",
                                    false
                                );
                                await this.$store.dispatch("user/getInfo");
                                // var Regx = new RegExp(/^(?![^a-zA-Z]+$)(?!\D+$)/);
                                // if (
                                //   this.loginForm.password.length > 7 &&
                                //   Regx.test(this.loginForm.password)
                                // ) {
                                //   this.$store.dispatch("user/passwordValid", true);
                                // } else {
                                //   this.$store.dispatch("user/passwordValid", false);
                                // }
                                const passwordValid =
                                    this.$store.getters.passwordValid;
                                // console.log('passwordValid',passwordValid)
                                if (!passwordValid) {
                                    // console.log('11')
                                    this.$router.push({
                                        path: "changePassword",
                                    });
                                    return;
                                }

                                this.$router.push({
                                    path: this.redirect || "/",
                                    query: this.otherQuery,
                                });

                                // this.$store.dispatch("user/badgeNum");
                                //this.$message.success("登录成功");
                            })
                            .catch((err) => {
                                this.loadingForm = false;
                                this.$refs.validCode.refreshCode();
                                //this.$message.error("登录失败");
                            });
                        //   }
                        // })
                        // .catch((err) => {
                        //   this.loadingForm = false;
                        // });
                    } else {
                        var data = {
                            phoneNumber: this.loginForm.phoneNumber,
                            code: this.loginForm.code,
                            studentIDNumber: this.loginForm.studentIDNumber,
                            clientId: config.client.client_id,
                            clientSecret: config.client.client_secret,
                        };
                        this.$store
                            .dispatch("user/loginByPhone", data)
                            .then((res) => {
                                this.loadingForm = false;
                                this.$store.dispatch(
                                    "user/toggleloginbox",
                                    false
                                );
                                this.$store.dispatch("user/getInfo");
                                // this.$store.dispatch("user/passwordValid", true);
                                this.$router.push({
                                    path: this.redirect || "/",
                                    query: this.otherQuery,
                                });
                            });
                    }
                } else {
                    return false;
                }
            });
        },
        handleForgetPasswordClick() {
            if (this.$refs.findPasswordForm) {
                this.$refs.findPasswordForm.resetFields();
            }
            this.restFindPasswordForm();

            this.findPassword = true;
        },

        getOtherQuery(query) {
            return Object.keys(query).reduce((acc, cur) => {
                if (cur !== "redirect") {
                    acc[cur] = query[cur];
                }
                return acc;
            }, {});
        },
        getVerificateCode(t) {
            if (t === 1) {
                this.$refs.findPasswordForm.validateField(
                    "phoneNumber",
                    (phoneNumber) => {
                        if (!phoneNumber) {
                            this.resendVerificateCodeTime = 120;
                            this.verificateCodeButtonDisabled = true;
                            console.log(this.findPasswordForm);
                            var data = {};
                            if (this.findType === 0) {
                                data = {
                                    phone: this.findPasswordForm.phoneNumber,
                                    appName: "VMSWeb",
                                    username: this.findPasswordForm.username,
                                    returnUrl: "",
                                    returnUrlHash: "",
                                };
                            } else {
                                data = {
                                    phone: this.findPasswordForm.phoneNumber,
                                    appName: "VMSWeb",
                                    studentIDNumber:
                                        this.findPasswordForm.studentIDNumber,
                                    returnUrl: "",
                                    returnUrlHash: "",
                                };
                            }

                            sendPasswordResetCode(data)
                                .then((res) => {
                                    this.timer();
                                    this.findPasswordForm.resetTokenId = res;
                                    this.$message.success("发送成功");
                                })
                                .catch(() => {
                                    this.verificateCodeButtonDisabled = false;
                                });
                        }
                    }
                );
            } else {
                this.$refs.loginForm.validateField(
                    "phoneNumber",
                    (phoneNumber) => {
                        if (!phoneNumber) {
                            this.loginresendVerificateCodeTime = 120;
                            this.loginverificateCodeButtonDisabled = true;
                            var data = {
                                phoneNumber: this.loginForm.phoneNumber,
                                studentIDNumber: this.loginForm.studentIDNumber,
                            };
                            sendLoginCode(data)
                                .then((res) => {
                                    this.logintimer();
                                    this.$message.success("发送成功");
                                })
                                .catch(() => {
                                    this.loginverificateCodeButtonDisabled = false;
                                });
                        }
                    }
                );
            }
        },
        timer() {
            if (this.resendVerificateCodeTime > 0) {
                this.verificateCodeButtonDisabled = true;
                this.resendVerificateCodeTime--;
                this.verificateCodeButtonTitle =
                    "(" + this.resendVerificateCodeTime + "s)后重新发送";
                setTimeout(this.timer, 1000);
            } else {
                this.resendVerificateCodeTime = 0;
                this.verificateCodeButtonTitle = "发送验证码";
                this.verificateCodeButtonDisabled = false;
            }
        },
        logintimer() {
            if (this.loginresendVerificateCodeTime > 0) {
                this.loginverificateCodeButtonDisabled = true;
                this.loginresendVerificateCodeTime--;
                this.loginverificateCodeButtonTitle =
                    "(" + this.loginresendVerificateCodeTime + "s)后重新发送";
                setTimeout(this.logintimer, 1000);
            } else {
                this.loginresendVerificateCodeTime = 0;
                this.loginverificateCodeButtonTitle = "发送验证码";
                this.loginverificateCodeButtonDisabled = false;
            }
        },
        handleFindPassword(t) {
            this.findType = t;
            this.step = 1;
        },
        stepPrev() {
            if (this.$refs.findPasswordForm) {
                this.$refs.findPasswordForm.resetFields();
            }
            if (this.step > 0) {
                this.step -= 1;
            }
        },
        stepNext() {
            if (this.step === 1) {
                this.checkUsernameOrNumber();
            } else if (this.step === 2) {
                this.handlePhoneFindPasswordSure();
            }
        },
        checkUsernameOrNumber() {
            if (this.findType === 0) {
                // 用户名
                this.$refs.findPasswordForm.validateField(
                    "username",
                    (username) => {
                        if (!username) {
                            findTenant(this.findPasswordForm).then((res) => {
                                this.step = 2;
                            });
                        }
                    }
                );
            } else {
                // 工号
                this.$refs.findPasswordForm.validateField(
                    "studentIDNumber",
                    (studentIDNumber) => {
                        if (!studentIDNumber) {
                            checkJobNumber(
                                this.findPasswordForm.studentIDNumber
                            ).then((res) => {
                                if (res) {
                                    this.step = 2;
                                } else {
                                    this.$message.error("请输入正确的工号");
                                }
                            });
                        }
                    }
                );
            }
        },
        handlePhoneFindPasswordSure() {
            this.$refs.findPasswordForm.validate((valid) => {
                if (valid) {
                    var data = {
                        token: this.findPasswordForm.resetToken,
                        phoneNumber: this.findPasswordForm.phoneNumber,
                        id: this.findPasswordForm.resetTokenId,
                    };
                    checkPasswordResetCode(data)
                        .then((res) => {
                            if (res) {
                                this.step = 3;
                            } else {
                                this.$message.error("验证码错误");
                            }
                        })
                        .catch(() => {
                            this.$message.error("验证码错误");
                        });
                } else {
                    return false;
                }
            });
        },
        handleChangePasswordSure() {
            this.$refs.findPasswordForm.validate((valid) => {
                if (valid) {
                    resetPassword(this.findPasswordForm)
                        .then((res) => {
                            this.$message.success("修改成功");
                            this.findPassword = false;
                        })
                        .catch(() => {
                            this.$message.error("修改失败");
                        });
                } else {
                    return false;
                }
            });
        },

        restFindPasswordForm() {
            this.step = 0;
            this.findPasswordForm = {
                username: "",
                studentIDNumber: "",
                phoneNumber: "",
                resetToken: "",
                resetTokenId: "",
                password: "",
            };
        },
        createValidCode(data) {
            this.validCode = data;
        },
    },
};
</script>
<style lang="scss">
html {
    min-width: auto !important;
}
$main_color: #003686;
$secondary_color: #005dc2;
$el-color: #606266;
$border_color: #dcdfe6;
$main_border_color: #003686;
.login_container {
    display: flex;
    height: 100vh;
    overflow: hidden;
    // -webkit-filter: grayscale(100%);
    // filter: grayscale(100%);
}

.color_btn {
    background: linear-gradient(to right, $secondary_color, $main_color);
    border: none;
    color: white;
    // height: 48px;
}
.color_btn:hover,
.color_btn:focus {
    color: white;
    background: linear-gradient(to right, $secondary_color, $main_color);
}
// .input_style .el-input__inner {
//   height: 48px;
// }
.form_style {
    .el-form-item.is-error .el-input__inner {
        border-color: $border_color !important;
    }
    .el-form-item.is-error .el-input__inner:focus,
    .el-form-item.is-error .el-input__inner:focus-within {
        border-color: $main_border_color !important;
    }
    .el-form-item__error {
        padding-top: 8px;
    }
    .valid_form_item .el-form-item__content {
        display: flex;
    }
    .input_style:focus-within {
        border-color: $main_border_color !important;

        .svg-icon {
            color: $main_color;
        }
    }
}
.left_container,
.right_container {
    flex: 1;
    height: 100%;
    background-color: #fff;
    position: relative;
}
.login_bg {
    width: 100%;
    height: 100%;
}
.right_container {
    display: flex;
    background-color: white;
}
.login_icon {
    position: absolute;
    top: 44px;
    left: 55px;
    width: 180px;
    height: auto;
    z-index: 99;
}
// .login_content {
//   display: flex;
//   height: calc(100% - 70px);
// }
.right_icon {
    width: 100px;
    height: 100px;
    position: absolute;
    top: calc(50% - 25px);
    right: -50px;
    z-index: 99;
}

.login_form {
    align-self: center;
    max-width: 480px;
    margin: 0 auto;
    width: 85%;
}

.login_form .title {
    font-size: 38px;
    text-align: left;
    margin-bottom: 80px;
}

.login_form .form_label {
    margin-bottom: 20px;
    font-size: 14px;
    // color: $el-color;
}

.login_input .el-input__inner {
    padding-left: 40px;
}

.input_style .el-input__inner:focus {
    border-color: $main_border_color !important;
}

.svg-container {
    font-size: 20px;
    vertical-align: middle;
    color: rgb(115, 116, 138);
    margin: 0 5px;
}

.login_user .el-input__inner:focus-within,
.login_pass .el-input__inner:focus-within {
    border-color: $main_border_color !important;
}

.login_user_item {
    margin-bottom: 40px;
}

.login_user_item .login_user:focus-within,
.login_pass_item .login_pass:focus-within {
    border-color: $main_border_color !important;

    .svg-icon {
        color: $main_color;
    }
}

.login_pass_item {
    margin-bottom: 20px;
}

.link_style:hover {
    color: $main_color !important;
}

.login_btn {
    width: 100%;
    margin-top: 40px;
}

.footer {
    text-align: center;
    position: absolute;
    bottom: 10px;
    width: 100%;
    // height: 70px;
    // margin-bottom: 10px;
}

.footer_span {
    font-size: 12px;
    margin-bottom: 10px;
    color: #ccc;
}

.customDialog {
    border-radius: 15px;
    min-width: 300px;
    max-width: 350px;
}
.forget-form .el-form-item {
    margin-bottom: 30px;
}
.verificateCodeButton.is-disabled {
    background: linear-gradient(
        to right,
        $secondary_color,
        $main_color
    ) !important;
}
.loginRegistButton {
    width: 100%;
    margin-bottom: 20px;
}

.login_type {
    margin-bottom: 40px;
    .btn {
        width: 100%;
        height: 50px;
        font-weight: 700;
        display: inline-block;
        text-align: center;
        line-height: 50px;
        font-size: 22px;
        letter-spacing: 2px;
        border-bottom: 1px solid #cdcdcd;
        cursor: pointer;
    }
    .btn.active {
        border-bottom: 2px solid $main_color;
    }
}

.find_title_item {
    display: flex;
    justify-content: center;
    align-self: center;
    margin-bottom: 40px;
}
.find_icon {
    height: 20px;
    width: 5px;
    background-color: $main_color;
}
.find_title {
    margin-left: 12px;
    font-size: 18px;
    font-weight: 600;
}
.find_item {
    margin: 40px auto;
    width: 320px;
    height: 80px;
    border: 1px solid #cdcdcd;
    border-radius: 10px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 16px;
    cursor: pointer;
    .svg-icon {
        color: $main_color;
    }
    &:hover {
        border-color: $main_border_color;
    }
}

.info_form {
    margin: 60px auto;
    width: 500px;
}
.info_form {
    .valid_form_item .el-form-item__content {
        display: flex;
    }
    .step_box {
        display: flex;
        margin-top: 40px;
        justify-content: space-around;
    }
}
</style>
