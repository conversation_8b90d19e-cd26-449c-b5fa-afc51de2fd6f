<template>
    <div class="biaozhun">
        <div class="maps">
            <img src="../../assets/image/zan/map.png" />
            <span>人才中心</span>
            <img src="../../assets/image/zan/left.png" />
            <span>人才简历</span>
        </div>
        <div class="qiye-box">
            <div class="qiye-item" v-for="item in list" :key="item.id" @click="go(item.id)">
                <img :src="item.imgUrl" class="imgs" />
                <div class="font1">
                    <p>{{item.name}}</p>
                    <p>{{item.title}}</p>
                </div>
                <div class="font2">
                    <p>{{ item.year }}</p>
                    <p>{{ item.education }}</p>
                    <p>{{ item.age }}</p>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
export default {
    data() {
        return {
            list:[
                {
                    id:1,
                    title:'汽车设计师',
                    name:'王一轩',
                    imgUrl:require('../../assets/image/static/jianli1.png'),
                    year:'1-3年经验',
                    education:'本科',
                    age:'25岁'
                },
                {
                    id:2,
                    title:'汽车工程师',
                    name:'王一',
                    imgUrl:require('../../assets/image/static/jianli2.png'),
                    year:'3年经验',
                    education:'硕士',
                    age:'26岁'
                },
                {
                    id:3,
                    title:'生产工程师',
                    name:'刘强',
                    imgUrl:require('../../assets/image/static/jianli3.png'),
                    year:'3年经验',
                    education:'硕士',
                    age:'34岁'
                },
                {
                    id:4,
                    title:'质量工程师',
                    name:'张磊',
                    imgUrl:require('../../assets/image/static/jianli4.png'),
                    year:'3年经验',
                    education:'本科',
                    age:'26岁'
                },
                {
                    id:5,
                    title:'车联网工程师',
                    name:'王峰',
                    imgUrl:require('../../assets/image/static/jianli5.png'),
                    year:'2年经验',
                    education:'硕士',
                    age:'26岁'
                },
                {
                    id:6,
                    title:'售后技术顾问',
                    name:'何赛',
                    imgUrl:require('../../assets/image/static/jianli6.png'),
                    year:'3年经验',
                    education:'本科',
                    age:'26岁'
                },
                {
                    id:7,
                    title:'汽车定损员',
                    name:'李明',
                    imgUrl:require('../../assets/image/static/jianli7.png'),
                    year:'3年经验',
                    education:'硕士',
                    age:'29岁'
                },
                {
                    id:8,
                    title:'售后技术顾问',
                    name:'周和',
                    imgUrl:require('../../assets/image/static/jianli8.png'),
                    year:'3年经验',
                    education:'硕士',
                    age:'26岁'
                }
            ]
        }
    },
    methods: {
        go(id) {
            if(id===1){
                const url = this.$router.resolve({
                    name: "personalDetail"
                }).href;

                window.open(url, "_blank");
            }

        }
    }
}
</script>
<style scoped>
.qiye-box{
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    margin-top: 20px;
}
.qiye-item{
    box-shadow: 0 5px 10px 0 rgba(0, 0, 0, 0.10);
    border-radius: 20px;
    width: 260px;
    /* height: 150px; */
    -webkit-box-sizing: border-box;
    background: #fff;
    margin-right: 60px;
    margin-bottom: 50px;
    cursor:  pointer;
}
.imgs{
    width: 100%;
    height: 220px;
    display: block;
}
.font1{
    display: flex;
    justify-content: space-between;
    box-sizing: border-box;
    padding: 0 20px;
    margin-top: 10px;
}
.font1>p{
    margin: 0 !important;
}
.font1>p:nth-child(1){font-size:16px;color: #5d5d5d;}
.font1>p:nth-child(2){font-size:18px;color: #096dd9;}
.font2{display: flex; box-sizing: border-box;
    padding: 0 20px;}
.font2>p{
    background: #ebeef2;
    color: #a9aaab;
    padding:2px 6px;
    box-sizing: border-box;
    border-radius: 3px;
    margin-right: 10px;
    font-size: 14px;
}
</style>
