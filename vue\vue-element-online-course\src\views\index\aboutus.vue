<template>
  <div class="aboutus-body">
    <div class="breadcrumb-box ">
      <el-breadcrumb separator-class="el-icon-arrow-right" class="bread_con">
        <el-breadcrumb-item :to="{ path: '/' }"><i class="el-icon-location-outline"></i></el-breadcrumb-item>
        <el-breadcrumb-item >关于我们</el-breadcrumb-item>
      </el-breadcrumb>
    </div>
    <div class="aboutus-body-con">
      <div class="Head-title0"><span class="line"></span><span>关于我们</span></div>
      <div class="cent">
        <div class="box">
          <h1>企业概况</h1>
          <div class="title_en">Enterprise Profile</div>
          <div class="txt-green">职教立业，人才兴邦，让每个人都有出彩的人生。</div>
          <div class="p-txt">景格科技创办于2003年，公司致力成为职业教育服务产业链的引领者。一直专注于职业教育，以汽车专业为起点，不断拓展交通运输类（轨道交通、工程机械、港口机械）、智能制造类（机器人、自动化、数控）等专业领域；以智慧教育为核心，围绕职业院校专业建设需求，提供专业咨询、课程建设、实训中心建设、师资队伍建设、教学评价、创新创业、产教融合等服务。</div>
          <br />
          <div class="p-txt">景格科技多年来凭借卓越的创新能力、精湛的专业技术、高效的管理服务及优秀的企业文化，在业内形成良好口碑！2014年公司在新三板成功挂牌〔股票代码：430638〕，是一家具有核心竞争力的高新技术公众企业。</div>
        </div>
        <div class="box" id="concact">
          <h1>联系我们</h1>
          <div class="title_en">Contact us</div>
          <div class="p-txt font24">上海（总部）</div>
          <div class="p-txt">地址：上海市嘉定区杭桂路1211弄60号（同济大学科技园）</div>
          <div class="p-txt">邮编：201804</div>
          <div class="p-txt">总机：021-52851509</div>
          <div class="p-txt">传真：021-52852059</div>
          <div class="p-txt">客服：400-9201-021</div>
        </div>
        <div class="zizhi">
          <span class="c-c30f22 color-box"><span>2014</span>年  <br>成功挂牌</span>
          <span class="c-f39700 color-box"><span>200</span> <br>多款软件</span>
          <span class="c-8fc31e color-box"><span>200</span> <br>多项专利</span>
          <span class="c-31a7e0 color-box"><span>300</span>项 <br> 荣誉资质</span>
          <span class="c-a40d5e color-box"><span>12</span>个  <br>国家地区</span>
          <span class="c-601986 color-box"><span>3000</span> <br> 多家客户</span>
        </div>
        <img src="/images/about.png" />
      </div>
    </div>

  </div>
</template>

<script>
//import { getCarousel,   getTypes} from '@/api/vms';
export default {
  name: 'Index',
  data() {
   return {

   }
  },
  watch: {
    // $route: {
    //   handler: function(route) {
    //     this.redirect = route.query && route.query.redirect
    //   },
    //   immediate: true
    // }
  },
  mounted(){

  },
  methods: {

  }
}
</script>

<style >

</style>
