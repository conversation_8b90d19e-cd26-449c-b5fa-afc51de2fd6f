<template>
  <div class="evaluate-cent evaluate-box">
    <div class="breadcrumb-box">
      <el-breadcrumb separator-class="el-icon-arrow-right" class="bread_con">
        <el-breadcrumb-item :to="{ path: '/' }">
          <i class="el-icon-location-outline" />
        </el-breadcrumb-item>
        <el-breadcrumb-item :to="{ path: 'myLive' }">
          我的直播
        </el-breadcrumb-item>
        <el-breadcrumb-item>{{ Livename }}</el-breadcrumb-item>
        <el-breadcrumb-item>课程考评</el-breadcrumb-item>
      </el-breadcrumb>
    </div>
    <div v-if="exam!=null&& pageIndex==1" class="evaluate-center">
      <!-- <div class="tab-info-txt">请不要中途开启新的答题流程，不支持多端同时作答</div> -->
      <div class="evaluate-info-div">
        <div class="evaluate-tihao-list">
          <span   :class="[currentIndex==index?'active':'',item.answer2===undefined||item.answer2==='' ||item.answer2.length===0 ?'':'IsDone']" v-for="(item,index) in exam.questions" @click="changeIndex(index)">
            {{index+1}}
            <img v-if="currentIndex==index"  src="/images/active.png" />
          </span>
        </div>
        <div class="evaluate-title">
          <!-- <span class="title">{{exam.name}}</span> -->
          <span class="evaluate-num"><span>{{currentIndex+1}}</span>/{{exam.questions.length}}</span>
        </div>
      <!-- 单选题 -->
      <div v-if="exam.questions[currentIndex].questionType==0"  class="evaluate-question-box" >
          <span class="desc"> {{currentIndex+1}} . 【单选题】{{exam.questions[currentIndex].questionStem.Title}} ({{exam.questions[currentIndex].score}}分) </span>
          <div v-if="exam.questions[currentIndex].questionStem.Title_Imgs!=null" class="imgs">
              <img
                  fit="contain"
                  :src="image1"
                  :key="'img'+ exam.questions[currentIndex].questionBankId + index1"
                  v-show="image1!=''"
                  v-for="(image1,index1) in exam.questions[currentIndex].questionStem.Title_Imgs"
                  />
          </div>
          <el-radio-group v-model="exam.questions[currentIndex].answer2"   @change="flush">
              <el-radio  size="large"  style="margin-bottom:20px"    :label="option.Order" v-for="(option,index) in exam.questions[currentIndex].questionStem.Options" :key="exam.questions[currentIndex].questionBankId + option.Order">
                  {{answerOrder[option.Order-1]}}.{{option.Title}}
                  <div  v-if="option.Images!=null" class="imgs">
                      <img
                          fit="contain"
                          :src="oimage"
                          :key="'img'+ exam.questions[currentIndex].questionBankId + option.Order+ index2"
                          v-show="oimage!=''"
                          v-for="(oimage,index2) in option.Images"
                          />
                  </div>
              </el-radio>
          </el-radio-group>
      </div>
      <!-- 多选 -->
      <div v-if="exam.questions[currentIndex].questionType==1"  class="evaluate-question-box" >
          <span class="desc"> {{currentIndex+1}} . 【多选题】{{exam.questions[currentIndex].questionStem.Title}} ({{exam.questions[currentIndex].score}}分)  </span>
          <div v-if="exam.questions[currentIndex].questionStem.Title_Imgs!=null" class="imgs">
              <img
                  fit="contain"
                  :src="image"
                  :key="'img'+ exam.questions[currentIndex].questionBankId + index1"
                  v-show="image!=''"
                  v-for="(image,index1) in exam.questions[currentIndex].questionStem.Title_Imgs"/>
          </div>
          <el-checkbox-group v-model="exam.questions[currentIndex].answer2"  @change="flush" >
              <el-checkbox  size="large" style="margin-bottom:20px"  :label="option.Order" v-for="(option,index2) in exam.questions[currentIndex].questionStem.Options" :key="exam.questions[currentIndex].questionBankId + option.Order">
                  {{answerOrder[option.Order-1]}}.{{option.Title}}
                  <div  v-if="option.Images!=null"  class="imgs">
                      <img
                          fit="contain"
                          :src="oimage"
                          :key="'img'+ exam.questions[currentIndex].questionBankId + option.Order+ index2"
                          v-show="oimage!=''"
                          v-for="(oimage,index2) in option.Images"/>
                  </div>
              </el-checkbox>
          </el-checkbox-group>
      </div>
      <!-- 判断 -->
      <div v-if="exam.questions[currentIndex].questionType==2"  class="evaluate-question-box"  >
          <span class="desc"> {{currentIndex+1}} .【判断题】 {{exam.questions[currentIndex].questionStem.Title}} ({{exam.questions[currentIndex].score}}分) </span>
          <div v-if="exam.questions[currentIndex].questionStem.Title_Imgs!=null" class="imgs">
              <img
                  fit="contain"
                  :src="image"
                  :key="'img'+ exam.questions[currentIndex].questionBankId + index1"
                  v-show="image!=''"
                  v-for="(image,index1) in exam.questions[currentIndex].questionStem.Title_Imgs"
                  />
          </div>
          <el-radio-group v-model="exam.questions[currentIndex].answer2" direction="horizontal" @change="flush">
            <el-radio label="正确"  style="margin-bottom:20px"  size="large"  >A.正确</el-radio>
            <el-radio label="错误"  style="margin-bottom:20px"  size="large" >B.错误</el-radio>
          </el-radio-group>
      </div>
      </div>
      <div class="evaluate-opr">
        <button class="prev" :disabled="currentIndex==0" @click="prev()">上一题</button>
        <div class="fr">
          <button class="btn-submit" @click="submitPage">提交考评</button>
          <button class="next" :disabled="exam!=null && currentIndex==exam.questions.length-1" @click="next()">下一题</button>
        </div>
      </div>
    </div>
    <div v-if="pageIndex==2&& result!=null" class="evaluate-center">
        <div class="evaluate-result-info" v-if="result.totalScore<passScore">
          <img src="/images/unpassed.png" />
          <div class="result-txt">很遗憾，您未通过考评！<br> 请复习相关内容后，再次参加考评.</div>
        </div>
        <div class="evaluate-result-info" v-else>
          <img src="/images/passed.png" />
          <div class="result-txt">恭喜您，通过考评！</div>
        </div>
        <div class="evaluate-result-nums">
          <span class="n-item">
            <img src="/images/dui.png"/>
            <span class="num">答对<span>{{rightNum}}</span> 题</span>
          </span>
          <span class="n-item">
            <img src="/images/cuo.png"/>
            <span class="num">答错<span>{{wrongNum}}</span> 题</span>
          </span>
          <span class="n-item">
            <img src="/images/unanswered.png"/>
            <span class="num">未答<span>{{unansweredNum}}</span> 题</span>
          </span>
        </div>
        <div class="evaluate-opr" style="text-align: center">
          <button class="btn-submit" @click="viewResult()">查看记录</button>
        </div>
    </div>
    <div v-if="pageIndex==3&&result!=null"  class="evaluate-center">
      <div class="evaluate-result-header">
        <span class="r-item">
          <img src="/images/dui.png"/>
          <span class="num">答对<span>{{rightNum}}</span> 题</span>
        </span>
        <span class="r-item">
          <img src="/images/cuo.png"/>
          <span class="num">答错<span>{{wrongNum}}</span> 题</span>
        </span>
        <span class="r-item">
          <img src="/images/unanswered.png"/>
          <span class="num">未答<span>{{unansweredNum}}</span> 题</span>
        </span>
      </div>
      <div class="evaluate-info-div">
        <div class="evaluate-tihao-list">
          <span   :class="[currentIndex==index?'active':'',item.answer2===undefined||item.answer2==='' ||item.answer2.length===0 ?'':'IsDone']" v-for="(item,index) in result.questions" @click="changeIndex(index)">
            {{index+1}}
            <img v-if="currentIndex==index"  src="/images/active.png" />
          </span>
        </div>
        <div class="evaluate-title">
          <!-- <span class="title">{{exam.name}}</span> -->
          <span class="evaluate-num">{{currentIndex+1}}/{{exam.questions.length}}</span>
        </div>
      </div>
      <!-- 单选 -->
      <div v-if="result.questions[currentIndex].questionType==0"  class="evaluate-question-box">
        <span class="desc">{{currentIndex+1}} .【单选题】 {{result.questions[currentIndex].questionStem.Title}}（{{result.questions[currentIndex].score}}分） </span>
        <div v-if="result.questions[currentIndex].questionStem.Title_Imgs!=null" class="imgs">
            <img
                :src="image1"
                :key="'img'+ result.questions[currentIndex].questionBankId + index1"
                v-show="image1!=''"
                v-for="(image1,index1) in result.questions[currentIndex].questionStem.Title_Imgs"
                />
        </div>
        <el-radio-group v-model="result.questions[currentIndex].answer2"  disabled>
            <el-radio style="margin-bottom:20px"   :label="option.Order" v-for="(option,index) in result.questions[currentIndex].questionStem.Options" :key="result.questions[currentIndex].questionBankId + option.Order">
                {{answerOrder[option.Order-1]}}.{{option.Title}}
                <div  v-if="option.Images!=null" class="imgs">
                    <img
                        :src="oimage"
                        :key="'img'+ result.questions[currentIndex].questionBankId + option.Order+ index2"
                        v-show="oimage!=''"
                        v-for="(oimage,index2) in option.Images"
                        />
                </div>
            </el-radio>
        </el-radio-group>

      </div>
      <!-- 多选 -->
      <div v-if="result.questions[currentIndex].questionType==1"  class="evaluate-question-box">
          <span class="desc"> {{currentIndex+1}} .【多选题】 {{result.questions[currentIndex].questionStem.Title}} （{{result.questions[currentIndex].score}}分） </span>
          <div v-if="result.questions[currentIndex].questionStem.Title_Imgs!=null" class="tigan-imgs">
              <img
                  :src="image"
                  :key="'img'+ result.questions[currentIndex].questionBankId + index1"
                  v-show="image!=''"
                  v-for="(image,index1) in result.questions[currentIndex].questionStem.Title_Imgs"
                  />
          </div>
          <el-checkbox-group v-model="result.questions[currentIndex].answer2" disabled >
              <el-checkbox   shape="square" style="margin-bottom:20px"    :label="option.Order" v-for="(option,index) in result.questions[currentIndex].questionStem.Options" :key="result.questions[currentIndex].questionBankId + option.Order">
                  {{answerOrder[option.Order-1]}}.{{option.Title}}
                  <div  v-if="option.Images!=null"  class="imgs">
                      <img
                          :src="oimage"
                          :key="'img'+ result.questions[currentIndex].questionBankId + option.Order+ index2"
                          v-show="oimage!=''"
                          v-for="(oimage,index2) in option.Images"
                          />
                  </div>
              </el-checkbox>
          </el-checkbox-group>
      </div>
      <!-- 判断 -->
      <div v-if="result.questions[currentIndex].questionType==2" class="evaluate-question-box" >
          <span class="desc"> {{currentIndex+1}} .【判断题】 {{result.questions[currentIndex].questionStem.Title}} ({{result.questions[currentIndex].score}}分) </span>
          <div v-if="result.questions[currentIndex].questionStem.Title_Imgs!=null" class="imgs">
              <img
                  :src="image"
                  :key="'img'+ result.questions[currentIndex].questionBankId + index1"
                  v-show="image!=''"
                  v-for="(image,index1) in result.questions[currentIndex].questionStem.Title_Imgs"
                  />
          </div>

          <el-radio-group v-model="result.questions[currentIndex].answer2" direction="horizontal" disabled>
            <el-radio label="正确">A.正确</el-radio>
            <el-radio label="错误">B.错误</el-radio>
          </el-radio-group>
      </div>
      <div class="evaluate-opr">
        <button class="prev" :disabled="currentIndex==0" @click="prev()">上一题</button>
        <div class="fr">
          <button class="btn-submit" @click="restart()">重新考评</button>
          <button class="next" :disabled="exam!=null && currentIndex==result.questions.length-1" @click="next()">下一题</button>
        </div>
      </div>
    </div>
    <el-dialog title="" :visible.sync="IsshowPopup2" class="submitpage" :show-close="false">
      <div class="submit-page-box" v-if="exam!=null">
          <div class="h_title">
            <div class="title">提交试卷</div>
            <a  class="close" @click="IsshowPopup2=false">&times;</a>
          </div>
          <div class="p">共<span>{{exam.questions.length}}</span>题，已答<span>{{answeredNum}}</span>题，未答<span style="color: red;">{{exam.questions.length-answeredNum}}</span>题;</div>
          <div class="p">提交考评后无法再进行答题，您是否确认提交考评。如确认提交考评请输入下方验证码后确认提交考评。</div>
          <div  class="code-box">
            <div class="col-span4">输入验证码</div>
            <div class="col-span4" >
              <input type="text" width="100%" class="codetxt" v-model="code2"/>
            </div>
            <div class="col-span8" >
              <canvas  ref="" id="mycanvas" width="80" height="25" @click="makeCode"></canvas>
              <br>
            </div>
          </div>
        </div>
        <div class="code-opr">
          <button   :disabled="!submitLoading&&code2!=code" @click="submitdata" class="submit_btn">提交考评</button>
          <button   @click="continueAnswer" class="continue_btn">继续答题</button>
        </div>
        <div class="loading"  v-if="submitLoading">
          <img src="/exercise/loading.gif" />
          <div class="txt" >提交中，请稍后...</div>
        </div>
    </el-dialog>
  </div>
</template>
<script >
import { startExams,submitExams,getResult} from "@/api/exam";
export default {
name: 'evaluate',
props:{
  CourseId:'',
  cloudLearn:false,
  canExamAfterLearn:false,
  isLearnPass:false,
  isComplete:false
},
data(){
  return{
      Livename:'',
      answerOrder:['A','B','C','D','E','F','G','H','I','J','K','L','M','N'],
      exam:null,
      result:null,
      currentIndex:0,
      //showExam:false,
      pageIndex:0,
      IsshowPopup2:false,
      answeredNum:0,
      code:'',
      code2:'',
      submitLoading:false,
      timer:null,
      wrongNum:0,
      rightNum:0,
      unansweredNum:0,
      passScore:0
  }
},

mounted(){
  clearInterval(this.timer)
  this.pageIndex = 1
  this.Livename = this.$route.query.name
  this.passScore = this.$route.query.passScore
  this.getExamData(this.$route.query.ExamId)
} ,
destroyed(){
  clearInterval(this.timer)
},
methods:{
  getExamData(id){
    startExams({
      examinationId:id,
    }).then(res => {
      this.exam = res;
          // this.qlength = this.exam.questions.length
          // if (this.exam.replyContent != undefined && this.exam.replyContent != "" && this.exam.replyContent != null)
          //   this.exam.replyContent = JSON.parse(this.exam.replyContent); //之前保存的数据
          if (this.exam.questions.length > 0) {
            var qArr = []
            this.exam.questions.forEach(element => {
              // if (element.questionType == 3) {element.answer = []}
              // if (this.exam.replyContent != null && this.exam.replyContent != "") {
              //   var q = this.exam.replyContent.find(item => item.Q == element.id)
              //   if (q) {
              //     if (element.questionType == 0) element.answer = q.O !== null ? q.O[0] : undefined
              //     if (element.questionType == 1) element.answer = q.O !== null ? q.O : undefined
              //     if (element.questionType == 2) element.answer = q.J !== null ? (q.J === 1 ? "正确" : "错误") : ''
              //     if (element.questionType == 3) {
              //       // for(let i =0;i++;i<q.ba.length){
              //       //   element.answer.push(q.ba[i])
              //       // }
              //       element.answer = q.BA
              //     }
              //     if (element.questionType == 6) element.answer = q.RA!== null ? q.RA:''
              //   }
              // }
              // 题目选项转json
              element.questionStem = JSON.parse(element.questionStem);
              //题干中的图片
              if (element.questionStem.Title_Imgs != null) element.questionStem.Title_Imgs = element.questionStem.Title_Imgs.split(",");

              //选项中的图片
              if (element.questionStem!=undefined &&  element.questionStem.Options!=undefined&& element.questionStem.Options.length > 0) {
                element.questionStem.Options.forEach(item => {
                  if (item.Images != null) item.Images = item.Images.split(",");
                })
              }
              // 多选题
              if(element.questionType == 1) element.answer2 = []
              // 填空题
              // if(element.questionType == 3){
              //  // console.log(elements)
              //   var reg = /_{2,}/g
              //   element.questionStem.Title =  element.questionStem.Title.replaceAll(reg , "____")
              // }
              if(element.questionType==0 || element.questionType==1 || element.questionType==2 ){
                qArr.push(element)
              }
            });
            this.exam.questions = qArr
          }


        // this.exam.questions =  this.GroupByType(this.exam.questions)
        //  console.log(this.exam.questions)
        //  this.exam.questions.forEach(element => {
        //    if (this.exam.isDisorder) { //是否乱序
        //       element.subList.sort(function() {
        //         return Math.random() - 0.5;
        //       });
        //     }
        //  })
        // console.log(this.exam.questions)
        // 分割题目
        //  for (let i = 0; i <this.typeList.length; i++) {
        //   this.sliceData(i)
        //  }

         // console.log(this.$data)

         // this.startTimer() //开启倒计时
        //  this.loading = false
        },
        error => {
          //console.log(error);
          // this.loading=false
          // window.removeEventListener('resize',this.onresize)
          // window.removeEventListener('beforeunload',this.onbeforeunload)
          // document.removeEventListener('visibilitychange', this.onVisibilityChange);
          // clearInterval(this.timer2)
          // clearInterval(this.timer3)
          // setTimeout(()=>{
          //   let returnUrl=this.$route.query.returnUrl
          //   if(returnUrl){
          //     window.open(returnUrl,'_self')
          //     return
          //   }
          //   this.$router.push({ name: "home" });
          // },5000)
          this.$message.info(error.error.message)
          // Notify({ type: 'danger', message: error.error.message+'，5秒后自动返回首页', duration: 5000, });
          // screenfull.exit()
        }
    )
  },
  handleData(submitType){
      // if(submitType==1) {
      //   clearInterval(this.timer2) //正式提交前 清除定时提交定时器 防止数据覆盖
      // }
      let data = {
        examinationId: this.exam.examinationId,
        examinationUserRecordId:  this.exam.examinationUserRecordId,
        submitType: submitType, //提交状态 0：系统每隔几分钟（5-10分钟）自动提交 1：学生答完提交
        replyContent:[],
        replyContentS:'',
        quitCount:this.leaveCount
      }
      // let data_arr = []
      // this.exam.questions.forEach(item=>{
      //   data_arr = data_arr.concat(item.subList)
      // })

      // for (let index = 0; index < this.typeList.length*3;index++) {
      //   //const element = array[index];
      //   data_arr = data_arr.concat(this.$data['list'+  (index+1)] )
      // }

      // console.log(data_arr)
      this.exam.questions.forEach(item=>{
        var itemarr =   {
          Q: item.id, //题目id
          B: item.questionBankId,
          O: null, //选择答案
          J: null, //判断答案
          BA:[], // 填空题答案
          RA:null // 简答题答案
        }
        // 未选择选项时
        if(item.answer2==undefined || item.answer2==''){
          itemarr.O = null
          itemarr.J = null
        }
        else if(item.questionType == 0) itemarr.O = [item.answer2]
        else if(item.questionType == 1) itemarr.O = item.answer2.sort((a,b)=>{ return a-b})
        else if(item.questionType == 2) itemarr.J = item.answer2=="正确"?1:0
        // // 填空题
        // else if(item.questionType == 3){
        //   itemarr.BA = item.answer
        //   // var strList  = item.questionStem.Title.split(/_+/)
        //   // for(let i = 0 ;i<strList.length-1;i++){
        //   //   itemarr.ba.push(item.answer[i])
        //   // }
        // }
        // 简答题
        // else if(item.questionType == 6){
        //   itemarr.RA = item.answer
        // }
        data.replyContent.push(itemarr)
      })
      // data.replyContentS = JSON.stringify(data.replyContent )
      // console.log(data)
      // if(submitType==1) {data.replyContentS=""}
      // if(submitType==0) {data.replyContent=[]}
      // console.log(data)

      submitExams(data).then(()=>{
        if(submitType==1)
        {
          this.$message.success('直播考评提交成功')
          //setTimeout(() => {
            // console.log(2222)
            this.pageIndex = 2
            this.timer = setInterval(()=>{
              this.getResult(this.exam.examinationId)
            },3000)
					//}, 5000)
        }
      },
      error => {
        this.submitLoading = false
        this.$message.info( '操作失败，请稍后重试' )
      })
    },
  prev(){
    this.currentIndex--
  },
  next(){
    this.currentIndex++
  },
  restart(){
    this.pageIndex = 1
    this.currentIndex = 0
    this.wrongNum = 0
    this.rightNum = 0
    this.unansweredNum = 0
    this.getExamData(this.$route.query.ExamId)
  },
  changeIndex(index){
    this.currentIndex = index
  },
  viewResult(){
    this.pageIndex  = 3
    this.code2 = ''
  },
  submitPage(){
    this.IsshowPopup2 = true
    this.getAnsweredNum()
      setTimeout(()=>{
          this.makeCode()
      },500)
  },
  submitdata(){
    if(this.code2!= this.code){ this.$message.info('验证码输入错误，如果看不清,请点击验证码图片换一下')}
    this.submitLoading = true;
    this.handleData(1)
  },
  continueAnswer(){
    this.IsshowPopup2 = false
  },
  getAnsweredNum(){
      // this.answeredNum = this.exam.questions.length
      let arr = this.exam.questions.filter(item => item.answer2===undefined || item.answer2==='' || item.answer2.length==0)
      this.answeredNum = this.exam.questions.length - arr.length
  },
  makeCode(){  //生成验证码
      var num = 4
      this.code = ''
      var canvas = document.getElementById("mycanvas")

      var ctx = canvas.getContext("2d");

      ctx.clearRect(0,0,canvas.width,canvas.height);  //清除画布

      for(let i=0;i<num;i++){
       var f = this.randomNum(0,9)
       ctx.font="18px Georgia";
       ctx.fillText(f,15*i+10,15);
       ctx.fillStyle = this.randomColor(0, 255);
       this.code += f
      }
  },
  randomNum(min, max) {
      return Math.floor(Math.random() * (max - min) + min);
  },
  randomColor(min, max) {
      let r = this.randomNum(min, max);
      let g = this.randomNum(min, max);
      let b = this.randomNum(min, max);
      return "rgb(" + r + "," + g + "," + b + ")";
  },
  GroupByType(items){ // 按照类型分组
      let newArr = [];
      items.forEach((item, i) => {
        let index = -1;
        //some用来查找数组中是否存在某个值
        let isExists = newArr.some((newItem, j) => {
          if (item.questionType == newItem.questionType) {
            index = j;
            return true;
          }
        })
        if (!isExists) {
          newArr.push({
            questionType: item.questionType,
            itemscore:item.score,
            // classGroupText:item.classGroup==null?'未分类':item.classGroup,
            subList: [item]
          })
        } else {
          newArr[index].subList.push(item);
        }

      })
      return newArr
  },
  getResult(id){
      // let that = this
      getResult({
        examId: id,
      }).then(
        res => {
          clearInterval(this.timer)
          this.IsshowPopup2 = false
          this.submitLoading = false
          // that.$forceUpdate()
          this.currentIndex = 0
          this.result = res;
          if(this.result.totalScore>=this.passScore) this.$emit('getInfo')  // 重新获取课程详情
          this.result.replyContent = JSON.parse(this.result.replyContent); //之前保存的数据
          var qArr = []
          this.result.questions.forEach(element => {
            //题目选项转json
            element.questionStem = JSON.parse(element.questionStem);
            //题干中的图片
            if (element.questionStem.Title_Imgs != null)  element.questionStem.Title_Imgs = element.questionStem.Title_Imgs.split(",");
            //选项中的图片
           if(element!=undefined && element.questionStem!=undefined && element.questionStem.Options!=undefined &&element.questionStem.Options.length>0)
            element.questionStem.Options.forEach(item => {
             if(item.Images!=null) item.Images = item.Images.split(",");
            })
            // 填空题
            // if (element.questionType == 3) {
            //     //console.log(element)
            //     var reg = /_{2,}/g
            //     element.questionStem.Title = element.questionStem.Title.replaceAll(reg, "____")
            // }
            //判断题，每题分值，数目，得分
            // element.userScore=0
            // element.hasRightAnswer=true
            // console.log(element)
            if(this.result.replyContent!=null){  //提交记录
              var q =  this.result.replyContent.find(item=>item.Q== element.id)  //answer2 用户选项
               if(q) {
                  if(q.S==undefined){q.S=0}
                  if(element.questionType == 0){ //单选
                    element.answer2 = q.O!==null? q.O[0]:undefined

                  }
                  if(element.questionType == 1) { // 多选
                    element.answer2 = q.O!==null? q.O:undefined

                  }
                  if(element.questionType == 2) { //判断
                    element.answer2 = q.J!==null?(q.J===1?"正确":"错误"):''

                  }

                  // if(element.questionType == 3){ // 填空题
                  //   element.hasRightAnswer=false
                  //   // element.answer3 = q.ba'
                  //   var strList =  element.questionStem.Title.split(/_+/)
                  //   var str = ''
                  //   var str1 = ''
                  //   for(let i = 0;i<strList.length-1;i++){
                  //     // console.log(q)
                  //     str += strList[i]
                  //     str1 = q.BA[i]!=null && q.BA[i]!=undefined?q.BA[i]:''
                  //     str += '<span class="blank_answer">'+ str1 + '</span>'
                  //   }
                  //   if(q.BA.length>0)  element.answer2 = 1 // 题卡的颜色变化
                  //   element.questionStem.Title = str
                  //   this.sumData.blank.count++
                  //   this.sumData.blank.score += q.S
                  // }
                  // if(element.questionType == 6){ // 简答题
                  //   element.hasRightAnswer=false
                  //   element.answer2 = q.RA
                  //   this.sumData.expand.count++
                  //   this.sumData.expand.score += q.S
                  // }
                  element.IsRight= q.R
                  // element.userScore=q.S
               }
               if(element.questionType!= 3 &&  element.questionType != 6) {
                if(element.answer2===undefined||element.answer2===''|| element.answer2===null || element.answer2.length==0) this.unansweredNum++ //未答
                if(element.IsRight ) this.rightNum++ //答题正确
               }
            }
            // else { //未提交结果
            //   if(element.questionType == 3){ // 填空题
            //         element.hasRightAnswer=false
            //         // element.answer3 = q.ba'
            //         var strList =  element.questionStem.Title.split(/_+/)
            //         var str = ''
            //         var str1 = ''
            //         for(let i = 0;i<strList.length-1;i++){
            //           // console.log(q)
            //           str += strList[i]
            //           // str1 = q.BA[i]!=null && q.BA[i]!=undefined?q.BA[i]:''
            //           str += '<span class="blank_answer"></span>'
            //         }
            //         //  if(q.BA.length>0)  element.answer2 = 1 // 题卡的颜色变化
            //         element.questionStem.Title = str
            //       }
            // }
            if(element.questionType==0 || element.questionType==1 || element.questionType==2 ){ // 过滤主观题
                qArr.push(element)
            }
          });

          this.result.questions = qArr

          if(this.result.replyContent==null){ this.unansweredNum =this.result.questions.length }
          this.wrongNum = this.result.questions.length - this.unansweredNum - this.rightNum  // 答错数

          // this.result.questions =  this.GroupByType(this.result.questions)
        },
        error => {
          console.log(error);
        }
      );

    },
  flush(){
      // console.log(this.exam.questions)
      this.$forceUpdate()
     // this.$emit('flushFun')
  },
}
}
</script>

