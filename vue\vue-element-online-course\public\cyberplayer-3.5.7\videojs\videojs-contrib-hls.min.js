!function e(t,i,n){function r(s,o){if(!i[s]){if(!t[s]){var u="function"==typeof require&&require;if(!o&&u)return u(s,!0);if(a)return a(s,!0);var d=new Error("Cannot find module '"+s+"'");throw d.code="MODULE_NOT_FOUND",d}var l=i[s]={exports:{}};t[s][0].call(l.exports,function(e){var i=t[s][1][e];return r(i||e)},l,l.exports,e,t,i,n)}return i[s].exports}for(var a="function"==typeof require&&require,s=0;s<n.length;s++)r(n[s]);return r}({1:[function(e,t,i){"use strict";Object.defineProperty(i,"__esModule",{value:!0});var n=function(){function e(e,t){var i=[],n=!0,r=!1,a=undefined;try{for(var s,o=e[Symbol.iterator]();!(n=(s=o.next()).done)&&(i.push(s.value),!t||i.length!==t);n=!0);}catch(u){r=!0,a=u}finally{try{!n&&o["return"]&&o["return"]()}finally{if(r)throw a}}return i}return function(t,i){if(Array.isArray(t))return t;if(Symbol.iterator in Object(t))return e(t,i);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),r=e(30),a=function(e){return e&&e.__esModule?e:{"default":e}}(r),s=function(e,t){for(var i=e.cues,n=0;n<i.length;n++){var r=i[n];if(t>=r.adStartTime&&t<=r.adEndTime)return r}return null},o=function(e,t){var i=arguments.length<=2||arguments[2]===undefined?0:arguments[2];if(e.segments)for(var r=i,o=undefined,u=0;u<e.segments.length;u++){var d=e.segments[u];if(o||(o=s(t,r+d.duration/2)),o){if("cueIn"in d){o.endTime=r,o.adEndTime=r,r+=d.duration,o=null;continue}if(r<o.endTime){r+=d.duration;continue}o.endTime+=d.duration}else if("cueOut"in d&&(o=new a["default"].VTTCue(r,r+d.duration,d.cueOut),o.adStartTime=r,o.adEndTime=r+parseFloat(d.cueOut),t.addCue(o)),"cueOutCont"in d){var l=undefined,f=undefined,c=d.cueOutCont.split("/").map(parseFloat),h=n(c,2);l=h[0],f=h[1],o=new a["default"].VTTCue(r,r+d.duration,""),o.adStartTime=r-l,o.adEndTime=o.adStartTime+f,t.addCue(o)}r+=d.duration}};i["default"]={updateAdCues:o,findAdCue:s},t.exports=i["default"]},{}],2:[function(e,t,i){"use strict";function n(e){for(var t=[],i=0;i<e.length;i+=4)t.push(e[i]<<24|e[i+1]<<16|e[i+2]<<8|e[i+3]);return t}function r(e){e=u(e,!0);var t=16-e.length%16,i=d(e.length+t);l(e,i);for(var n=e.length;n<i.length;n++)i[n]=t;return i}function a(e){if(e=u(e,!0),e.length<16)throw new Error("PKCS#7 invalid length");var t=e[e.length-1];if(t>16)throw new Error("PKCS#7 padding byte out of range");for(var i=e.length-t,n=0;n<t;n++)if(e[i+n]!==t)throw new Error("PKCS#7 invalid padding byte");var r=d(i);return l(e,r,0,0,i),r}Object.defineProperty(i,"__esModule",{value:!0});var s=function(e){return parseInt(e)===e},o=function(e){if(!s(e.length))return!1;for(var t=0;t<e.length;t++)if(!s(e[t])||e[t]<0||e[t]>255)return!1;return!0},u=function(e,t){if(e.buffer&&ArrayBuffer.isView(e)&&"Uint8Array"===e.name)return t&&(e=e.slice?e.slice():Array.prototype.slice.call(e)),e;if(Array.isArray(e)){if(!o(e))throw new Error("Array contains invalid value: "+e);return new Uint8Array(e)}if(s(e.length)&&o(e))return new Uint8Array(e);throw new Error("unsupported array-like object")},d=function(e){return new Uint8Array(e)},l=function(e,t,i,n,r){null==n&&null==r||(e=e.slice?e.slice(n,r):Array.prototype.slice.call(e,n,r)),t.set(e,i)},f=function(){function e(e){var t=[],i=0;for(e=encodeURI(e);i<e.length;){var n=e.charCodeAt(i++);37===n?(t.push(parseInt(e.substr(i,2),16)),i+=2):t.push(n)}return u(t)}function t(e){for(var t=[],i=0;i<e.length;){var n=e[i];n<128?(t.push(String.fromCharCode(n)),i++):n>191&&n<224?(t.push(String.fromCharCode((31&n)<<6|63&e[i+1])),i+=2):(t.push(String.fromCharCode((15&n)<<12|(63&e[i+1])<<6|63&e[i+2])),i+=3)}return t.join("")}return{toBytes:e,fromBytes:t}}(),c=function(){function e(e){for(var t=[],i=0;i<e.length;i+=2)t.push(parseInt(e.substr(i,2),16));return t}function t(e){for(var t=[],n=0;n<e.length;n++){var r=e[n];t.push(i[(240&r)>>4]+i[15&r])}return t.join("")}var i="0123456789abcdef";return{toBytes:e,fromBytes:t}}(),h={16:10,24:12,32:14
},p=[1,2,4,8,16,32,64,128,27,54,108,216,171,77,154,47,94,188,99,198,151,53,106,212,179,125,250,239,197,145],m=[99,124,119,123,242,107,111,197,48,1,103,43,254,215,171,118,202,130,201,125,250,89,71,240,173,212,162,175,156,164,114,192,183,253,147,38,54,63,247,204,52,165,229,241,113,216,49,21,4,199,35,195,24,150,5,154,7,18,128,226,235,39,178,117,9,131,44,26,27,110,90,160,82,59,214,179,41,227,47,132,83,209,0,237,32,252,177,91,106,203,190,57,74,76,88,207,208,239,170,251,67,77,51,133,69,249,2,127,80,60,159,168,81,163,64,143,146,157,56,245,188,182,218,33,16,255,243,210,205,12,19,236,95,151,68,23,196,167,126,61,100,93,25,115,96,129,79,220,34,42,144,136,70,238,184,20,222,94,11,219,224,50,58,10,73,6,36,92,194,211,172,98,145,149,228,121,231,200,55,109,141,213,78,169,108,86,244,234,101,122,174,8,186,120,37,46,28,166,180,198,232,221,116,31,75,189,139,138,112,62,181,102,72,3,246,14,97,53,87,185,134,193,29,158,225,248,152,17,105,217,142,148,155,30,135,233,206,85,40,223,140,161,137,13,191,230,66,104,65,153,45,15,176,84,187,22],g=[82,9,106,213,48,54,165,56,191,64,163,158,129,243,215,251,124,227,57,130,155,47,255,135,52,142,67,68,196,222,233,203,84,123,148,50,166,194,35,61,238,76,149,11,66,250,195,78,8,46,161,102,40,217,36,178,118,91,162,73,109,139,209,37,114,248,246,100,134,104,152,22,212,164,92,204,93,101,182,146,108,112,72,80,253,237,185,218,94,21,70,87,167,141,157,132,144,216,171,0,140,188,211,10,247,228,88,5,184,179,69,6,208,44,30,143,202,63,15,2,193,175,189,3,1,19,138,107,58,145,17,65,79,103,220,234,151,242,207,206,240,180,230,115,150,172,116,34,231,173,53,133,226,249,55,232,28,117,223,110,71,241,26,113,29,41,197,137,111,183,98,14,170,24,190,27,252,86,62,75,198,210,121,32,154,219,192,254,120,205,90,244,31,221,168,51,136,7,199,49,177,18,16,89,39,128,236,95,96,81,127,169,25,181,74,13,45,229,122,159,147,201,156,239,160,224,59,77,174,42,245,176,200,235,187,60,131,83,153,97,23,43,4,126,186,119,214,38,225,105,20,99,85,33,12,125],y=[3328402341,4168907908,4000806809,4135287693,4294111757,3597364157,3731845041,2445657428,1613770832,33620227,3462883241,1445669757,3892248089,3050821474,1303096294,3967186586,2412431941,528646813,2311702848,4202528135,4026202645,2992200171,2387036105,4226871307,1101901292,3017069671,1604494077,1169141738,597466303,1403299063,3832705686,2613100635,1974974402,3791519004,1033081774,1277568618,1815492186,2118074177,4126668546,2211236943,1748251740,1369810420,3521504564,4193382664,3799085459,2883115123,1647391059,706024767,134480908,2512897874,1176707941,2646852446,806885416,932615841,168101135,798661301,235341577,605164086,461406363,3756188221,3454790438,1311188841,2142417613,3933566367,302582043,495158174,1479289972,874125870,907746093,3698224818,3025820398,1537253627,2756858614,1983593293,3084310113,2108928974,1378429307,3722699582,1580150641,327451799,2790478837,3117535592,0,3253595436,1075847264,3825007647,2041688520,3059440621,3563743934,2378943302,1740553945,1916352843,2487896798,2555137236,2958579944,2244988746,3151024235,3320835882,1336584933,3992714006,2252555205,2588757463,1714631509,293963156,2319795663,3925473552,67240454,4269768577,2689618160,2017213508,631218106,1269344483,2723238387,1571005438,2151694528,93294474,1066570413,563977660,1882732616,4059428100,1673313503,2008463041,2950355573,1109467491,537923632,3858759450,4260623118,3218264685,2177748300,403442708,638784309,3287084079,3193921505,899127202,2286175436,773265209,2479146071,1437050866,4236148354,2050833735,3362022572,3126681063,840505643,3866325909,3227541664,427917720,2655997905,2749160575,1143087718,1412049534,999329963,193497219,2353415882,3354324521,1807268051,672404540,2816401017,3160301282,369822493,2916866934,3688947771,1681011286,1949973070,336202270,2454276571,201721354,1210328172,3093060836,2680341085,3184776046,1135389935,3294782118,965841320,831886756,3554993207,4068047243,3588745010,2345191491,1849112409,3664604599,26054028,2983581028,2622377682,1235855840,3630984372,2891339514,4092916743,3488279077,3395642799,4101667470,1202630377,268961816,1874508501,4034427016,1243948399,1546530418,941366308,1470539505,1941222599,2546386513,3421038627,2715671932,3899946140,1042226977,2521517021,1639824860,227249030,260737669,3765465232,2084453954,1907733956,3429263018,2420656344,100860677,4160157185,470683154,3261161891,1781871967,2924959737,1773779408,394692241,2579611992,974986535,664706745,3655459128,3958962195,731420851,571543859,3530123707,2849626480,126783113,865375399,765172662,1008606754,361203602,3387549984,2278477385,2857719295,1344809080,2782912378,59542671,1503764984,160008576,437062935,1707065306,3622233649,2218934982,3496503480,2185314755,697932208,1512910199,504303377,2075177163,2824099068,1841019862,739644986],_=[2781242211,2230877308,2582542199,2381740923,234877682,3184946027,2984144751,1418839493,1348481072,50462977,2848876391,2102799147,434634494,1656084439,3863849899,2599188086,1167051466,2636087938,1082771913,2281340285,368048890,3954334041,3381544775,201060592,3963727277,1739838676,4250903202,3930435503,3206782108,4149453988,2531553906,1536934080,3262494647,484572669,2923271059,1783375398,1517041206,1098792767,49674231,1334037708,1550332980,4098991525,886171109,150598129,2481090929,1940642008,1398944049,1059722517,201851908,1385547719,1699095331,1587397571,674240536,2704774806,252314885,3039795866,151914247,908333586,2602270848,1038082786,651029483,1766729511,3447698098,2682942837,454166793,2652734339,1951935532,775166490,758520603,3000790638,4004797018,4217086112,4137964114,1299594043,1639438038,3464344499,2068982057,1054729187,1901997871,2534638724,4121318227,1757008337,0,750906861,1614815264,535035132,3363418545,3988151131,3201591914,1183697867,3647454910,1265776953,3734260298,3566750796,3903871064,1250283471,1807470800,717615087,3847203498,384695291,3313910595,3617213773,1432761139,2484176261,3481945413,283769337,100925954,2180939647,4037038160,1148730428,3123027871,3813386408,4087501137,4267549603,3229630528,2315620239,2906624658,3156319645,1215313976,82966005,3747855548,3245848246,1974459098,1665278241,807407632,451280895,251524083,1841287890,1283575245,337120268,891687699,801369324,3787349855,2721421207,3431482436,959321879,1469301956,4065699751,2197585534,1199193405,2898814052,3887750493,724703513,2514908019,2696962144,2551808385,3516813135,2141445340,1715741218,2119445034,2872807568,2198571144,3398190662,700968686,3547052216,1009259540,2041044702,3803995742,487983883,1991105499,1004265696,1449407026,1316239930,504629770,3683797321,168560134,1816667172,3837287516,1570751170,1857934291,4014189740,2797888098,2822345105,2754712981,936633572,2347923833,852879335,1133234376,1500395319,3084545389,2348912013,1689376213,3533459022,3762923945,3034082412,4205598294,133428468,634383082,2949277029,2398386810,3913789102,403703816,3580869306,2297460856,1867130149,1918643758,607656988,4049053350,3346248884,1368901318,600565992,2090982877,2632479860,557719327,3717614411,3697393085,2249034635,2232388234,2430627952,1115438654,3295786421,2865522278,3633334344,84280067,33027830,303828494,2747425121,1600795957,4188952407,3496589753,2434238086,1486471617,658119965,3106381470,953803233,334231800,3005978776,857870609,3151128937,1890179545,2298973838,2805175444,3056442267,574365214,2450884487,550103529,1233637070,4289353045,2018519080,2057691103,2399374476,4166623649,2148108681,387583245,3664101311,836232934,3330556482,3100665960,3280093505,2955516313,2002398509,287182607,3413881008,4238890068,3597515707,975967766],v=[1671808611,2089089148,2006576759,2072901243,4061003762,1807603307,1873927791,3310653893,810573872,16974337,1739181671,729634347,4263110654,3613570519,2883997099,1989864566,3393556426,2191335298,3376449993,2106063485,4195741690,1508618841,1204391495,4027317232,2917941677,3563566036,2734514082,2951366063,2629772188,2767672228,1922491506,3227229120,3082974647,4246528509,2477669779,644500518,911895606,1061256767,4144166391,3427763148,878471220,2784252325,3845444069,4043897329,1905517169,3631459288,827548209,356461077,67897348,3344078279,593839651,3277757891,405286936,2527147926,84871685,2595565466,118033927,305538066,2157648768,3795705826,3945188843,661212711,2999812018,1973414517,152769033,2208177539,745822252,439235610,455947803,1857215598,1525593178,2700827552,1391895634,994932283,3596728278,3016654259,695947817,3812548067,795958831,2224493444,1408607827,3513301457,0,3979133421,543178784,4229948412,2982705585,1542305371,1790891114,3410398667,3201918910,961245753,1256100938,1289001036,1491644504,3477767631,3496721360,4012557807,2867154858,4212583931,1137018435,1305975373,861234739,2241073541,1171229253,4178635257,33948674,2139225727,1357946960,1011120188,2679776671,2833468328,1374921297,2751356323,1086357568,2408187279,2460827538,2646352285,944271416,4110742005,3168756668,3066132406,3665145818,560153121,271589392,4279952895,4077846003,3530407890,3444343245,202643468,322250259,3962553324,1608629855,2543990167,1154254916,389623319,3294073796,2817676711,2122513534,1028094525,1689045092,1575467613,422261273,1939203699,1621147744,2174228865,1339137615,3699352540,577127458,712922154,2427141008,2290289544,1187679302,3995715566,3100863416,339486740,3732514782,1591917662,186455563,3681988059,3762019296,844522546,978220090,169743370,1239126601,101321734,611076132,1558493276,3260915650,3547250131,2901361580,1655096418,2443721105,2510565781,3828863972,2039214713,3878868455,3359869896,928607799,1840765549,2374762893,3580146133,1322425422,2850048425,1823791212,1459268694,4094161908,3928346602,1706019429,2056189050,2934523822,135794696,3134549946,2022240376,628050469,779246638,472135708,2800834470,3032970164,3327236038,3894660072,3715932637,1956440180,522272287,1272813131,3185336765,2340818315,2323976074,1888542832,1044544574,3049550261,1722469478,1222152264,50660867,4127324150,236067854,1638122081,895445557,1475980887,3117443513,2257655686,3243809217,489110045,2662934430,3778599393,4162055160,2561878936,288563729,1773916777,3648039385,2391345038,2493985684,2612407707,505560094,2274497927,3911240169,3460925390,1442818645,678973480,3749357023,2358182796,2717407649,2306869641,219617805,3218761151,3862026214,1120306242,1756942440,1103331905,2578459033,762796589,252780047,2966125488,1425844308,3151392187,372911126],b=[1667474886,2088535288,2004326894,2071694838,4075949567,1802223062,1869591006,3318043793,808472672,16843522,1734846926,724270422,4278065639,3621216949,2880169549,1987484396,3402253711,2189597983,3385409673,2105378810,4210693615,1499065266,1195886990,4042263547,2913856577,3570689971,2728590687,2947541573,2627518243,2762274643,1920112356,3233831835,3082273397,4261223649,2475929149,640051788,909531756,1061110142,4160160501,3435941763,875846760,2779116625,3857003729,4059105529,1903268834,3638064043,825316194,353713962,67374088,3351728789,589522246,3284360861,404236336,2526454071,84217610,2593830191,117901582,303183396,2155911963,3806477791,3958056653,656894286,2998062463,1970642922,151591698,2206440989,741110872,437923380,454765878,1852748508,1515908788,2694904667,1381168804,993742198,3604373943,3014905469,690584402,3823320797,791638366,2223281939,1398011302,3520161977,0,3991743681,538992704,4244381667,2981218425,1532751286,1785380564,3419096717,3200178535,960056178,1246420628,1280103576,1482221744,3486468741,3503319995,4025428677,2863326543,4227536621,1128514950,1296947098,859002214,2240123921,1162203018,4193849577,33687044,2139062782,1347481760,1010582648,2678045221,2829640523,1364325282,2745433693,1077985408,2408548869,2459086143,2644360225,943212656,4126475505,3166494563,3065430391,3671750063,555836226,269496352,4294908645,4092792573,3537006015,3452783745,202118168,320025894,3974901699,1600119230,2543297077,1145359496,387397934,3301201811,2812801621,2122220284,1027426170,1684319432,1566435258,421079858,1936954854,1616945344,2172753945,1330631070,3705438115,572679748,707427924,2425400123,2290647819,1179044492,4008585671,3099120491,336870440,3739122087,1583276732,185277718,3688593069,3772791771,842159716,976899700,168435220,1229577106,101059084,606366792,1549591736,3267517855,3553849021,2897014595,1650632388,2442242105,2509612081,3840161747,2038008818,3890688725,3368567691,926374254,1835907034,2374863873,3587531953,1313788572,2846482505,1819063512,1448540844,4109633523,3941213647,1701162954,2054852340,2930698567,134748176,3132806511,2021165296,623210314,774795868,471606328,2795958615,3031746419,3334885783,3907527627,3722280097,1953799400,522133822,1263263126,3183336545,2341176845,2324333839,1886425312,1044267644,3048588401,1718004428,1212733584,50529542,4143317495,235803164,1633788866,892690282,1465383342,3115962473,2256965911,3250673817,488449850,2661202215,3789633753,4177007595,2560144171,286339874,1768537042,3654906025,2391705863,2492770099,2610673197,505291324,2273808917,3924369609,3469625735,1431699370,673740880,3755965093,2358021891,2711746649,2307489801,218961690,3217021541,3873845719,1111672452,1751693520,1094828930,2576986153,757954394,252645662,2964376443,1414855848,3149649517,370555436],T=[1374988112,2118214995,437757123,975658646,1001089995,530400753,2902087851,1273168787,540080725,2910219766,2295101073,4110568485,1340463100,3307916247,641025152,3043140495,3736164937,632953703,1172967064,1576976609,3274667266,2169303058,2370213795,1809054150,59727847,361929877,3211623147,2505202138,3569255213,1484005843,1239443753,2395588676,1975683434,4102977912,2572697195,666464733,3202437046,4035489047,3374361702,2110667444,1675577880,3843699074,2538681184,1649639237,2976151520,3144396420,4269907996,4178062228,1883793496,2403728665,2497604743,1383856311,2876494627,1917518562,3810496343,1716890410,3001755655,800440835,2261089178,3543599269,807962610,599762354,33778362,3977675356,2328828971,2809771154,4077384432,1315562145,1708848333,101039829,3509871135,3299278474,875451293,2733856160,92987698,2767645557,193195065,1080094634,1584504582,3178106961,1042385657,2531067453,3711829422,1306967366,2438237621,1908694277,67556463,1615861247,429456164,3602770327,2302690252,1742315127,2968011453,126454664,3877198648,2043211483,2709260871,2084704233,4169408201,0,159417987,841739592,504459436,1817866830,4245618683,260388950,1034867998,908933415,168810852,1750902305,2606453969,607530554,202008497,2472011535,3035535058,463180190,2160117071,1641816226,1517767529,470948374,3801332234,3231722213,1008918595,303765277,235474187,4069246893,766945465,337553864,1475418501,2943682380,4003061179,2743034109,4144047775,1551037884,1147550661,1543208500,2336434550,3408119516,3069049960,3102011747,3610369226,1113818384,328671808,2227573024,2236228733,3535486456,2935566865,3341394285,496906059,3702665459,226906860,2009195472,733156972,2842737049,294930682,1206477858,2835123396,2700099354,1451044056,573804783,2269728455,3644379585,2362090238,2564033334,2801107407,2776292904,3669462566,1068351396,742039012,1350078989,1784663195,1417561698,4136440770,2430122216,775550814,2193862645,2673705150,1775276924,1876241833,3475313331,3366754619,270040487,3902563182,3678124923,3441850377,1851332852,3969562369,2203032232,3868552805,2868897406,566021896,4011190502,3135740889,1248802510,3936291284,699432150,832877231,708780849,3332740144,899835584,1951317047,4236429990,3767586992,866637845,4043610186,1106041591,2144161806,395441711,1984812685,1139781709,3433712980,3835036895,2664543715,1282050075,3240894392,1181045119,2640243204,25965917,4203181171,4211818798,3009879386,2463879762,3910161971,1842759443,2597806476,933301370,1509430414,3943906441,3467192302,3076639029,3776767469,2051518780,2631065433,1441952575,404016761,1942435775,1408749034,1610459739,3745345300,2017778566,3400528769,3110650942,941896748,3265478751,371049330,3168937228,675039627,4279080257,967311729,135050206,3635733660,1683407248,2076935265,3576870512,1215061108,3501741890],S=[1347548327,1400783205,3273267108,2520393566,3409685355,4045380933,2880240216,2471224067,1428173050,4138563181,2441661558,636813900,4233094615,3620022987,2149987652,2411029155,1239331162,1730525723,2554718734,3781033664,46346101,310463728,2743944855,3328955385,3875770207,2501218972,3955191162,3667219033,768917123,3545789473,692707433,1150208456,1786102409,2029293177,1805211710,3710368113,3065962831,401639597,1724457132,3028143674,409198410,2196052529,1620529459,1164071807,3769721975,2226875310,486441376,2499348523,1483753576,428819965,2274680428,3075636216,598438867,3799141122,1474502543,711349675,129166120,53458370,2592523643,2782082824,4063242375,2988687269,3120694122,1559041666,730517276,2460449204,4042459122,2706270690,3446004468,3573941694,533804130,2328143614,2637442643,2695033685,839224033,1973745387,957055980,2856345839,106852767,1371368976,4181598602,1033297158,2933734917,1179510461,3046200461,91341917,1862534868,4284502037,605657339,2547432937,3431546947,2003294622,3182487618,2282195339,954669403,3682191598,1201765386,3917234703,3388507166,0,2198438022,1211247597,2887651696,1315723890,4227665663,1443857720,507358933,657861945,1678381017,560487590,3516619604,975451694,2970356327,261314535,3535072918,2652609425,1333838021,2724322336,1767536459,370938394,182621114,3854606378,1128014560,487725847,185469197,2918353863,3106780840,3356761769,2237133081,1286567175,3152976349,4255350624,2683765030,3160175349,3309594171,878443390,1988838185,3704300486,1756818940,1673061617,3403100636,272786309,1075025698,545572369,2105887268,4174560061,296679730,1841768865,1260232239,4091327024,3960309330,3497509347,1814803222,2578018489,4195456072,575138148,3299409036,446754879,3629546796,4011996048,3347532110,3252238545,4270639778,915985419,3483825537,681933534,651868046,2755636671,3828103837,223377554,2607439820,1649704518,3270937875,3901806776,1580087799,4118987695,3198115200,2087309459,2842678573,3016697106,1003007129,2802849917,1860738147,2077965243,164439672,4100872472,32283319,2827177882,1709610350,2125135846,136428751,3874428392,3652904859,3460984630,3572145929,3593056380,2939266226,824852259,818324884,3224740454,930369212,2801566410,2967507152,355706840,1257309336,4148292826,243256656,790073846,2373340630,1296297904,1422699085,3756299780,3818836405,457992840,3099667487,2135319889,77422314,1560382517,1945798516,788204353,1521706781,1385356242,870912086,325965383,2358957921,2050466060,2388260884,2313884476,4006521127,901210569,3990953189,1014646705,1503449823,1062597235,2031621326,3212035895,3931371469,1533017514,350174575,2256028891,2177544179,1052338372,741876788,1606591296,1914052035,213705253,2334669897,1107234197,1899603969,3725069491,2631447780,2422494913,1635502980,1893020342,1950903388,1120974935],w=[2807058932,1699970625,2764249623,1586903591,1808481195,1173430173,1487645946,59984867,4199882800,1844882806,1989249228,1277555970,3623636965,3419915562,1149249077,2744104290,1514790577,459744698,244860394,3235995134,1963115311,4027744588,2544078150,4190530515,1608975247,2627016082,2062270317,1507497298,2200818878,567498868,1764313568,3359936201,2305455554,2037970062,1047239e3,1910319033,1337376481,2904027272,2892417312,984907214,1243112415,830661914,861968209,2135253587,2011214180,2927934315,2686254721,731183368,1750626376,4246310725,1820824798,4172763771,3542330227,48394827,2404901663,2871682645,671593195,3254988725,2073724613,145085239,2280796200,2779915199,1790575107,2187128086,472615631,3029510009,4075877127,3802222185,4107101658,3201631749,1646252340,4270507174,1402811438,1436590835,3778151818,3950355702,3963161475,4020912224,2667994737,273792366,2331590177,104699613,95345982,3175501286,2377486676,1560637892,3564045318,369057872,4213447064,3919042237,1137477952,2658625497,1119727848,2340947849,1530455833,4007360968,172466556,266959938,516552836,0,2256734592,3980931627,1890328081,1917742170,4294704398,945164165,3575528878,958871085,3647212047,2787207260,1423022939,775562294,1739656202,3876557655,2530391278,2443058075,3310321856,547512796,1265195639,437656594,3121275539,719700128,3762502690,387781147,218828297,3350065803,2830708150,2848461854,428169201,122466165,3720081049,1627235199,648017665,4122762354,1002783846,2117360635,695634755,3336358691,4234721005,4049844452,3704280881,2232435299,574624663,287343814,612205898,1039717051,840019705,2708326185,793451934,821288114,1391201670,3822090177,376187827,3113855344,1224348052,1679968233,2361698556,1058709744,752375421,2431590963,1321699145,3519142200,2734591178,188127444,2177869557,3727205754,2384911031,3215212461,2648976442,2450346104,3432737375,1180849278,331544205,3102249176,4150144569,2952102595,2159976285,2474404304,766078933,313773861,2570832044,2108100632,1668212892,3145456443,2013908262,418672217,3070356634,2594734927,1852171925,3867060991,3473416636,3907448597,2614737639,919489135,164948639,2094410160,2997825956,590424639,2486224549,1723872674,3157750862,3399941250,3501252752,3625268135,2555048196,3673637356,1343127501,4130281361,3599595085,2957853679,1297403050,81781910,3051593425,2283490410,532201772,1367295589,3926170974,895287692,1953757831,1093597963,492483431,3528626907,1446242576,1192455638,1636604631,209336225,344873464,1015671571,669961897,3375740769,3857572124,2973530695,3747192018,1933530610,3464042516,935293895,3454686199,2858115069,1863638845,3683022916,4085369519,3292445032,875313188,1080017571,3279033885,621591778,1233856572,2504130317,24197544,3017672716,3835484340,3247465558,2220981195,3060847922,1551124588,1463996600],k=[4104605777,1097159550,396673818,660510266,2875968315,2638606623,4200115116,3808662347,821712160,1986918061,3430322568,38544885,3856137295,718002117,893681702,1654886325,2975484382,3122358053,3926825029,4274053469,796197571,1290801793,1184342925,3556361835,2405426947,2459735317,1836772287,1381620373,3196267988,1948373848,3764988233,3385345166,3263785589,2390325492,1480485785,3111247143,3780097726,2293045232,548169417,3459953789,3746175075,439452389,1362321559,1400849762,1685577905,1806599355,2174754046,137073913,1214797936,1174215055,3731654548,2079897426,1943217067,1258480242,529487843,1437280870,3945269170,3049390895,3313212038,923313619,679998e3,3215307299,57326082,377642221,3474729866,2041877159,133361907,1776460110,3673476453,96392454,878845905,2801699524,777231668,4082475170,2330014213,4142626212,2213296395,1626319424,1906247262,1846563261,562755902,3708173718,1040559837,3871163981,1418573201,3294430577,114585348,1343618912,2566595609,3186202582,1078185097,3651041127,3896688048,2307622919,425408743,3371096953,2081048481,1108339068,2216610296,0,2156299017,736970802,292596766,1517440620,251657213,2235061775,2933202493,758720310,265905162,1554391400,1532285339,908999204,174567692,1474760595,4002861748,2610011675,3234156416,3693126241,2001430874,303699484,2478443234,2687165888,585122620,454499602,151849742,2345119218,3064510765,514443284,4044981591,1963412655,2581445614,2137062819,19308535,1928707164,1715193156,4219352155,1126790795,600235211,3992742070,3841024952,836553431,1669664834,2535604243,3323011204,1243905413,3141400786,4180808110,698445255,2653899549,2989552604,2253581325,3252932727,3004591147,1891211689,2487810577,3915653703,4237083816,4030667424,2100090966,865136418,1229899655,953270745,3399679628,3557504664,4118925222,2061379749,3079546586,2915017791,983426092,2022837584,1607244650,2118541908,2366882550,3635996816,972512814,3283088770,1568718495,3499326569,3576539503,621982671,2895723464,410887952,2623762152,1002142683,645401037,1494807662,2595684844,1335535747,2507040230,4293295786,3167684641,367585007,3885750714,1865862730,2668221674,2960971305,2763173681,1059270954,2777952454,2724642869,1320957812,2194319100,2429595872,2815956275,77089521,3973773121,3444575871,2448830231,1305906550,4021308739,2857194700,2516901860,3518358430,1787304780,740276417,1699839814,1592394909,2352307457,2272556026,188821243,1729977011,3687994002,274084841,3594982253,3613494426,2701949495,4162096729,322734571,2837966542,1640576439,484830689,1202797690,3537852828,4067639125,349075736,3342319475,4157467219,4255800159,1030690015,1155237496,2951971274,1757691577,607398968,2738905026,499347990,3794078908,1011452712,227885567,2818666809,213114376,3034881240,1455525988,3414450555,850817237,1817998408,3092726480],E=[0,235474187,470948374,303765277,941896748,908933415,607530554,708780849,1883793496,2118214995,1817866830,1649639237,1215061108,1181045119,1417561698,1517767529,3767586992,4003061179,4236429990,4069246893,3635733660,3602770327,3299278474,3400528769,2430122216,2664543715,2362090238,2193862645,2835123396,2801107407,3035535058,3135740889,3678124923,3576870512,3341394285,3374361702,3810496343,3977675356,4279080257,4043610186,2876494627,2776292904,3076639029,3110650942,2472011535,2640243204,2403728665,2169303058,1001089995,899835584,666464733,699432150,59727847,226906860,530400753,294930682,1273168787,1172967064,1475418501,1509430414,1942435775,2110667444,1876241833,1641816226,2910219766,2743034109,2976151520,3211623147,2505202138,2606453969,2302690252,2269728455,3711829422,3543599269,3240894392,3475313331,3843699074,3943906441,4178062228,4144047775,1306967366,1139781709,1374988112,1610459739,1975683434,2076935265,1775276924,1742315127,1034867998,866637845,566021896,800440835,92987698,193195065,429456164,395441711,1984812685,2017778566,1784663195,1683407248,1315562145,1080094634,1383856311,1551037884,101039829,135050206,437757123,337553864,1042385657,807962610,573804783,742039012,2531067453,2564033334,2328828971,2227573024,2935566865,2700099354,3001755655,3168937228,3868552805,3902563182,4203181171,4102977912,3736164937,3501741890,3265478751,3433712980,1106041591,1340463100,1576976609,1408749034,2043211483,2009195472,1708848333,1809054150,832877231,1068351396,766945465,599762354,159417987,126454664,361929877,463180190,2709260871,2943682380,3178106961,3009879386,2572697195,2538681184,2236228733,2336434550,3509871135,3745345300,3441850377,3274667266,3910161971,3877198648,4110568485,4211818798,2597806476,2497604743,2261089178,2295101073,2733856160,2902087851,3202437046,2968011453,3936291284,3835036895,4136440770,4169408201,3535486456,3702665459,3467192302,3231722213,2051518780,1951317047,1716890410,1750902305,1113818384,1282050075,1584504582,1350078989,168810852,67556463,371049330,404016761,841739592,1008918595,775550814,540080725,3969562369,3801332234,4035489047,4269907996,3569255213,3669462566,3366754619,3332740144,2631065433,2463879762,2160117071,2395588676,2767645557,2868897406,3102011747,3069049960,202008497,33778362,270040487,504459436,875451293,975658646,675039627,641025152,2084704233,1917518562,1615861247,1851332852,1147550661,1248802510,1484005843,1451044056,933301370,967311729,733156972,632953703,260388950,25965917,328671808,496906059,1206477858,1239443753,1543208500,1441952575,2144161806,1908694277,1675577880,1842759443,3610369226,3644379585,3408119516,3307916247,4011190502,3776767469,4077384432,4245618683,2809771154,2842737049,3144396420,3043140495,2673705150,2438237621,2203032232,2370213795],O=[0,185469197,370938394,487725847,741876788,657861945,975451694,824852259,1483753576,1400783205,1315723890,1164071807,1950903388,2135319889,1649704518,1767536459,2967507152,3152976349,2801566410,2918353863,2631447780,2547432937,2328143614,2177544179,3901806776,3818836405,4270639778,4118987695,3299409036,3483825537,3535072918,3652904859,2077965243,1893020342,1841768865,1724457132,1474502543,1559041666,1107234197,1257309336,598438867,681933534,901210569,1052338372,261314535,77422314,428819965,310463728,3409685355,3224740454,3710368113,3593056380,3875770207,3960309330,4045380933,4195456072,2471224067,2554718734,2237133081,2388260884,3212035895,3028143674,2842678573,2724322336,4138563181,4255350624,3769721975,3955191162,3667219033,3516619604,3431546947,3347532110,2933734917,2782082824,3099667487,3016697106,2196052529,2313884476,2499348523,2683765030,1179510461,1296297904,1347548327,1533017514,1786102409,1635502980,2087309459,2003294622,507358933,355706840,136428751,53458370,839224033,957055980,605657339,790073846,2373340630,2256028891,2607439820,2422494913,2706270690,2856345839,3075636216,3160175349,3573941694,3725069491,3273267108,3356761769,4181598602,4063242375,4011996048,3828103837,1033297158,915985419,730517276,545572369,296679730,446754879,129166120,213705253,1709610350,1860738147,1945798516,2029293177,1239331162,1120974935,1606591296,1422699085,4148292826,4233094615,3781033664,3931371469,3682191598,3497509347,3446004468,3328955385,2939266226,2755636671,3106780840,2988687269,2198438022,2282195339,2501218972,2652609425,1201765386,1286567175,1371368976,1521706781,1805211710,1620529459,2105887268,1988838185,533804130,350174575,164439672,46346101,870912086,954669403,636813900,788204353,2358957921,2274680428,2592523643,2441661558,2695033685,2880240216,3065962831,3182487618,3572145929,3756299780,3270937875,3388507166,4174560061,4091327024,4006521127,3854606378,1014646705,930369212,711349675,560487590,272786309,457992840,106852767,223377554,1678381017,1862534868,1914052035,2031621326,1211247597,1128014560,1580087799,1428173050,32283319,182621114,401639597,486441376,768917123,651868046,1003007129,818324884,1503449823,1385356242,1333838021,1150208456,1973745387,2125135846,1673061617,1756818940,2970356327,3120694122,2802849917,2887651696,2637442643,2520393566,2334669897,2149987652,3917234703,3799141122,4284502037,4100872472,3309594171,3460984630,3545789473,3629546796,2050466060,1899603969,1814803222,1730525723,1443857720,1560382517,1075025698,1260232239,575138148,692707433,878443390,1062597235,243256656,91341917,409198410,325965383,3403100636,3252238545,3704300486,3620022987,3874428392,3990953189,4042459122,4227665663,2460449204,2578018489,2226875310,2411029155,3198115200,3046200461,2827177882,2743944855],A=[0,218828297,437656594,387781147,875313188,958871085,775562294,590424639,1750626376,1699970625,1917742170,2135253587,1551124588,1367295589,1180849278,1265195639,3501252752,3720081049,3399941250,3350065803,3835484340,3919042237,4270507174,4085369519,3102249176,3051593425,2734591178,2952102595,2361698556,2177869557,2530391278,2614737639,3145456443,3060847922,2708326185,2892417312,2404901663,2187128086,2504130317,2555048196,3542330227,3727205754,3375740769,3292445032,3876557655,3926170974,4246310725,4027744588,1808481195,1723872674,1910319033,2094410160,1608975247,1391201670,1173430173,1224348052,59984867,244860394,428169201,344873464,935293895,984907214,766078933,547512796,1844882806,1627235199,2011214180,2062270317,1507497298,1423022939,1137477952,1321699145,95345982,145085239,532201772,313773861,830661914,1015671571,731183368,648017665,3175501286,2957853679,2807058932,2858115069,2305455554,2220981195,2474404304,2658625497,3575528878,3625268135,3473416636,3254988725,3778151818,3963161475,4213447064,4130281361,3599595085,3683022916,3432737375,3247465558,3802222185,4020912224,4172763771,4122762354,3201631749,3017672716,2764249623,2848461854,2331590177,2280796200,2431590963,2648976442,104699613,188127444,472615631,287343814,840019705,1058709744,671593195,621591778,1852171925,1668212892,1953757831,2037970062,1514790577,1463996600,1080017571,1297403050,3673637356,3623636965,3235995134,3454686199,4007360968,3822090177,4107101658,4190530515,2997825956,3215212461,2830708150,2779915199,2256734592,2340947849,2627016082,2443058075,172466556,122466165,273792366,492483431,1047239e3,861968209,612205898,695634755,1646252340,1863638845,2013908262,1963115311,1446242576,1530455833,1277555970,1093597963,1636604631,1820824798,2073724613,1989249228,1436590835,1487645946,1337376481,1119727848,164948639,81781910,331544205,516552836,1039717051,821288114,669961897,719700128,2973530695,3157750862,2871682645,2787207260,2232435299,2283490410,2667994737,2450346104,3647212047,3564045318,3279033885,3464042516,3980931627,3762502690,4150144569,4199882800,3070356634,3121275539,2904027272,2686254721,2200818878,2384911031,2570832044,2486224549,3747192018,3528626907,3310321856,3359936201,3950355702,3867060991,4049844452,4234721005,1739656202,1790575107,2108100632,1890328081,1402811438,1586903591,1233856572,1149249077,266959938,48394827,369057872,418672217,1002783846,919489135,567498868,752375421,209336225,24197544,376187827,459744698,945164165,895287692,574624663,793451934,1679968233,1764313568,2117360635,1933530610,1343127501,1560637892,1243112415,1192455638,3704280881,3519142200,3336358691,3419915562,3907448597,3857572124,4075877127,4294704398,3029510009,3113855344,2927934315,2744104290,2159976285,2377486676,2594734927,2544078150],L=[0,151849742,303699484,454499602,607398968,758720310,908999204,1059270954,1214797936,1097159550,1517440620,1400849762,1817998408,1699839814,2118541908,2001430874,2429595872,2581445614,2194319100,2345119218,3034881240,3186202582,2801699524,2951971274,3635996816,3518358430,3399679628,3283088770,4237083816,4118925222,4002861748,3885750714,1002142683,850817237,698445255,548169417,529487843,377642221,227885567,77089521,1943217067,2061379749,1640576439,1757691577,1474760595,1592394909,1174215055,1290801793,2875968315,2724642869,3111247143,2960971305,2405426947,2253581325,2638606623,2487810577,3808662347,3926825029,4044981591,4162096729,3342319475,3459953789,3576539503,3693126241,1986918061,2137062819,1685577905,1836772287,1381620373,1532285339,1078185097,1229899655,1040559837,923313619,740276417,621982671,439452389,322734571,137073913,19308535,3871163981,4021308739,4104605777,4255800159,3263785589,3414450555,3499326569,3651041127,2933202493,2815956275,3167684641,3049390895,2330014213,2213296395,2566595609,2448830231,1305906550,1155237496,1607244650,1455525988,1776460110,1626319424,2079897426,1928707164,96392454,213114376,396673818,514443284,562755902,679998e3,865136418,983426092,3708173718,3557504664,3474729866,3323011204,4180808110,4030667424,3945269170,3794078908,2507040230,2623762152,2272556026,2390325492,2975484382,3092726480,2738905026,2857194700,3973773121,3856137295,4274053469,4157467219,3371096953,3252932727,3673476453,3556361835,2763173681,2915017791,3064510765,3215307299,2156299017,2307622919,2459735317,2610011675,2081048481,1963412655,1846563261,1729977011,1480485785,1362321559,1243905413,1126790795,878845905,1030690015,645401037,796197571,274084841,425408743,38544885,188821243,3613494426,3731654548,3313212038,3430322568,4082475170,4200115116,3780097726,3896688048,2668221674,2516901860,2366882550,2216610296,3141400786,2989552604,2837966542,2687165888,1202797690,1320957812,1437280870,1554391400,1669664834,1787304780,1906247262,2022837584,265905162,114585348,499347990,349075736,736970802,585122620,972512814,821712160,2595684844,2478443234,2293045232,2174754046,3196267988,3079546586,2895723464,2777952454,3537852828,3687994002,3234156416,3385345166,4142626212,4293295786,3841024952,3992742070,174567692,57326082,410887952,292596766,777231668,660510266,1011452712,893681702,1108339068,1258480242,1343618912,1494807662,1715193156,1865862730,1948373848,2100090966,2701949495,2818666809,3004591147,3122358053,2235061775,2352307457,2535604243,2653899549,3915653703,3764988233,4219352155,4067639125,3444575871,3294430577,3746175075,3594982253,836553431,953270745,600235211,718002117,367585007,484830689,133361907,251657213,2041877159,1891211689,1806599355,1654886325,1568718495,1418573201,1335535747,1184342925],P=function x(e){
if(!(this instanceof x))throw Error("AES must be instanitated with `new`");Object.defineProperty(this,"key",{value:u(e,!0)}),this._prepare()};P.prototype._prepare=function(){var e=h[this.key.length];if(null==e)throw new Error("invalid key size (must be 16, 24 or 32 bytes)");this._Ke=[],this._Kd=[];for(var t=0;t<=e;t++)this._Ke.push([0,0,0,0]),this._Kd.push([0,0,0,0]);for(var i=4*(e+1),r=this.key.length/4,a=n(this.key),s=undefined,t=0;t<r;t++)s=t>>2,this._Ke[s][t%4]=a[t],this._Kd[e-s][t%4]=a[t];for(var o=0,u=r,d=undefined;u<i;){if(d=a[r-1],a[0]^=m[d>>16&255]<<24^m[d>>8&255]<<16^m[255&d]<<8^m[d>>24&255]^p[o]<<24,o+=1,8!=r)for(var l=1;l<r;l++)a[l]^=a[l-1];else{for(var f=1;f<r/2;f++)a[f]^=a[f-1];d=a[r/2-1],a[r/2]^=m[255&d]^m[d>>8&255]<<8^m[d>>16&255]<<16^m[d>>24&255]<<24;for(var c=r/2+1;c<r;c++)a[c]^=a[c-1]}for(var t=0,g=undefined,y=undefined;t<r&&u<i;)g=u>>2,y=u%4,this._Ke[g][y]=a[t],this._Kd[e-g][y]=a[t++],u++}for(var g=1;g<e;g++)for(var y=0;y<4;y++)d=this._Kd[g][y],this._Kd[g][y]=E[d>>24&255]^O[d>>16&255]^A[d>>8&255]^L[255&d]},P.prototype.encrypt=function(e){if(16!=e.length)throw new Error("invalid plaintext size (must be 16 bytes)");for(var t=this._Ke.length-1,i=[0,0,0,0],r=n(e),a=0;a<4;a++)r[a]^=this._Ke[0][a];for(var s=1;s<t;s++){for(var a=0;a<4;a++)i[a]=y[r[a]>>24&255]^_[r[(a+1)%4]>>16&255]^v[r[(a+2)%4]>>8&255]^b[255&r[(a+3)%4]]^this._Ke[s][a];r=i.slice()}for(var o=d(16),u=undefined,a=0;a<4;a++)u=this._Ke[t][a],o[4*a]=255&(m[r[a]>>24&255]^u>>24),o[4*a+1]=255&(m[r[(a+1)%4]>>16&255]^u>>16),o[4*a+2]=255&(m[r[(a+2)%4]>>8&255]^u>>8),o[4*a+3]=255&(m[255&r[(a+3)%4]]^u);return o},P.prototype.decrypt=function(e){if(16!=e.length)throw new Error("invalid ciphertext size (must be 16 bytes)");for(var t=this._Kd.length-1,i=[0,0,0,0],r=n(e),a=0;a<4;a++)r[a]^=this._Kd[0][a];for(var s=1;s<t;s++){for(var a=0;a<4;a++)i[a]=T[r[a]>>24&255]^S[r[(a+3)%4]>>16&255]^w[r[(a+2)%4]>>8&255]^k[255&r[(a+1)%4]]^this._Kd[s][a];r=i.slice()}for(var o=d(16),u=undefined,a=0;a<4;a++)u=this._Kd[t][a],o[4*a]=255&(g[r[a]>>24&255]^u>>24),o[4*a+1]=255&(g[r[(a+3)%4]>>16&255]^u>>16),o[4*a+2]=255&(g[r[(a+2)%4]>>8&255]^u>>8),o[4*a+3]=255&(g[255&r[(a+1)%4]]^u);return o};var I=function B(e){if(!(this instanceof B))throw Error("AES must be instanitated with `new`");this.description="Electronic Code Block",this.name="ecb",this._aes=new P(e)};I.prototype.encrypt=function(e){if(e=u(e),e.length%16!=0)throw new Error("invalid plaintext size (must be multiple of 16 bytes)");for(var t=d(e.length),i=d(16),n=0;n<e.length;n+=16)l(e,i,0,n,n+16),i=this._aes.encrypt(i),l(i,t,n);return t},I.prototype.decrypt=function(e){if(e=u(e),e.length%16!=0)throw new Error("invalid ciphertext size (must be multiple of 16 bytes)");for(var t=d(e.length),i=d(16),n=0;n<e.length;n+=16)l(e,i,0,n,n+16),i=this._aes.decrypt(i),l(i,t,n);return t};var C=function j(e,t){if(!(this instanceof j))throw Error("AES must be instanitated with `new`");if(this.description="Cipher Block Chaining",this.name="cbc",t){if(16!=t.length)throw new Error("invalid initialation vector size (must be 16 bytes)")}else t=d(16);this._lastCipherblock=u(t,!0),this._aes=new P(e)};C.prototype.encrypt=function(e){if(e=u(e),e.length%16!=0)throw new Error("invalid plaintext size (must be multiple of 16 bytes)");for(var t=d(e.length),i=d(16),n=0;n<e.length;n+=16){l(e,i,0,n,n+16);for(var r=0;r<16;r++)i[r]^=this._lastCipherblock[r];this._lastCipherblock=this._aes.encrypt(i),l(this._lastCipherblock,t,n)}return t},C.prototype.decrypt=function(e){if(e=u(e),e.length%16!=0)throw new Error("invalid ciphertext size (must be multiple of 16 bytes)");for(var t=d(e.length),i=d(16),n=0;n<e.length;n+=16){l(e,i,0,n,n+16),i=this._aes.decrypt(i);for(var r=0;r<16;r++)t[n+r]=i[r]^this._lastCipherblock[r];l(e,this._lastCipherblock,0,n,n+16)}return t};var U=function N(e,t,i){if(!(this instanceof N))throw Error("AES must be instanitated with `new`");if(this.description="Cipher Feedback",this.name="cfb",t){if(16!=t.length)throw new Error("invalid initialation vector size (must be 16 size)")}else t=d(16);i||(i=1),this.segmentSize=i,this._shiftRegister=u(t,!0),this._aes=new P(e)};U.prototype.encrypt=function(e){if(e.length%this.segmentSize!=0)throw new Error("invalid plaintext size (must be segmentSize bytes)");for(var t=u(e,!0),i=undefined,n=0;n<t.length;n+=this.segmentSize){i=this._aes.encrypt(this._shiftRegister);for(var r=0;r<this.segmentSize;r++)t[n+r]^=i[r];l(this._shiftRegister,this._shiftRegister,0,this.segmentSize),l(t,this._shiftRegister,16-this.segmentSize,n,n+this.segmentSize)}return t},U.prototype.decrypt=function(e){if(e.length%this.segmentSize!=0)throw new Error("invalid ciphertext size (must be segmentSize bytes)");for(var t=u(e,!0),i=undefined,n=0;n<t.length;n+=this.segmentSize){i=this._aes.encrypt(this._shiftRegister);for(var r=0;r<this.segmentSize;r++)t[n+r]^=i[r];l(this._shiftRegister,this._shiftRegister,0,this.segmentSize),l(e,this._shiftRegister,16-this.segmentSize,n,n+this.segmentSize)}return t};var M=function F(e,t){if(!(this instanceof F))throw Error("AES must be instanitated with `new`");if(this.description="Output Feedback",this.name="ofb",t){if(16!=t.length)throw new Error("invalid initialation vector size (must be 16 bytes)")}else t=d(16);this._lastPrecipher=u(t,!0),this._lastPrecipherIndex=16,this._aes=new P(e)};M.prototype.encrypt=function(e){for(var t=u(e,!0),i=0;i<t.length;i++)16===this._lastPrecipherIndex&&(this._lastPrecipher=this._aes.encrypt(this._lastPrecipher),this._lastPrecipherIndex=0),t[i]^=this._lastPrecipher[this._lastPrecipherIndex++];return t},M.prototype.decrypt=M.prototype.encrypt;var D=function q(e){if(!(this instanceof q))throw Error("Counter must be instanitated with `new`");0===e||e||(e=1),"number"==typeof e?(this._counter=d(16),this.setValue(e)):this.setBytes(e)};D.prototype.setValue=function(e){if("number"!=typeof e||parseInt(e)!=e)throw new Error("invalid counter value (must be an integer)");if(e>Number.MAX_SAFE_INTEGER)throw new Error("integer value out of safe range");for(var t=15;t>=0;--t)this._counter[t]=e%256,e=parseInt(e/256)},D.prototype.setBytes=function(e){if(e=u(e,!0),16!=e.length)throw new Error("invalid counter bytes size (must be 16 bytes)");this._counter=e},D.prototype.increment=function(){for(var e=15;e>=0;e--){if(255!==this._counter[e]){this._counter[e]++;break}this._counter[e]=0}};var R=function G(e,t){if(!(this instanceof G))throw Error("AES must be instanitated with `new`");this.description="Counter",this.name="ctr",t instanceof D||(t=new D(t)),this._counter=t,this._remainingCounter=null,this._remainingCounterIndex=16,this._aes=new P(e)};R.prototype.encrypt=function(e){for(var t=u(e,!0),i=0;i<t.length;i++)16===this._remainingCounterIndex&&(this._remainingCounter=this._aes.encrypt(this._counter._counter),this._remainingCounterIndex=0,this._counter.increment()),t[i]^=this._remainingCounter[this._remainingCounterIndex++];return t},R.prototype.decrypt=R.prototype.encrypt,i["default"]={AES:P,Counter:D,ModeOfOperation:{ecb:I,cbc:C,cfb:U,ofb:M,ctr:R},utils:{hex:c,utf8:f},padding:{pkcs7:{pad:r,strip:a}},_arrayTest:{coerceArray:u,createArray:d,copyArray:l}},t.exports=i["default"]},{}],3:[function(e,t,i){"use strict";Object.defineProperty(i,"__esModule",{value:!0});var n=function(e,t){return e.start(t)+"-"+e.end(t)},r=function(e,t){var i=e.toString(16);return"00".substring(0,2-i.length)+i+(t%2?" ":"")},a=function(e){return e>=32&&e<126?String.fromCharCode(e):"."},s=function(e){var t={};return Object.keys(e).forEach(function(i){var n=e[i];ArrayBuffer.isView(n)?t[i]={bytes:n.buffer,byteOffset:n.byteOffset,byteLength:n.byteLength}:t[i]=n}),t},o=function(e){var t=e.byterange||{length:Infinity,offset:0};return[t.length,t.offset,e.resolvedUri].join(",")},u={hexDump:function(e){for(var t=Array.prototype.slice.call(e),i="",n=undefined,s=undefined,o=0;o<t.length/16;o++)n=t.slice(16*o,16*o+16).map(r).join(""),s=t.slice(16*o,16*o+16).map(a).join(""),i+=n+" "+s+"\n";return i},tagDump:function(e){return u.hexDump(e.bytes)},textRanges:function(e){var t="",i=undefined;for(i=0;i<e.length;i++)t+=n(e,i)+" ";return t},createTransferableMessage:s,initSegmentId:o};i["default"]=u,t.exports=i["default"]},{}],4:[function(e,t,i){"use strict";Object.defineProperty(i,"__esModule",{value:!0}),i["default"]={GOAL_BUFFER_LENGTH:30,MAX_GOAL_BUFFER_LENGTH:60,GOAL_BUFFER_LENGTH_RATE:1,BANDWIDTH_VARIANCE:1.2,BUFFER_LOW_WATER_LINE:0,MAX_BUFFER_LOW_WATER_LINE:30,BUFFER_LOW_WATER_LINE_RATE:1},t.exports=i["default"]},{}],5:[function(e,t,i){"use strict";Object.defineProperty(i,"__esModule",{value:!0});var n=e(30),r=function(e){return e&&e.__esModule?e:{"default":e}}(n),a=e(26),s=e(3),o=function(e){e.onmessage=function(e){var t=e.data,i=new Uint8Array(t.encrypted.bytes,t.encrypted.byteOffset,t.encrypted.byteLength),n=new Uint32Array(t.key.bytes,t.key.byteOffset,t.key.byteLength/4),o=new Uint32Array(t.iv.bytes,t.iv.byteOffset,t.iv.byteLength/4);new a.Decrypter(i,n,o,function(e,i){r["default"].postMessage((0,s.createTransferableMessage)({source:t.source,decrypted:i}),[i.buffer])})}};i["default"]=function(e){return new o(e)},t.exports=i["default"]},{}],6:[function(e,t,i){(function(t){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function a(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}Object.defineProperty(i,"__esModule",{value:!0});var s=function(){function e(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,i,n){return i&&e(t.prototype,i),n&&e(t,n),t}}(),o=function(e,t,i){for(var n=!0;n;){var r=e,a=t,s=i;n=!1,null===r&&(r=Function.prototype);var o=Object.getOwnPropertyDescriptor(r,a);if(o!==undefined){if("value"in o)return o.value;var u=o.get;return u===undefined?undefined:u.call(s)}var d=Object.getPrototypeOf(r);if(null===d)return undefined;e=d,t=a,i=s,n=!0,o=d=undefined}},u=e(10),d=n(u),l=e(12),f=e(17),c=n(f),h=e(21),p=n(h),m=e(13),g=n(m),y="undefined"!=typeof window?window.videojs:void 0!==t?t.videojs:null,_=n(y),v=e(1),b=n(v),T=e(19),S=n(T),w=e(66),k=e(77),E=n(k),O=e(5),A=n(O),L=e(4),P=n(L),I=e(20),C=e(7),U=undefined,M={videoCodec:"avc1",videoObjectTypeIndicator:".4d400d",audioProfile:"2"},D=["mediaRequests","mediaRequestsAborted","mediaRequestsTimedout","mediaRequestsErrored","mediaTransferDuration","mediaBytesTransferred"],R=function(e){return this.audioSegmentLoader_[e]+this.mainSegmentLoader_[e]},x=function(){var t=undefined;try{t=e.resolve("./decrypter-worker")}catch(i){}return t},B=function(e){return e.replace(/avc1\.(\d+)\.(\d+)/i,function(e){return(0,w.translateLegacyCodecs)([e])[0]})};i.mapLegacyAvcCodecs_=B;var j=function(e,t,i){return e+"/"+t+'; codecs="'+i.filter(function(e){return!!e}).join(", ")+'"'},N=function(e){return e.segments&&e.segments.length&&e.segments[0].map?"mp4":"mp2t"},F=function(e){var t=e.attributes||{};return t.CODECS?(0,I.parseCodecs)(t.CODECS):M},q=function(e,t){var i=N(t),n=F(t),r=t.attributes||{},a=!0,s=!1;if(!t)return[];if(e.mediaGroups.AUDIO&&r.AUDIO){var o=e.mediaGroups.AUDIO[r.AUDIO];if(o){s=!0,a=!1;for(var u in o)if(!o[u].uri){a=!0;break}}}s&&!n.audioProfile&&(_["default"].log.warn("Multiple audio tracks present but no audio codec string is specified. Attempting to use the default audio codec (mp4a.40.2)"),n.audioProfile=M.audioProfile);var d={};n.videoCodec&&(d.video=""+n.videoCodec+n.videoObjectTypeIndicator),n.audioProfile&&(d.audio="mp4a.40."+n.audioProfile);var l=j("audio",i,[d.audio]),f=j("video",i,[d.video]),c=j("video",i,[d.video,d.audio]);return s?!a&&d.video?[f,l]:[c,l]:d.video?[c]:[l]};i.mimeTypesForPlaylist_=q;var G=function(e){function t(e){var i=this;r(this,t),o(Object.getPrototypeOf(t.prototype),"constructor",this).call(this);var n=e.url,a=e.handleManifestRedirects,s=e.withCredentials,u=e.mode,l=e.tech,f=e.bandwidth,h=e.externHls,m=e.useCueTags,g=e.blacklistDuration,y=e.enableLowInitialPlaylist;if(!n)throw new Error("A non-empty playlist URL is required");U=h,this.tech_=l,this.hls_=l.hls,this.mode_=u,this.useCueTags_=m,this.blacklistDuration=g,this.enableLowInitialPlaylist=y,this.useCueTags_&&(this.cueTagsTrack_=this.tech_.addTextTrack("metadata","ad-cues"),this.cueTagsTrack_.inBandMetadataTrackDispatchType=""),this.requestOptions_={withCredentials:s,handleManifestRedirects:a,timeout:null},this.mediaTypes_=(0,C.createMediaTypes)(),this.mediaSource=new _["default"].MediaSource({mode:u}),this.mediaSource.addEventListener("sourceopen",this.handleSourceOpen_.bind(this)),this.seekable_=_["default"].createTimeRanges(),this.hasPlayed_=function(){return!1},this.syncController_=new S["default"](e),this.segmentMetadataTrack_=l.addRemoteTextTrack({kind:"metadata",label:"segment-metadata"},!1).track,this.decrypter_=(0,E["default"])(A["default"],x());var v={hls:this.hls_,mediaSource:this.mediaSource,currentTime:this.tech_.currentTime.bind(this.tech_),seekable:function(){return i.seekable()},seeking:function(){return i.tech_.seeking()},duration:function(){return i.mediaSource.duration},hasPlayed:function(){return i.hasPlayed_()},goalBufferLength:function(){return i.goalBufferLength()},bandwidth:f,syncController:this.syncController_,decrypter:this.decrypter_};this.masterPlaylistLoader_=new d["default"](n,this.hls_,this.requestOptions_),this.setupMasterPlaylistLoaderListeners_(),this.mainSegmentLoader_=new c["default"](_["default"].mergeOptions(v,{segmentMetadataTrack:this.segmentMetadataTrack_,loaderType:"main"}),e),this.audioSegmentLoader_=new c["default"](_["default"].mergeOptions(v,{loaderType:"audio"}),e),this.subtitleSegmentLoader_=new p["default"](_["default"].mergeOptions(v,{loaderType:"vtt"}),e),this.setupSegmentLoaderListeners_(),D.forEach(function(e){i[e+"_"]=R.bind(i,e)}),this.masterPlaylistLoader_.load()}return a(t,e),s(t,[{key:"setupMasterPlaylistLoaderListeners_",value:function(){var e=this;this.masterPlaylistLoader_.on("loadedmetadata",function(){var t=e.masterPlaylistLoader_.media(),i=1.5*e.masterPlaylistLoader_.targetDuration*1e3;(0,l.isLowestEnabledRendition)(e.masterPlaylistLoader_.master,e.masterPlaylistLoader_.media())?e.requestOptions_.timeout=0:e.requestOptions_.timeout=i,t.endList&&"none"!==e.tech_.preload()&&(e.mainSegmentLoader_.playlist(t,e.requestOptions_),e.mainSegmentLoader_.load()),(0,C.setupMediaGroups)({segmentLoaders:{AUDIO:e.audioSegmentLoader_,SUBTITLES:e.subtitleSegmentLoader_,main:e.mainSegmentLoader_},tech:e.tech_,requestOptions:e.requestOptions_,masterPlaylistLoader:e.masterPlaylistLoader_,mode:e.mode_,hls:e.hls_,master:e.master(),mediaTypes:e.mediaTypes_,blacklistCurrentPlaylist:e.blacklistCurrentPlaylist.bind(e)}),e.triggerPresenceUsage_(e.master(),t);try{e.setupSourceBuffers_()}catch(n){return _["default"].log.warn("Failed to create SourceBuffers",n),e.mediaSource.endOfStream("decode")}e.setupFirstPlay(),e.trigger("selectedinitialmedia")}),this.masterPlaylistLoader_.on("loadedplaylist",function(){var t=e.masterPlaylistLoader_.media();if(!t){var i=undefined;return e.enableLowInitialPlaylist&&(i=e.selectInitialPlaylist()),i||(i=e.selectPlaylist()),e.initialMedia_=i,void e.masterPlaylistLoader_.media(e.initialMedia_)}e.useCueTags_&&e.updateAdCues_(t),e.mainSegmentLoader_.playlist(t,e.requestOptions_),e.updateDuration(),e.tech_.paused()||e.mainSegmentLoader_.load(),t.endList||function(){var t=function(){var t=e.seekable();0!==t.length&&e.mediaSource.addSeekableRange_(t.start(0),t.end(0))};e.duration()!==Infinity?function(){var i=function n(){e.duration()===Infinity?t():e.tech_.one("durationchange",n)};e.tech_.one("durationchange",i)}():t()}()}),this.masterPlaylistLoader_.on("error",function(){e.blacklistCurrentPlaylist(e.masterPlaylistLoader_.error)}),this.masterPlaylistLoader_.on("mediachanging",function(){e.mainSegmentLoader_.abort(),e.mainSegmentLoader_.pause()}),this.masterPlaylistLoader_.on("mediachange",function(){var t=e.masterPlaylistLoader_.media(),i=1.5*e.masterPlaylistLoader_.targetDuration*1e3;(0,l.isLowestEnabledRendition)(e.masterPlaylistLoader_.master,e.masterPlaylistLoader_.media())?e.requestOptions_.timeout=0:e.requestOptions_.timeout=i,e.mainSegmentLoader_.playlist(t,e.requestOptions_),e.mainSegmentLoader_.load(),e.tech_.trigger({type:"mediachange",bubbles:!0})}),this.masterPlaylistLoader_.on("playlistunchanged",function(){var t=e.masterPlaylistLoader_.media();e.stuckAtPlaylistEnd_(t)&&(e.blacklistCurrentPlaylist({message:"Playlist no longer updating."}),e.tech_.trigger("playliststuck"))}),this.masterPlaylistLoader_.on("renditiondisabled",function(){e.tech_.trigger({type:"usage",name:"hls-rendition-disabled"})}),this.masterPlaylistLoader_.on("renditionenabled",function(){e.tech_.trigger({type:"usage",name:"hls-rendition-enabled"})})}},{key:"triggerPresenceUsage_",value:function(e,t){var i=e.mediaGroups||{},n=!0,r=Object.keys(i.AUDIO);for(var a in i.AUDIO)for(var s in i.AUDIO[a]){var o=i.AUDIO[a][s];o.uri||(n=!1)}n&&this.tech_.trigger({type:"usage",name:"hls-demuxed"}),Object.keys(i.SUBTITLES).length&&this.tech_.trigger({type:"usage",name:"hls-webvtt"}),U.Playlist.isAes(t)&&this.tech_.trigger({type:"usage",name:"hls-aes"}),U.Playlist.isFmp4(t)&&this.tech_.trigger({type:"usage",name:"hls-fmp4"}),r.length&&Object.keys(i.AUDIO[r[0]]).length>1&&this.tech_.trigger({type:"usage",name:"hls-alternate-audio"}),this.useCueTags_&&this.tech_.trigger({type:"usage",name:"hls-playlist-cue-tags"})}},{key:"setupSegmentLoaderListeners_",value:function(){var e=this;this.mainSegmentLoader_.on("bandwidthupdate",function(){var t=e.selectPlaylist(),i=e.masterPlaylistLoader_.media(),n=e.tech_.buffered(),r=n.length?n.end(n.length-1)-e.tech_.currentTime():0,a=e.bufferLowWaterLine();(!i.endList||e.duration()<P["default"].MAX_BUFFER_LOW_WATER_LINE||t.attributes.BANDWIDTH<i.attributes.BANDWIDTH||r>=a)&&e.masterPlaylistLoader_.media(t),e.tech_.trigger("bandwidthupdate")}),this.mainSegmentLoader_.on("progress",function(){e.trigger("progress")}),this.mainSegmentLoader_.on("error",function(){e.blacklistCurrentPlaylist(e.mainSegmentLoader_.error())}),this.mainSegmentLoader_.on("syncinfoupdate",function(){e.onSyncInfoUpdate_()}),this.mainSegmentLoader_.on("timestampoffset",function(){e.tech_.trigger({type:"usage",name:"hls-timestamp-offset"})}),this.audioSegmentLoader_.on("syncinfoupdate",function(){e.onSyncInfoUpdate_()}),this.mainSegmentLoader_.on("ended",function(){e.onEndOfStream()}),this.mainSegmentLoader_.on("alive",function(){var t=e.masterPlaylistLoader_.media();t&&!t.endList&&e.trigger("alive")}),this.mainSegmentLoader_.on("noLiveStream",function(){var t=e.masterPlaylistLoader_.media();t&&!t.endList&&e.trigger("noLiveStream")}),this.mainSegmentLoader_.on("earlyabort",function(){e.blacklistCurrentPlaylist({message:"Aborted early because there isn't enough bandwidth to complete the request without rebuffering."},120)}),this.mainSegmentLoader_.on("reseteverything",function(){e.tech_.trigger("hls-reset")}),this.mainSegmentLoader_.on("segmenttimemapping",function(t){e.tech_.trigger({type:"hls-segment-time-mapping",mapping:t.mapping})}),this.audioSegmentLoader_.on("ended",function(){e.onEndOfStream()})}},{key:"mediaSecondsLoaded_",value:function(){return Math.max(this.audioSegmentLoader_.mediaSecondsLoaded+this.mainSegmentLoader_.mediaSecondsLoaded)}},{key:"load",value:function(){this.mainSegmentLoader_.load(),this.mediaTypes_.AUDIO.activePlaylistLoader&&this.audioSegmentLoader_.load(),this.mediaTypes_.SUBTITLES.activePlaylistLoader&&this.subtitleSegmentLoader_.load()}},{key:"fastQualityChange_",value:function(){var e=this.selectPlaylist();e!==this.masterPlaylistLoader_.media()&&(this.masterPlaylistLoader_.media(e),this.mainSegmentLoader_.resetLoader())}},{key:"play",value:function(){if(!this.setupFirstPlay()){this.tech_.ended()&&this.tech_.setCurrentTime(0),this.hasPlayed_()&&this.load();var e=this.tech_.seekable();return this.tech_.duration()===Infinity&&this.tech_.currentTime()<e.start(0)?this.tech_.setCurrentTime(e.end(e.length-1)):void 0}}},{key:"setupFirstPlay",value:function(){var e=this,t=this.masterPlaylistLoader_.media();if(!t||this.tech_.paused()||this.hasPlayed_())return!1;if(!t.endList){var i=function(){var t=e.seekable();return t.length?_["default"].browser.IE_VERSION&&"html5"===e.mode_&&0===e.tech_.readyState()?(e.tech_.one("loadedmetadata",function(){e.trigger("firstplay"),e.tech_.setCurrentTime(t.end(0)),e.hasPlayed_=function(){return!0}}),{v:!1}):(e.trigger("firstplay"),void e.tech_.setCurrentTime(t.end(0))):{v:!1}}();if("object"==typeof i)return i.v}return this.hasPlayed_=function(){return!0},this.load(),!0}},{key:"handleSourceOpen_",value:function(){try{this.setupSourceBuffers_()}catch(t){return _["default"].log.warn("Failed to create Source Buffers",t),this.mediaSource.endOfStream("decode")}if(this.tech_.autoplay()){var e=this.tech_.play();void 0!==e&&"function"==typeof e.then&&e.then(null,function(e){})}this.trigger("sourceopen")}},{key:"onEndOfStream",value:function(){var e=this.mainSegmentLoader_.ended_;this.mediaTypes_.AUDIO.activePlaylistLoader&&(e=e&&this.audioSegmentLoader_.ended_),e&&this.mediaSource.endOfStream()}},{key:"stuckAtPlaylistEnd_",value:function(e){if(!this.seekable().length)return!1;var t=this.syncController_.getExpiredTime(e,this.mediaSource.duration);if(null===t)return!1;var i=U.Playlist.playlistEnd(e,t),n=this.tech_.currentTime(),r=this.tech_.buffered();if(!r.length)return i-n<=g["default"].SAFE_TIME_DELTA;var a=r.end(r.length-1);return a-n<=g["default"].SAFE_TIME_DELTA&&i-a<=g["default"].SAFE_TIME_DELTA}},{key:"blacklistCurrentPlaylist",value:function(e,t){e===undefined&&(e={});var i=undefined,n=undefined;if(i=e.playlist||this.masterPlaylistLoader_.media(),t=t||e.blacklistDuration||this.blacklistDuration,!i){this.error=e;try{return this.mediaSource.endOfStream("network")}catch(a){return this.trigger("error")}}var r=1===this.masterPlaylistLoader_.master.playlists.filter(l.isEnabled).length;return r?(_["default"].log.warn("Problem encountered with the current HLS playlist. Trying again since it is the final playlist."),this.tech_.trigger("retryplaylist"),this.masterPlaylistLoader_.load(r)):(i.excludeUntil=Date.now()+1e3*t,this.tech_.trigger("blacklistplaylist"),this.tech_.trigger({type:"usage",name:"hls-rendition-blacklisted"}),n=this.selectPlaylist(),_["default"].log.warn("Problem encountered with the current HLS playlist."+(e.message?" "+e.message:"")+" Switching to another playlist."),this.masterPlaylistLoader_.media(n))}},{key:"pauseLoading",value:function(){this.mainSegmentLoader_.pause(),this.mediaTypes_.AUDIO.activePlaylistLoader&&this.audioSegmentLoader_.pause(),this.mediaTypes_.SUBTITLES.activePlaylistLoader&&this.subtitleSegmentLoader_.pause()}},{key:"setCurrentTime",value:function(e){var t=g["default"].findRange(this.tech_.buffered(),e);return this.masterPlaylistLoader_&&this.masterPlaylistLoader_.media()&&this.masterPlaylistLoader_.media().segments?t&&t.length&&"flash"!==this.mode_?e:(this.mainSegmentLoader_.resetEverything(),this.mainSegmentLoader_.abort(),this.mediaTypes_.AUDIO.activePlaylistLoader&&(this.audioSegmentLoader_.resetEverything(),this.audioSegmentLoader_.abort()),this.mediaTypes_.SUBTITLES.activePlaylistLoader&&(this.subtitleSegmentLoader_.resetEverything(),this.subtitleSegmentLoader_.abort()),void this.load()):0}},{key:"duration",value:function(){return this.masterPlaylistLoader_?this.mediaSource?this.mediaSource.duration:U.Playlist.duration(this.masterPlaylistLoader_.media()):0}},{key:"seekable",value:function(){return this.seekable_}},{key:"onSyncInfoUpdate_",value:function(){var e=undefined,t=undefined;if(this.masterPlaylistLoader_){var i=this.masterPlaylistLoader_.media();if(i){var n=this.syncController_.getExpiredTime(i,this.mediaSource.duration);if(null!==n&&(e=U.Playlist.seekable(i,n),0!==e.length)){if(this.mediaTypes_.AUDIO.activePlaylistLoader){if(i=this.mediaTypes_.AUDIO.activePlaylistLoader.media(),null===(n=this.syncController_.getExpiredTime(i,this.mediaSource.duration)))return;if(t=U.Playlist.seekable(i,n),0===t.length)return}t?t.start(0)>e.end(0)||e.start(0)>t.end(0)?this.seekable_=e:this.seekable_=_["default"].createTimeRanges([[t.start(0)>e.start(0)?t.start(0):e.start(0),t.end(0)<e.end(0)?t.end(0):e.end(0)]]):this.seekable_=e,this.tech_.trigger("seekablechanged")}}}}},{key:"updateDuration",value:function(){var e=this,t=this.mediaSource.duration,i=U.Playlist.duration(this.masterPlaylistLoader_.media()),n=this.tech_.buffered(),r=function a(){e.mediaSource.duration=i,e.tech_.trigger("durationchange"),e.mediaSource.removeEventListener("sourceopen",a)};n.length>0&&(i=Math.max(i,n.end(n.length-1))),t!==i&&("open"!==this.mediaSource.readyState?this.mediaSource.addEventListener("sourceopen",r):r())}},{key:"dispose",value:function(){var e=this;this.decrypter_.terminate(),this.masterPlaylistLoader_.dispose(),this.mainSegmentLoader_.dispose(),["AUDIO","SUBTITLES"].forEach(function(t){var i=e.mediaTypes_[t].groups;for(var n in i)i[n].forEach(function(e){e.playlistLoader&&e.playlistLoader.dispose()})}),this.audioSegmentLoader_.dispose(),this.subtitleSegmentLoader_.dispose()}},{key:"master",value:function(){return this.masterPlaylistLoader_.master}},{key:"media",value:function(){return this.masterPlaylistLoader_.media()||this.initialMedia_}},{key:"setupSourceBuffers_",value:function(){var e=this.masterPlaylistLoader_.media(),t=undefined;if(e&&"open"===this.mediaSource.readyState){if(t=q(this.masterPlaylistLoader_.master,e),t.length<1)return this.error="No compatible SourceBuffer configuration for the variant stream:"+e.resolvedUri,this.mediaSource.endOfStream("decode");this.mainSegmentLoader_.mimeType(t[0]),t[1]&&this.audioSegmentLoader_.mimeType(t[1]),this.excludeIncompatibleVariants_(e)}}},{key:"excludeIncompatibleVariants_",value:function(e){var t=this.masterPlaylistLoader_.master,i=2,n=null,r=undefined;e.attributes.CODECS&&(r=(0,I.parseCodecs)(e.attributes.CODECS),n=r.videoCodec,i=r.codecCount),t.playlists.forEach(function(e){var t={codecCount:2,videoCodec:null};if(e.attributes.CODECS){var r=e.attributes.CODECS;t=(0,I.parseCodecs)(r),window.MediaSource&&window.MediaSource.isTypeSupported&&!window.MediaSource.isTypeSupported('video/mp4; codecs="'+B(r)+'"')&&(e.excludeUntil=Infinity)}t.codecCount!==i&&(e.excludeUntil=Infinity),t.videoCodec!==n&&(e.excludeUntil=Infinity)})}},{key:"updateAdCues_",value:function(e){var t=0,i=this.seekable();i.length&&(t=i.start(0)),b["default"].updateAdCues(e,this.cueTagsTrack_,t)}},{key:"goalBufferLength",value:function(){var e=this.tech_.currentTime(),t=P["default"].GOAL_BUFFER_LENGTH,i=P["default"].GOAL_BUFFER_LENGTH_RATE,n=Math.max(t,P["default"].MAX_GOAL_BUFFER_LENGTH);return Math.min(t+e*i,n)}},{key:"bufferLowWaterLine",value:function(){var e=this.tech_.currentTime(),t=P["default"].BUFFER_LOW_WATER_LINE,i=P["default"].BUFFER_LOW_WATER_LINE_RATE,n=Math.max(t,P["default"].MAX_BUFFER_LOW_WATER_LINE);return Math.min(t+e*i,n)}}]),t}(_["default"].EventTarget);i.MasterPlaylistController=G}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],7:[function(e,t,i){(function(t){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(i,"__esModule",{value:!0});var r="undefined"!=typeof window?window.videojs:void 0!==t?t.videojs:null,a=n(r),s=e(10),o=n(s),u=function(){},d=function(e){var t=e["default"]?"main":"alternative";return e.characteristics&&e.characteristics.indexOf("public.accessibility.describes-video")>=0&&(t="main-desc"),t},l=function(e,t){e.abort(),e.pause(),t&&t.activePlaylistLoader&&(t.activePlaylistLoader.pause(),t.activePlaylistLoader=null)};i.stopLoaders=l;var f=function(e,t){t.activePlaylistLoader=e,e.load()};i.startLoaders=f;var c=function(e,t){return function(){var i=t.segmentLoaders,n=i[e],r=i.main,a=t.mediaTypes[e],s=a.activeTrack(),o=a.activeGroup(s),u=a.activePlaylistLoader;if(l(n,a),o){if(!o.playlistLoader)return void(u&&r.resetEverything());n.resyncLoader(),f(o.playlistLoader,a)}}};i.onGroupChanged=c;var h=function(e,t){return function(){var i=t.segmentLoaders,n=i[e],r=i.main,a=t.mediaTypes[e],s=a.activeTrack(),o=a.activeGroup(s),u=a.activePlaylistLoader;if(l(n,a),o){if(!o.playlistLoader)return void r.resetEverything();if(u===o.playlistLoader)return void f(o.playlistLoader,a);n.track&&n.track(s),n.resetEverything(),f(o.playlistLoader,a)}}};i.onTrackChanged=h;var p={AUDIO:function(e,t){return function(){var i=t.segmentLoaders[e],n=t.mediaTypes[e],r=t.blacklistCurrentPlaylist;l(i,n);var s=n.activeTrack(),o=n.activeGroup(),u=(o.filter(function(e){return e["default"]})[0]||o[0]).id,d=n.tracks[u];if(s===d)return void r({message:"Problem encountered loading the default audio track."});a["default"].log.warn("Problem encountered loading the alternate audio track.Switching back to default.");for(var f in n.tracks)n.tracks[f].enabled=n.tracks[f]===d;n.onTrackChanged()}},SUBTITLES:function(e,t){return function(){var i=t.segmentLoaders[e],n=t.mediaTypes[e];a["default"].log.warn("Problem encountered loading the subtitle track.Disabling subtitle track."),l(i,n);var r=n.activeTrack();r&&(r.mode="disabled"),n.onTrackChanged()}}};i.onError=p;var m={AUDIO:function(e,t,i){if(t){var n=i.tech,r=i.requestOptions,a=i.segmentLoaders[e];t.on("loadedmetadata",function(){var e=t.media();a.playlist(e,r),(!n.paused()||e.endList&&"none"!==n.preload())&&a.load()}),t.on("loadedplaylist",function(){a.playlist(t.media(),r),n.paused()||a.load()}),t.on("error",p[e](e,i))}},SUBTITLES:function(e,t,i){var n=i.tech,r=i.requestOptions,a=i.segmentLoaders[e],s=i.mediaTypes[e];t.on("loadedmetadata",function(){var e=t.media();a.playlist(e,r),a.track(s.activeTrack()),(!n.paused()||e.endList&&"none"!==n.preload())&&a.load()}),t.on("loadedplaylist",function(){a.playlist(t.media(),r),n.paused()||a.load()}),t.on("error",p[e](e,i))}};i.setupListeners=m;var g={AUDIO:function(e,t){var i=t.mode,n=t.hls,r=t.segmentLoaders[e],s=t.requestOptions,u=t.master.mediaGroups,l=t.mediaTypes[e],f=l.groups,c=l.tracks;u[e]&&0!==Object.keys(u[e]).length&&"html5"===i||(u[e]={main:{"default":{"default":!0}}});for(var h in u[e]){f[h]||(f[h]=[]);for(var g in u[e][h]){var y=u[e][h][g],_=undefined;if(_=y.resolvedUri?new o["default"](y.resolvedUri,n,s):null,y=a["default"].mergeOptions({id:g,playlistLoader:_},y),m[e](e,y.playlistLoader,t),f[h].push(y),"undefined"==typeof c[g]){var v=new a["default"].AudioTrack({id:g,kind:d(y),enabled:!1,language:y.language,"default":y["default"],label:g});c[g]=v}}}r.on("error",p[e](e,t))},SUBTITLES:function(e,t){var i=t.tech,n=t.hls,r=t.segmentLoaders[e],s=t.requestOptions,u=t.master.mediaGroups,d=t.mediaTypes[e],l=d.groups,f=d.tracks;for(var c in u[e]){l[c]||(l[c]=[]);for(var h in u[e][c])if(!u[e][c][h].forced){var g=u[e][c][h];if(g=a["default"].mergeOptions({id:h,playlistLoader:new o["default"](g.resolvedUri,n,s)},g),m[e](e,g.playlistLoader,t),l[c].push(g),"undefined"==typeof f[h]){var y=i.addRemoteTextTrack({id:h,kind:"subtitles",enabled:!1,language:g.language,label:h},!1).track;f[h]=y}}}r.on("error",p[e](e,t))},"CLOSED-CAPTIONS":function(e,t){var i=t.tech,n=t.master.mediaGroups,r=t.mediaTypes[e],s=r.groups,o=r.tracks;for(var u in n[e]){s[u]||(s[u]=[]);for(var d in n[e][u]){var l=n[e][u][d];if(l.instreamId.match(/CC\d/)&&(s[u].push(a["default"].mergeOptions({id:d},l)),"undefined"==typeof o[d])){var f=i.addRemoteTextTrack({id:l.instreamId,kind:"captions",enabled:!1,language:l.language,label:d},!1).track;o[d]=f}}}}};i.initialize=g;var y=function(e,t){return function(i){var n=t.masterPlaylistLoader,r=t.mediaTypes[e].groups,a=n.media();if(!a)return null;var s=null;return a.attributes[e]&&(s=r[a.attributes[e]]),s=s||r.main,void 0===i?s:null===i?null:s.filter(function(e){
return e.id===i.id})[0]||null}};i.activeGroup=y;var _={AUDIO:function(e,t){return function(){var i=t.mediaTypes[e].tracks;for(var n in i)if(i[n].enabled)return i[n];return null}},SUBTITLES:function(e,t){return function(){var i=t.mediaTypes[e].tracks;for(var n in i)if("showing"===i[n].mode)return i[n];return null}}};i.activeTrack=_;var v=function(e){["AUDIO","SUBTITLES","CLOSED-CAPTIONS"].forEach(function(t){g[t](t,e)});var t=e.mediaTypes,i=e.masterPlaylistLoader,n=e.tech,r=e.hls;["AUDIO","SUBTITLES"].forEach(function(i){t[i].activeGroup=y(i,e),t[i].activeTrack=_[i](i,e),t[i].onGroupChanged=c(i,e),t[i].onTrackChanged=h(i,e)});var a=t.AUDIO.activeGroup(),s=(a.filter(function(e){return e["default"]})[0]||a[0]).id;t.AUDIO.tracks[s].enabled=!0,t.AUDIO.onTrackChanged(),i.on("mediachange",function(){["AUDIO","SUBTITLES"].forEach(function(e){return t[e].onGroupChanged()})});var o=function(){t.AUDIO.onTrackChanged(),n.trigger({type:"usage",name:"hls-audio-change"})};n.audioTracks().addEventListener("change",o),n.remoteTextTracks().addEventListener("change",t.SUBTITLES.onTrackChanged),r.on("dispose",function(){n.audioTracks().removeEventListener("change",o),n.remoteTextTracks().removeEventListener("change",t.SUBTITLES.onTrackChanged)}),n.clearTracks("audio");for(var u in t.AUDIO.tracks)n.audioTracks().addTrack(t.AUDIO.tracks[u])};i.setupMediaGroups=v;var b=function(){var e={};return["AUDIO","SUBTITLES","CLOSED-CAPTIONS"].forEach(function(t){e[t]={groups:{},tracks:{},activePlaylistLoader:null,activeGroup:u,activeTrack:u,onGroupChanged:u,onTrackChanged:u}}),e};i.createMediaTypes=b}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],8:[function(e,t,i){(function(t){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(i,"__esModule",{value:!0});var r="undefined"!=typeof window?window.videojs:void 0!==t?t.videojs:null,a=n(r),s=e(3),o=e(2),u=n(o),d={FAILURE:2,TIMEOUT:-101,ABORTED:-102};i.REQUEST_ERRORS=d;var l=undefined,f=function(e){var t=(undefined,undefined);return t=e.offset+e.length-1,"bytes="+e.offset+"-"+t},c=function(e){var t={};return e.byterange&&(t.Range=f(e.byterange)),t},h=function(e){e.forEach(function(e){e.abort()})},p=function(e){return{bandwidth:e.bandwidth,bytesReceived:e.bytesReceived||0,roundTripTime:e.roundTripTime||0}},m=function(e){var t=e.target,i=Date.now()-t.requestTime,n={bandwidth:Infinity,bytesReceived:0,roundTripTime:i||0};return n.bytesReceived=e.loaded,n.bandwidth=Math.floor(n.bytesReceived/n.roundTripTime*8*1e3),n},g=function(e,t){return t.timedout?{status:t.status,message:"HLS request timed-out at URL: "+t.uri,code:d.TIMEOUT,xhr:t}:t.aborted?{status:t.status,message:"HLS request aborted at URL: "+t.uri,code:d.ABORTED,xhr:t}:e?{status:t.status,message:"HLS request errored at URL: "+t.uri,code:d.FAILURE,xhr:t}:null},y=function(e,t){return function(i,n){var r=n.response,a=g(i,n),s=undefined;if(a)return t(a,e);if("media-drm-token"===e.playlist.keyFormat||"media-drm-player-binding"===e.playlist.keyFormat){for(var o=JSON.parse(r),f=o.encryptedVideoKey,c=u["default"].utils.utf8.toBytes("72Fhskjglp8qjpqx"),h=new u["default"].ModeOfOperation.ecb(c),p=[],m=0;m<f.length;m+=2)p.push(parseInt(f[m]+f[m+1],16));var y=h.decrypt(p),_=u["default"].utils.utf8.fromBytes(y),v=function(e){for(var t=new ArrayBuffer(e.length),i=new Uint8Array(t),n=0,r=e.length;n<r;n++)i[n]=e.charCodeAt(n);return t}(_);l=s=new DataView(v)}else{if(16!==r.byteLength)return t({status:n.status,message:"Invalid HLS key at URL: "+n.uri,code:d.FAILURE,xhr:n},e);l=s=new DataView(r)}return e.key.bytes=new Uint32Array([s.getUint32(0),s.getUint32(4),s.getUint32(8),s.getUint32(12)]),t(null,e)}},_=function(e,t){return function(i,n){var r=n.response,a=g(i,n);return a?t(a,e):0===r.byteLength?t({status:n.status,message:"Empty HLS segment content at URL: "+n.uri,code:d.FAILURE,xhr:n},e):(e.map.bytes=new Uint8Array(n.response),t(null,e))}},v=function(e,t){return function(i,n){var r=n.response,a=g(i,n);return a?t(a,e):0===r.byteLength?t({status:n.status,message:"Empty HLS segment content at URL: "+n.uri,code:d.FAILURE,xhr:n},e):(e.stats=p(n),e.key?e.encryptedBytes=new Uint8Array(n.response):e.bytes=new Uint8Array(n.response),t(null,e))}},b=function(e,t,i){var n=function r(n){if(n.data.source===t.requestId){e.removeEventListener("message",r);var a=n.data.decrypted;return t.bytes=new Uint8Array(a.bytes,a.byteOffset,a.byteLength),i(null,t)}};e.addEventListener("message",n),e.postMessage((0,s.createTransferableMessage)({source:t.requestId,encrypted:t.encryptedBytes,key:t.key.bytes,iv:t.key.iv}),[t.encryptedBytes.buffer,t.key.bytes.buffer])},T=function(e){return e.reduce(function(e,t){return t.code>e.code?t:e})},S=function(e,t,i){var n=[],r=0;return function(a,s){if(a&&(h(e),n.push(a)),(r+=1)===e.length){if(s.endOfAllRequests=Date.now(),n.length>0){var o=T(n);return i(o,s)}return s.encryptedBytes?b(t,s,i):i(null,s)}}},w=function(e,t){return function(i){return e.stats=a["default"].mergeOptions(e.stats,m(i)),!e.stats.firstBytesReceivedAt&&e.stats.bytesReceived&&(e.stats.firstBytesReceivedAt=Date.now()),t(i,e)}},k=function(e,t,i,n,r,s,o){var u=[],d=S(u,i,o);if(r.key){var f="",p="";if(l)r.key.bytes=new Uint32Array([l.getUint32(0),l.getUint32(4),l.getUint32(8),l.getUint32(12)]);else{if("media-drm-token"===r.playlist.keyFormat&&!n.options_.token)return;"media-drm-token"===r.playlist.keyFormat?(f=r.key.resolvedUri+"&playerId=pid-1-5-1&token="+n.options_.token[n.options_.url],p="text/json"):"media-drm-player-binding"===r.playlist.keyFormat?(f=r.key.resolvedUri+"&playerId=pid-1-5-1",p="text/json"):(f=r.key.resolvedUri,p="arraybuffer");var m=a["default"].mergeOptions(t,{uri:f,responseType:p}),g=y(r,d),b=e(m,g);u.push(b)}}if(r.map&&!r.map.bytes){var T=a["default"].mergeOptions(t,{uri:r.map.resolvedUri,responseType:"arraybuffer",headers:c(r.map)}),k=_(r,d),E=e(T,k);u.push(E)}var O=a["default"].mergeOptions(t,{uri:r.resolvedUri,responseType:"arraybuffer",headers:c(r)}),A=v(r,d),L=e(O,A);return L.addEventListener("progress",w(r,s)),u.push(L),function(){return h(u)}};i.mediaSegmentRequest=k;var E=function(){l=null};i.resetKeyView=E}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],9:[function(e,t,i){(function(n){"use strict";function r(e){return e&&e.__esModule?e:{"default":e}}function a(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(i,"__esModule",{value:!0});var s=function(){function e(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,i,n){return i&&e(t.prototype,i),n&&e(t,n),t}}(),o=e(30),u=r(o),d=e(13),l=r(d),f="undefined"!=typeof window?window.videojs:void 0!==n?n.videojs:null,c=r(f),h=["seeking","seeked","pause","playing","error"],p=function(){function e(t){var i=this;a(this,e),this.tech_=t.tech,this.seekable=t.seekable,this.consecutiveUpdates=0,this.lastRecordedTime=null,this.timer_=null,this.checkCurrentTimeTimeout_=null,t.debug&&(this.logger_=c["default"].log.bind(c["default"],"playback-watcher ->")),this.logger_("initialize");var n=function(){return i.monitorCurrentTime_()},r=function(){return i.techWaiting_()},s=function(){return i.cancelTimer_()},o=function(){return i.fixesBadSeeks_()};this.tech_.on("seekablechanged",o),this.tech_.on("waiting",r),this.tech_.on(h,s),this.tech_.on("canplay",n),this.dispose=function(){i.logger_("dispose"),i.tech_.off("seekablechanged",o),i.tech_.off("waiting",r),i.tech_.off(h,s),i.tech_.off("canplay",n),i.checkCurrentTimeTimeout_&&u["default"].clearTimeout(i.checkCurrentTimeTimeout_),i.cancelTimer_()}}return s(e,[{key:"monitorCurrentTime_",value:function(){this.checkCurrentTime_(),this.checkCurrentTimeTimeout_&&u["default"].clearTimeout(this.checkCurrentTimeTimeout_),this.checkCurrentTimeTimeout_=u["default"].setTimeout(this.monitorCurrentTime_.bind(this),250)}},{key:"checkCurrentTime_",value:function(){if(this.tech_.seeking()&&this.fixesBadSeeks_())return this.consecutiveUpdates=0,void(this.lastRecordedTime=this.tech_.currentTime());if(!this.tech_.paused()&&!this.tech_.seeking()){var e=this.tech_.currentTime(),t=this.tech_.buffered();if(this.lastRecordedTime===e&&(!t.length||e+l["default"].SAFE_TIME_DELTA>=t.end(t.length-1)))return this.techWaiting_();this.consecutiveUpdates>=5&&e===this.lastRecordedTime?(this.consecutiveUpdates++,this.waiting_()):e===this.lastRecordedTime?this.consecutiveUpdates++:(this.consecutiveUpdates=0,this.lastRecordedTime=e)}}},{key:"cancelTimer_",value:function(){this.consecutiveUpdates=0,this.timer_&&(this.logger_("cancelTimer_"),clearTimeout(this.timer_)),this.timer_=null}},{key:"fixesBadSeeks_",value:function(){var e=this.tech_.seeking(),t=this.seekable(),i=this.tech_.currentTime(),n=undefined;if(e&&this.afterSeekableWindow_(t,i)){n=t.end(t.length-1)}if(e&&this.beforeSeekableWindow_(t,i)){n=t.start(0)+l["default"].SAFE_TIME_DELTA}return void 0!==n&&(this.logger_("Trying to seek outside of seekable at time "+i+" with seekable range "+l["default"].printableRange(t)+". Seeking to "+n+"."),this.tech_.setCurrentTime(n),!0)}},{key:"waiting_",value:function(){if(!this.techWaiting_()){var e=this.tech_.currentTime(),t=this.tech_.buffered(),i=l["default"].findRange(t,e);return i.length&&e+3<=i.end(0)?(this.cancelTimer_(),this.tech_.setCurrentTime(e),this.logger_("Stopped at "+e+" while inside a buffered region ["+i.start(0)+" -> "+i.end(0)+"]. Attempting to resume playback by seeking to the current time."),void this.tech_.trigger({type:"usage",name:"hls-unknown-waiting"})):void 0}}},{key:"techWaiting_",value:function(){var e=this.seekable(),t=this.tech_.currentTime();if(this.tech_.seeking()&&this.fixesBadSeeks_())return!0;if(this.tech_.seeking()||null!==this.timer_)return!0;if(this.beforeSeekableWindow_(e,t)){var i=e.end(e.length-1);return this.logger_("Fell out of live window at time "+t+". Seeking to live point (seekable end) "+i),this.cancelTimer_(),this.tech_.setCurrentTime(i),this.tech_.trigger({type:"usage",name:"hls-live-resync"}),!0}var n=this.tech_.buffered(),r=l["default"].findNextRange(n,t);if(this.videoUnderflow_(r,n,t))return this.cancelTimer_(),this.tech_.setCurrentTime(t),this.tech_.trigger({type:"usage",name:"hls-video-underflow"}),!0;if(r.length>0){var a=r.start(0)-t;return this.logger_("Stopped at "+t+", setting timer for "+a+", seeking to "+r.start(0)),this.timer_=setTimeout(this.skipTheGap_.bind(this),1e3*a,t),!0}return!1}},{key:"afterSeekableWindow_",value:function(e,t){return!!e.length&&t>e.end(e.length-1)+l["default"].SAFE_TIME_DELTA}},{key:"beforeSeekableWindow_",value:function(e,t){return!!(e.length&&e.start(0)>0&&t<e.start(0)-l["default"].SAFE_TIME_DELTA)}},{key:"videoUnderflow_",value:function(e,t,i){if(0===e.length){var n=this.gapFromVideoUnderflow_(t,i);if(n)return this.logger_("Encountered a gap in video from "+n.start+" to "+n.end+". Seeking to current time "+i),!0}return!1}},{key:"skipTheGap_",value:function(e){var t=this.tech_.buffered(),i=this.tech_.currentTime(),n=l["default"].findNextRange(t,i);this.cancelTimer_(),0!==n.length&&i===e&&(this.logger_("skipTheGap_:","currentTime:",i,"scheduled currentTime:",e,"nextRange start:",n.start(0)),this.tech_.setCurrentTime(n.start(0)+l["default"].TIME_FUDGE_FACTOR),this.tech_.trigger({type:"usage",name:"hls-gap-skip"}))}},{key:"gapFromVideoUnderflow_",value:function(e,t){for(var i=l["default"].findGaps(e),n=0;n<i.length;n++){var r=i.start(n),a=i.end(n);if(t-r<4&&t-r>2)return{start:r,end:a}}return null}},{key:"logger_",value:function(){}}]),e}();i["default"]=p,t.exports=i["default"]}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],10:[function(e,t,i){(function(t){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function a(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}Object.defineProperty(i,"__esModule",{value:!0});var s=function(){function e(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,i,n){return i&&e(t.prototype,i),n&&e(t,n),t}}(),o=function(e,t,i){for(var n=!0;n;){var r=e,a=t,s=i;n=!1,null===r&&(r=Function.prototype);var o=Object.getOwnPropertyDescriptor(r,a);if(o!==undefined){if("value"in o)return o.value;var u=o.get;return u===undefined?undefined:u.call(s)}var d=Object.getPrototypeOf(r);if(null===d)return undefined;e=d,t=a,i=s,n=!0,o=d=undefined}},u=e(16),d=n(u),l="undefined"!=typeof window?window.videojs:void 0!==t?t.videojs:null,f=e(31),c=n(f),h=e(30),p=n(h),m=function(e,t,i){var n=t.slice();i=i||0;for(var r=Math.min(e.length,t.length+i),a=i;a<r;a++)n[a-i]=(0,l.mergeOptions)(e[a],n[a-i]);return n};i.updateSegments=m;var g=function(e,t){e.resolvedUri||(e.resolvedUri=(0,d["default"])(t,e.uri)),e.key&&!e.key.resolvedUri&&(e.key.resolvedUri=(0,d["default"])(t,e.key.uri)),e.map&&!e.map.resolvedUri&&(e.map.resolvedUri=(0,d["default"])(t,e.map.uri))};i.resolveSegmentUris=g;var y=function(e,t){var i=(0,l.mergeOptions)(e,{}),n=i.playlists.filter(function(e){return e.uri===t.uri})[0];if(!n)return null;if(n.segments&&t.segments&&n.segments.length===t.segments.length&&n.mediaSequence===t.mediaSequence)return null;var r=(0,l.mergeOptions)(n,t);n.segments&&(r.segments=m(n.segments,t.segments,t.mediaSequence-n.mediaSequence)),r.segments.forEach(function(e){g(e,r.resolvedUri)});for(var a=0;a<i.playlists.length;a++)i.playlists[a].uri===t.uri&&(i.playlists[a]=r);return i.playlists[t.uri]=r,i};i.updateMaster=y;var _=function(e){for(var t=e.playlists.length;t--;){var i=e.playlists[t];e.playlists[i.uri]=i,i.resolvedUri=(0,d["default"])(e.uri,i.uri),i.attributes||(i.attributes={},l.log.warn("Invalid playlist STREAM-INF detected. Missing BANDWIDTH attribute."))}};i.setupMediaPlaylists=_;var v=function(e){["AUDIO","SUBTITLES"].forEach(function(t){for(var i in e.mediaGroups[t])for(var n in e.mediaGroups[t][i]){var r=e.mediaGroups[t][i][n];r.uri&&(r.resolvedUri=(0,d["default"])(e.uri,r.uri))}})};i.resolveMediaGroupUris=v;var b=function(e,t){var i=e.segments[e.segments.length-1];undefined;return t&&i&&i.duration?1e3*i.duration:500*(e.targetDuration||10)};i.refreshDelay=b;var T=function(e){function t(e,i,n){var a=this;if(r(this,t),o(Object.getPrototypeOf(t.prototype),"constructor",this).call(this),n=n||{},this.srcUrl=e,this.hls_=i,this.withCredentials=!!n.withCredentials,this.handleManifestRedirects=!!n.handleManifestRedirects,!this.srcUrl)throw new Error("A non-empty playlist URL is required");this.state="HAVE_NOTHING",this.on("mediaupdatetimeout",function(){"HAVE_METADATA"===a.state&&(a.state="HAVE_CURRENT_METADATA",a.request=a.hls_.xhr({uri:(0,d["default"])(a.master.uri,a.media().uri),withCredentials:a.withCredentials},function(e,t){if(a.request)return e?a.playlistRequestError(a.request,a.media().uri,"HAVE_METADATA"):void a.haveMetadata(a.request,a.media().uri)}))})}return a(t,e),s(t,[{key:"playlistRequestError",value:function(e,t,i){this.request=null,i&&(this.state=i),this.error={playlist:this.master.playlists[t],status:e.status,message:"HLS playlist request error at URL: "+t,responseText:e.responseText,code:e.status>=500?4:2},this.trigger("error")}},{key:"haveMetadata",value:function(e,t){var i=this;this.request=null,this.state="HAVE_METADATA";var n=new c["default"].Parser;n.push(e.responseText),n.end(),n.manifest.uri=t,n.manifest.attributes=n.manifest.attributes||{},/media-drm-token/.test(e.responseText)?n.manifest.keyFormat="media-drm-token":/media-drm-player-binding/.test(e.responseText)?n.manifest.keyFormat="media-drm-player-binding":n.manifest.keyFormat="normal";var r=y(this.master,n.manifest);this.targetDuration=n.manifest.targetDuration,r?(this.master=r,this.media_=this.master.playlists[n.manifest.uri]):this.trigger("playlistunchanged"),this.media().endList||(p["default"].clearTimeout(this.mediaUpdateTimeout),this.mediaUpdateTimeout=p["default"].setTimeout(function(){i.trigger("mediaupdatetimeout")},b(this.media(),!!r))),this.trigger("loadedplaylist")}},{key:"dispose",value:function(){this.stopRequest(),p["default"].clearTimeout(this.mediaUpdateTimeout)}},{key:"stopRequest",value:function(){if(this.request){var e=this.request;this.request=null,e.onreadystatechange=null,e.abort()}}},{key:"media",value:function(e){var t=this,i=!1;if(!e)return this.media_;if("HAVE_NOTHING"===this.state)throw new Error("Cannot switch media playlist from "+this.state);var n=this.state;if("string"==typeof e){if(!this.master.playlists[e])throw new Error("Unknown playlist URI: "+e);e=this.master.playlists[e],i=!0}var r=!this.media_||e.uri!==this.media_.uri;if(this.master.playlists[e.uri].endList)return this.request&&(this.request.onreadystatechange=null,this.request.abort(),this.request=null),this.state="HAVE_METADATA",i&&(this.media_=e),void(r&&(this.trigger("mediachanging"),this.trigger("mediachange")));if(r){if(this.state="SWITCHING_MEDIA",this.request){if(e.resolvedUri===this.request.url)return;this.request.onreadystatechange=null,this.request.abort(),this.request=null}this.media_&&this.trigger("mediachanging"),this.request=this.hls_.xhr({uri:e.resolvedUri,withCredentials:this.withCredentials},function(i,r){if(t.request){if(e.resolvedUri=t.resolveManifestRedirect(e.resolvedUri,r),i)return t.playlistRequestError(t.request,e.uri,n);t.haveMetadata(r,e.uri),"HAVE_MASTER"===n?t.trigger("loadedmetadata"):t.trigger("mediachange")}})}}},{key:"resolveManifestRedirect",value:function(e,t){return this.handleManifestRedirects&&t.responseURL&&e!==t.responseURL?t.responseURL:e}},{key:"pause",value:function(){this.stopRequest(),p["default"].clearTimeout(this.mediaUpdateTimeout),"HAVE_NOTHING"===this.state&&(this.started=!1),"SWITCHING_MEDIA"===this.state?this.media_?this.state="HAVE_METADATA":this.state="HAVE_MASTER":"HAVE_CURRENT_METADATA"===this.state&&(this.state="HAVE_METADATA")}},{key:"load",value:function(e){var t=this;p["default"].clearTimeout(this.mediaUpdateTimeout);var i=this.media();if(e){var n=i?i.targetDuration/2*1e3:5e3;return void(this.mediaUpdateTimeout=p["default"].setTimeout(function(){return t.load()},n))}if(!this.started)return void this.start();i&&!i.endList?this.trigger("mediaupdatetimeout"):this.trigger("loadedplaylist")}},{key:"start",value:function(){var e=this;this.started=!0,this.request=this.hls_.xhr({uri:this.srcUrl,withCredentials:this.withCredentials},function(t,i){if(e.request){if(e.request=null,t)return e.error={status:i.status,message:"HLS playlist request error at URL: "+e.srcUrl,responseText:i.responseText,code:2},"HAVE_NOTHING"===e.state&&(e.started=!1),e.trigger("error");var n=new c["default"].Parser;return n.push(i.responseText),(n.end(),e.state="HAVE_MASTER",e.srcUrl=e.resolveManifestRedirect(e.srcUrl,i),n.manifest.uri=e.srcUrl,n.manifest.playlists)?(e.master=n.manifest,_(e.master),v(e.master),e.trigger("loadedplaylist"),void(e.request||e.media(n.manifest.playlists[0]))):(e.master={mediaGroups:{AUDIO:{},VIDEO:{},"CLOSED-CAPTIONS":{},SUBTITLES:{}},uri:p["default"].location.href,playlists:[{uri:e.srcUrl,resolvedUri:e.srcUrl,attributes:{}}]},e.master.playlists[e.srcUrl]=e.master.playlists[0],e.haveMetadata(i,e.srcUrl),e.trigger("loadedmetadata"))}})}}]),t}(l.EventTarget);i["default"]=T}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],11:[function(e,t,i){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(i,"__esModule",{value:!0});var r=e(4),a=n(r),s=e(12),o=n(s),u=e(20),d=function(e,t){var i=undefined;return e?(i=window.getComputedStyle(e),i?i[t]:""):""},l=function(e,t){var i=e.slice();e.sort(function(e,n){var r=t(e,n);return 0===r?i.indexOf(e)-i.indexOf(n):r})},f=function(e,t){var i=undefined,n=undefined;return e.attributes.BANDWIDTH&&(i=e.attributes.BANDWIDTH),i=i||window.Number.MAX_VALUE,t.attributes.BANDWIDTH&&(n=t.attributes.BANDWIDTH),n=n||window.Number.MAX_VALUE,i-n};i.comparePlaylistBandwidth=f;var c=function(e,t){var i=undefined,n=undefined;return e.attributes.RESOLUTION&&e.attributes.RESOLUTION.width&&(i=e.attributes.RESOLUTION.width),i=i||window.Number.MAX_VALUE,t.attributes.RESOLUTION&&t.attributes.RESOLUTION.width&&(n=t.attributes.RESOLUTION.width),n=n||window.Number.MAX_VALUE,i===n&&e.attributes.BANDWIDTH&&t.attributes.BANDWIDTH?e.attributes.BANDWIDTH-t.attributes.BANDWIDTH:i-n};i.comparePlaylistResolution=c;var h=function(e,t,i,n){var r=e.playlists.map(function(e){var t=undefined,i=undefined,n=undefined;return t=e.attributes.RESOLUTION&&e.attributes.RESOLUTION.width,i=e.attributes.RESOLUTION&&e.attributes.RESOLUTION.height,n=e.attributes.BANDWIDTH,n=n||window.Number.MAX_VALUE,{bandwidth:n,width:t,height:i,playlist:e}});l(r,function(e,t){return e.bandwidth-t.bandwidth}),r=r.filter(function(e){return!o["default"].isIncompatible(e.playlist)});var s=r.filter(function(e){return o["default"].isEnabled(e.playlist)});s.length||(s=r.filter(function(e){return!o["default"].isDisabled(e.playlist)}));var u=s.filter(function(e){return e.bandwidth*a["default"].BANDWIDTH_VARIANCE<t}),d=u[u.length-1],f=u.filter(function(e){return e.bandwidth===d.bandwidth})[0],c=u.filter(function(e){return e.width&&e.height});l(c,function(e,t){return e.width-t.width});var h=c.filter(function(e){return e.width===i&&e.height===n});d=h[h.length-1];var p=h.filter(function(e){return e.bandwidth===d.bandwidth})[0],m=undefined,g=undefined,y=undefined;p||(m=c.filter(function(e){return e.width>i||e.height>n}),g=m.filter(function(e){return e.width===m[0].width&&e.height===m[0].height}),d=g[g.length-1],y=g.filter(function(e){return e.bandwidth===d.bandwidth})[0]);var _=y||p||f||s[0]||r[0];return _?_.playlist:null};i.simpleSelector=h;var p=function(){return h(this.playlists.master,this.systemBandwidth,parseInt(d(this.tech_.el(),"width"),10),parseInt(d(this.tech_.el(),"height"),10))};i.lastBandwidthSelector=p;var m=function(e){var t=-1;if(e<0||e>1)throw new Error("Moving average bandwidth decay must be between 0 and 1.");return function(){return t<0&&(t=this.systemBandwidth),t=e*this.systemBandwidth+(1-e)*t,h(this.playlists.master,t,parseInt(d(this.tech_.el(),"width"),10),parseInt(d(this.tech_.el(),"height"),10))}};i.movingAverageBandwidthSelector=m;var g=function(e){var t=e.master,i=e.currentTime,n=e.bandwidth,r=e.duration,a=e.segmentDuration,s=e.timeUntilRebuffer,u=e.currentTimeline,d=e.syncController,c=t.playlists.filter(function(e){return!o["default"].isIncompatible(e)}),h=c.filter(o["default"].isEnabled);h.length||(h=c.filter(function(e){return!o["default"].isDisabled(e)}));var p=h.filter(o["default"].hasAttribute.bind(null,"BANDWIDTH")),m=p.map(function(e){var t=d.getSyncPoint(e,r,u,i),l=t?1:2;return{playlist:e,rebufferingImpact:o["default"].estimateSegmentRequestTime(a,n,e)*l-s}}),g=m.filter(function(e){return e.rebufferingImpact<=0});return l(g,function(e,t){return f(t.playlist,e.playlist)}),g.length?g[0]:(l(m,function(e,t){return e.rebufferingImpact-t.rebufferingImpact}),m[0]||null)};i.minRebufferMaxBandwidthSelector=g;var y=function(){var e=this.playlists.master.playlists.filter(o["default"].isEnabled);return l(e,function(e,t){return f(e,t)}),e.filter(function(e){return(0,u.parseCodecs)(e.attributes.CODECS).videoCodec})[0]||null};i.lowestBitrateCompatibleVariantSelector=y},{}],12:[function(e,t,i){(function(t){"use strict";Object.defineProperty(i,"__esModule",{value:!0});var n="undefined"!=typeof window?window.videojs:void 0!==t?t.videojs:null,r=e(30),a=function(e){return e&&e.__esModule?e:{"default":e}}(r),s=function(e,t){var i=0,n=t-e.mediaSequence,r=e.segments[n];if(r){if("undefined"!=typeof r.start)return{result:r.start,precise:!0};if("undefined"!=typeof r.end)return{result:r.end-r.duration,precise:!0}}for(;n--;){if(r=e.segments[n],"undefined"!=typeof r.end)return{result:i+r.end,precise:!0};if(i+=r.duration,"undefined"!=typeof r.start)return{result:i+r.start,precise:!0}}return{result:i,precise:!1}},o=function(e,t){for(var i=0,n=undefined,r=t-e.mediaSequence;r<e.segments.length;r++){if(n=e.segments[r],"undefined"!=typeof n.start)return{result:n.start-i,precise:!0};if(i+=n.duration,"undefined"!=typeof n.end)return{result:n.end-i,precise:!0}}return{result:-1,precise:!1}},u=function(e,t,i){var n=undefined,r=undefined;return void 0===t&&(t=e.mediaSequence+e.segments.length),t<e.mediaSequence?0:(n=s(e,t),n.precise?n.result:(r=o(e,t),r.precise?r.result:n.result+i))},d=function(e,t,i){if(!e)return 0;if("number"!=typeof i&&(i=0),void 0===t){if(e.totalDuration)return e.totalDuration;if(!e.endList)return a["default"].Infinity}return u(e,t,i)};i.duration=d;var l=function(e,t,i){var n=0;if(t>i){var r=[i,t];t=r[0],i=r[1]}if(t<0){for(var a=t;a<Math.min(0,i);a++)n+=e.targetDuration;t=0}for(var a=t;a<i;a++)n+=e.segments[a].duration;return n};i.sumDurations=l;var f=function(e){if(!e.segments.length)return 0;for(var t=e.segments.length-1,i=e.segments[t].duration||e.targetDuration,n=i+2*e.targetDuration;t--&&!((i+=e.segments[t].duration)>=n););return Math.max(0,t)};i.safeLiveIndex=f;var c=function(e,t,i){if(!e||!e.segments)return null;if(e.endList)return d(e);if(null===t)return null;t=t||0;var n=i?f(e):e.segments.length;return u(e,e.mediaSequence+n,t)};i.playlistEnd=c;var h=function(e,t){var i=t||0,r=c(e,t,!0);return null===r?(0,n.createTimeRange)():(0,n.createTimeRange)(i,r)};i.seekable=h;var p=function(e){return e-Math.floor(e)==0},m=function(e,t){if(p(t))return t+.1*e;for(var i=t.toString().split(".")[1].length,n=1;n<=i;n++){var r=Math.pow(10,n),a=t*r;if(p(a)||n===i)return(a+e)/r}},g=m.bind(null,1),y=m.bind(null,-1),_=function(e,t,i,n){var r=undefined,a=undefined,s=e.segments.length,o=t-n;if(o<0){if(i>0)for(r=i-1;r>=0;r--)if(a=e.segments[r],(o+=y(a.duration))>0)return{mediaIndex:r,startTime:n-l(e,i,r)};return{mediaIndex:0,startTime:t}}if(i<0){for(r=i;r<0;r++)if((o-=e.targetDuration)<0)return{mediaIndex:0,startTime:t};i=0}for(r=i;r<s;r++)if(a=e.segments[r],(o-=g(a.duration))<0)return{mediaIndex:r,startTime:n+l(e,i,r)};return{mediaIndex:s-1,startTime:t}};i.getMediaInfoForTime=_;var v=function(e){return e.excludeUntil&&e.excludeUntil>Date.now()};i.isBlacklisted=v;var b=function(e){return e.excludeUntil&&e.excludeUntil===Infinity};i.isIncompatible=b;var T=function(e){var t=v(e);return!e.disabled&&!t};i.isEnabled=T;var S=function(e){return e.disabled};i.isDisabled=S;var w=function(e){for(var t=0;t<e.segments.length;t++)if(e.segments[t].key)return!0;return!1};i.isAes=w;var k=function(e){for(var t=0;t<e.segments.length;t++)if(e.segments[t].map)return!0;return!1};i.isFmp4=k;var E=function(e,t){return t.attributes&&t.attributes[e]};i.hasAttribute=E;var O=function(e,t,i){var n=arguments.length<=3||arguments[3]===undefined?0:arguments[3];return E("BANDWIDTH",i)?(e*i.attributes.BANDWIDTH-8*n)/t:NaN};i.estimateSegmentRequestTime=O;var A=function(e,t){if(1===e.playlists.length)return!0;var i=t.attributes.BANDWIDTH||Number.MAX_VALUE;return 0===e.playlists.filter(function(e){return!!T(e)&&(e.attributes.BANDWIDTH||0)<i}).length};i.isLowestEnabledRendition=A,i["default"]={duration:d,seekable:h,safeLiveIndex:f,getMediaInfoForTime:_,isEnabled:T,isDisabled:S,isBlacklisted:v,isIncompatible:b,playlistEnd:c,isAes:w,isFmp4:k,hasAttribute:E,estimateSegmentRequestTime:O,isLowestEnabledRendition:A}}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],13:[function(e,t,i){(function(e){"use strict";Object.defineProperty(i,"__esModule",{value:!0});var n=function(){function e(e,t){var i=[],n=!0,r=!1,a=undefined;try{for(var s,o=e[Symbol.iterator]();!(n=(s=o.next()).done)&&(i.push(s.value),!t||i.length!==t);n=!0);}catch(u){r=!0,a=u}finally{try{!n&&o["return"]&&o["return"]()}finally{if(r)throw a}}return i}return function(t,i){if(Array.isArray(t))return t;if(Symbol.iterator in Object(t))return e(t,i);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),r="undefined"!=typeof window?window.videojs:void 0!==e?e.videojs:null,a=function(e){return e&&e.__esModule?e:{"default":e}}(r),s=function(e,t){var i=n(t,2),r=i[0],a=i[1];return Math.min(Math.max(r,e),a)},o=function(e,t){var i=[],n=undefined;if(e&&e.length)for(n=0;n<e.length;n++)t(e.start(n),e.end(n))&&i.push([e.start(n),e.end(n)]);return a["default"].createTimeRanges(i)},u=function(e,t){return o(e,function(e,i){return e-1/30<=t&&i+1/30>=t})},d=function(e,t){return o(e,function(e){return e-1/30>=t})},l=function(e){if(e.length<2)return a["default"].createTimeRanges();for(var t=[],i=1;i<e.length;i++){var n=e.end(i-1),r=e.start(i);t.push([n,r])}return a["default"].createTimeRanges(t)},f=function(e,t){var i=undefined,n=undefined,r=undefined,a=[],s=[],o=function(e){return e[0]<=r&&e[1]>=r};if(e)for(i=0;i<e.length;i++)n=e.start(i),r=e.end(i),s.push([n,r]);if(t)for(i=0;i<t.length;i++)n=t.start(i),r=t.end(i),s.some(o)||a.push(r);return 1!==a.length?null:a[0]},c=function(e,t){var i=null,n=null,r=0,s=[],o=[];if(!(e&&e.length&&t&&t.length))return a["default"].createTimeRange();for(var u=e.length;u--;)s.push({time:e.start(u),type:"start"}),s.push({time:e.end(u),type:"end"});for(u=t.length;u--;)s.push({time:t.start(u),type:"start"}),s.push({time:t.end(u),type:"end"});for(s.sort(function(e,t){return e.time-t.time}),u=0;u<s.length;u++)"start"===s[u].type?2===++r&&(i=s[u].time):"end"===s[u].type&&1===--r&&(n=s[u].time),null!==i&&null!==n&&(o.push([i,n]),i=null,n=null);return a["default"].createTimeRanges(o)},h=function(e,t,i,n){for(var r=t.end(0)-t.start(0),a=e.end(0)-e.start(0),s=r-a,o=c(e,n),u=c(t,n),d=0,l=0,f=o.length;f--;)d+=o.end(f)-o.start(f),o.start(f)===i&&(d+=s);for(f=u.length;f--;)l+=u.end(f)-u.start(f);return Math.max(d,l)/r*100},p=function(e,t,i,n){var r=e+t,o=a["default"].createTimeRanges([[e,r]]),u=a["default"].createTimeRanges([[s(e,[i,r]),r]]);if(u.start(0)===u.end(0))return 0;var d=h(u,o,i,n);return isNaN(d)||d===Infinity||d===-Infinity?0:d},m=function(e){var t=[];if(!e||!e.length)return"";for(var i=0;i<e.length;i++)t.push(e.start(i)+" => "+e.end(i));return t.join(", ")},g=function(e,t){var i=arguments.length<=2||arguments[2]===undefined?1:arguments[2];return((e.length?e.end(e.length-1):0)-t)/i};i["default"]={findRange:u,findNextRange:d,findGaps:l,findSoleUncommonTimeRangesEnd:f,getSegmentBufferedPercent:p,TIME_FUDGE_FACTOR:1/30,SAFE_TIME_DELTA:.1,printableRange:m,timeUntilRebuffer:g},t.exports=i["default"]}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],14:[function(e,t,i){(function(e){"use strict";Object.defineProperty(i,"__esModule",{value:!0});var n="undefined"!=typeof window?window.videojs:void 0!==e?e.videojs:null,r=function(e){return e&&e.__esModule?e:{"default":e}}(n),a={errorInterval:30,getSource:function(e){return e(this.tech({IWillNotUseThisInPlugins:!0}).currentSource_)}},s=function u(e,t){var i=0,n=0,s=r["default"].mergeOptions(a,t);e.ready(function(){e.trigger({type:"usage",name:"hls-error-reload-initialized"})});var o=function(){n&&e.currentTime(n)},d=function(t){null!==t&&t!==undefined&&(n=e.duration()!==Infinity&&e.currentTime()||0,e.one("loadedmetadata",o),e.src(t),e.trigger({type:"usage",name:"hls-error-reload"}),e.play())},l=function(){return Date.now()-i<1e3*s.errorInterval?void e.trigger({type:"usage",name:"hls-error-reload-canceled"}):s.getSource&&"function"==typeof s.getSource?(i=Date.now(),s.getSource.call(e,d)):void r["default"].log.error("ERROR: reloadSourceOnError - The option getSource must be a function!")},f=function h(){
e.off("loadedmetadata",o),e.off("error",l),e.off("dispose",h)},c=function(t){f(),u(e,t)};e.on("error",l),e.on("dispose",f),e.reloadSourceOnError=c},o=function(e){s(this,e)};i["default"]=o,t.exports=i["default"]}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],15:[function(e,t,i){"use strict";function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(i,"__esModule",{value:!0});var r=e(12),a=function(e,t,i){return function(n){var a=e.master.playlists[t],s=(0,r.isIncompatible)(a),o=(0,r.isEnabled)(a);return void 0===n?o:(n?delete a.disabled:a.disabled=!0,n===o||s||(i(),n?e.trigger("renditionenabled"):e.trigger("renditiondisabled")),n)}},s=function u(e,t,i){n(this,u);var r=e.masterPlaylistController_.fastQualityChange_.bind(e.masterPlaylistController_);if(t.attributes.RESOLUTION){var s=t.attributes.RESOLUTION;this.width=s.width,this.height=s.height}this.bandwidth=t.attributes.BANDWIDTH,this.id=i,this.enabled=a(e.playlists,t.uri,r)},o=function(e){var t=e.playlists;e.representations=function(){return t.master.playlists.filter(function(e){return!(0,r.isIncompatible)(e)}).map(function(t,i){return new s(e,t,t.uri)})}};i["default"]=o,t.exports=i["default"]},{}],16:[function(e,t,i){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(i,"__esModule",{value:!0});var r=e(64),a=n(r),s=e(30),o=n(s),u=function(e,t){return/^[a-z]+:/i.test(t)?t:(/\/\//i.test(e)||(e=a["default"].buildAbsoluteURL(o["default"].location.href,e)),a["default"].buildAbsoluteURL(e,t))};i["default"]=u,t.exports=i["default"]},{}],17:[function(e,t,i){(function(t){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function a(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}Object.defineProperty(i,"__esModule",{value:!0});var s=function(){function e(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,i,n){return i&&e(t.prototype,i),n&&e(t,n),t}}(),o=function(e,t,i){for(var n=!0;n;){var r=e,a=t,s=i;n=!1,null===r&&(r=Function.prototype);var o=Object.getOwnPropertyDescriptor(r,a);if(o!==undefined){if("value"in o)return o.value;var u=o.get;return u===undefined?undefined:u.call(s)}var d=Object.getPrototypeOf(r);if(null===d)return undefined;e=d,t=a,i=s,n=!0,o=d=undefined}},u=e(12),d=n(u),l="undefined"!=typeof window?window.videojs:void 0!==t?t.videojs:null,f=n(l),c=e(18),h=n(c),p=e(4),m=n(p),g=e(30),y=n(g),_=e(73),v=n(_),b=e(3),T=e(8),S=e(13),w=e(11),k=function(e,t,i){if(!e||!t)return!1;var n=e.segments,r=i===n.length;return e.endList&&"open"===t.readyState&&r},E=function(e){return"number"==typeof e&&isFinite(e)},O=function(e,t,i){return"main"===e&&t&&i?i.containsAudio||i.containsVideo?t.containsVideo&&!i.containsVideo?"Only audio found in segment when we expected video. We can't switch to audio only from a stream that had video. To get rid of this message, please add codec information to the manifest.":!t.containsVideo&&i.containsVideo?"Video found in segment when we expected only audio. We can't switch to a stream with video from an audio only stream. To get rid of this message, please add codec information to the manifest.":null:"Neither audio nor video found in segment.":null};i.illegalMediaSwitch=O;var A=function(e,t,i){var n=undefined;return n=e.length&&e.start(0)>0&&e.start(0)<t?e.start(0):t-30,Math.min(n,t-i)};i.safeBackBufferTrimTime=A;var L=function(e){function t(e){var i=this,n=arguments.length<=1||arguments[1]===undefined?{}:arguments[1];if(r(this,t),o(Object.getPrototypeOf(t.prototype),"constructor",this).call(this),!e)throw new TypeError("Initialization settings are required");if("function"!=typeof e.currentTime)throw new TypeError("No currentTime getter specified");if(!e.mediaSource)throw new TypeError("No MediaSource specified");this.state="INIT",this.bandwidth=e.bandwidth,this.throughput={rate:0,count:0},this.roundTrip=NaN,this.resetStats_(),this.mediaIndex=null,this.hasPlayed_=e.hasPlayed,this.currentTime_=e.currentTime,this.seekable_=e.seekable,this.seeking_=e.seeking,this.duration_=e.duration,this.mediaSource_=e.mediaSource,this.hls_=e.hls,this.loaderType_=e.loaderType,this.startingMedia_=void 0,this.segmentMetadataTrack_=e.segmentMetadataTrack,this.goalBufferLength_=e.goalBufferLength,this.checkBufferTimeout_=null,this.error_=void 0,this.currentTimeline_=-1,this.pendingSegment_=null,this.mimeType_=null,this.sourceUpdater_=null,this.xhrOptions_=null,this.activeInitSegmentId_=null,this.initSegments_={},this.decrypter_=e.decrypter,this.syncController_=e.syncController,this.syncPoint_={segmentIndex:0,time:0},this.syncController_.on("syncinfoupdate",function(){return i.trigger("syncinfoupdate")}),this.mediaSource_.addEventListener("sourceopen",function(){return i.ended_=!1}),this.fetchAtBuffer_=!1,n.debug&&(this.logger_=f["default"].log.bind(f["default"],"segment-loader",this.loaderType_,"->"))}return a(t,e),s(t,[{key:"resetStats_",value:function(){this.mediaBytesTransferred=0,this.mediaRequests=0,this.mediaRequestsAborted=0,this.mediaRequestsTimedout=0,this.mediaRequestsErrored=0,this.mediaTransferDuration=0,this.mediaSecondsLoaded=0}},{key:"dispose",value:function(){this.state="DISPOSED",this.pause(),this.abort_(),this.sourceUpdater_&&this.sourceUpdater_.dispose(),this.resetStats_()}},{key:"abort",value:function(){if("WAITING"!==this.state)return void(this.pendingSegment_&&(this.pendingSegment_=null));this.abort_(),this.state="READY",this.paused()||this.monitorBuffer_()}},{key:"abort_",value:function(){this.pendingSegment_&&this.pendingSegment_.abortRequests&&this.pendingSegment_.abortRequests(),this.pendingSegment_=null}},{key:"error",value:function(e){return void 0!==e&&(this.error_=e),this.pendingSegment_=null,this.error_}},{key:"endOfStream",value:function(){this.ended_=!0,this.pause(),this.trigger("ended")}},{key:"buffered_",value:function(){return this.sourceUpdater_?this.sourceUpdater_.buffered():f["default"].createTimeRanges()}},{key:"initSegment",value:function(e){var t=!(arguments.length<=1||arguments[1]===undefined)&&arguments[1];if(!e)return null;var i=(0,b.initSegmentId)(e),n=this.initSegments_[i];return t&&!n&&e.bytes&&(this.initSegments_[i]=n={resolvedUri:e.resolvedUri,byterange:e.byterange,bytes:e.bytes}),n||e}},{key:"couldBeginLoading_",value:function(){return this.playlist_&&(this.sourceUpdater_||this.mimeType_&&"INIT"===this.state)&&!this.paused()}},{key:"load",value:function(){if(this.monitorBuffer_(),this.playlist_){if(this.syncController_.setDateTimeMapping(this.playlist_),"INIT"===this.state&&this.couldBeginLoading_())return this.init_();!this.couldBeginLoading_()||"READY"!==this.state&&"INIT"!==this.state||(this.state="READY")}}},{key:"init_",value:function(){return this.state="READY",this.sourceUpdater_=new h["default"](this.mediaSource_,this.mimeType_),this.resetEverything(),"media-drm-token"===this.playlist_.keyFormat?this.waitForToken():(this.state="READY",this.monitorBuffer_())}},{key:"waitForToken",value:function(){var e=this,t=this.hls_.options_.url;if(this.hls_.options_.token&&this.hls_.options_.token[t])return this.state="READY",this.monitorBuffer_();setTimeout(function(){e.waitForToken()},100)}},{key:"playlist",value:function(e){var t=arguments.length<=1||arguments[1]===undefined?{}:arguments[1];if(e){var i=this.playlist_,n=this.pendingSegment_;if(this.playlist_=e,this.xhrOptions_=t,this.hasPlayed_()||(e.syncInfo={mediaSequence:e.mediaSequence,time:0}),this.trigger("syncinfoupdate"),"INIT"===this.state&&this.couldBeginLoading_())return this.init_();if(!i||i.uri!==e.uri)return void(null!==this.mediaIndex&&this.resyncLoader());var r=e.mediaSequence-i.mediaSequence;this.logger_("mediaSequenceDiff",r),null!==this.mediaIndex&&(this.mediaIndex-=r),n&&(n.mediaIndex-=r,n.mediaIndex>=0&&(n.segment=e.segments[n.mediaIndex])),this.syncController_.saveExpiredSegmentInfo(i,e)}}},{key:"pause",value:function(){this.checkBufferTimeout_&&(y["default"].clearTimeout(this.checkBufferTimeout_),this.checkBufferTimeout_=null)}},{key:"paused",value:function(){return null===this.checkBufferTimeout_}},{key:"mimeType",value:function(e){this.mimeType_||(this.mimeType_=e,"INIT"===this.state&&this.couldBeginLoading_()&&this.init_())}},{key:"resetEverything",value:function(){this.ended_=!1,this.resetLoader(),this.remove(0,this.duration_()),this.trigger("reseteverything"),(0,T.resetKeyView)()}},{key:"resetLoader",value:function(){this.fetchAtBuffer_=!1,this.resyncLoader()}},{key:"resyncLoader",value:function(){this.mediaIndex=null,this.syncPoint_=null,this.abort()}},{key:"remove",value:function(e,t){isNaN(t)||(this.sourceUpdater_&&this.sourceUpdater_.remove(e,t),(0,v["default"])(e,t,this.segmentMetadataTrack_))}},{key:"monitorBuffer_",value:function(){this.checkBufferTimeout_&&y["default"].clearTimeout(this.checkBufferTimeout_),this.checkBufferTimeout_=y["default"].setTimeout(this.monitorBufferTick_.bind(this),1)}},{key:"monitorBufferTick_",value:function(){"READY"===this.state&&this.fillBuffer_(),this.checkBufferTimeout_&&y["default"].clearTimeout(this.checkBufferTimeout_),this.checkBufferTimeout_=y["default"].setTimeout(this.monitorBufferTick_.bind(this),500)}},{key:"fillBuffer_",value:function(){if(!this.sourceUpdater_.updating()){this.syncPoint_||(this.syncPoint_=this.syncController_.getSyncPoint(this.playlist_,this.duration_(),this.currentTimeline_,this.currentTime_()));var e=this.checkBuffer_(this.buffered_(),this.playlist_,this.mediaIndex,this.hasPlayed_(),this.currentTime_(),this.syncPoint_);if(e){if(k(this.playlist_,this.mediaSource_,e.mediaIndex))return void this.endOfStream();(e.mediaIndex!==this.playlist_.segments.length-1||"ended"!==this.mediaSource_.readyState||this.seeking_())&&((e.timeline!==this.currentTimeline_||null!==e.startOfSegment&&e.startOfSegment<this.sourceUpdater_.timestampOffset())&&(this.syncController_.reset(),e.timestampOffset=e.startOfSegment),this.loadSegment_(e))}}}},{key:"checkBuffer_",value:function(e,t,i,n,r,a){var s=0,o=undefined;e.length&&(s=e.end(e.length-1));var u=Math.max(0,s-r);if(!t.segments.length)return null;if(u>=this.goalBufferLength_())return null;if(!n&&u>=1)return null;if(u<.5?this.trigger("noLiveStream"):this.trigger("alive"),this.logger_("checkBuffer_","mediaIndex:",i,"hasPlayed:",n,"currentTime:",r,"syncPoint:",a,"fetchAtBuffer:",this.fetchAtBuffer_,"bufferedTime:",u),null===a)return i=this.getSyncSegmentCandidate_(t),this.logger_("getSync","mediaIndex:",i),this.generateSegmentInfo_(t,i,null,!0);if(null!==i){this.logger_("walkForward","mediaIndex:",i+1);var l=t.segments[i];return o=l&&l.end?l.end:s,this.generateSegmentInfo_(t,i+1,o,!1)}if(this.fetchAtBuffer_){var f=d["default"].getMediaInfoForTime(t,s,a.segmentIndex,a.time);i=f.mediaIndex,o=f.startTime}else{var f=d["default"].getMediaInfoForTime(t,r,a.segmentIndex,a.time);i=f.mediaIndex,o=f.startTime}return this.logger_("getMediaIndexForTime","mediaIndex:",i,"startOfSegment:",o),this.generateSegmentInfo_(t,i,o,!1)}},{key:"getSyncSegmentCandidate_",value:function(e){var t=this;if(-1===this.currentTimeline_)return 0;var i=e.segments.map(function(e,t){return{timeline:e.timeline,segmentIndex:t}}).filter(function(e){return e.timeline===t.currentTimeline_});return i.length?i[Math.min(i.length-1,1)].segmentIndex:Math.max(e.segments.length-1,0)}},{key:"generateSegmentInfo_",value:function(e,t,i,n){if(t<0||t>=e.segments.length)return null;var r=e.segments[t];return{requestId:"segment-loader-"+Math.random(),uri:r.resolvedUri,mediaIndex:t,isSyncRequest:n,startOfSegment:i,playlist:e,bytes:null,encryptedBytes:null,timestampOffset:null,timeline:r.timeline,duration:r.duration,segment:r}}},{key:"abortRequestEarly_",value:function(e){if(this.hls_.tech_.paused()||!this.xhrOptions_.timeout||!this.playlist_.attributes.BANDWIDTH)return!1;if(Date.now()-(e.firstBytesReceivedAt||Date.now())<1e3)return!1;var t=this.currentTime_(),i=e.bandwidth,n=this.pendingSegment_.duration,r=d["default"].estimateSegmentRequestTime(n,i,this.playlist_,e.bytesReceived),a=(0,S.timeUntilRebuffer)(this.buffered_(),t,this.hls_.tech_.playbackRate())-1;if(r<=a)return!1;var s=(0,w.minRebufferMaxBandwidthSelector)({master:this.hls_.playlists.master,currentTime:t,bandwidth:i,duration:this.duration_(),segmentDuration:n,timeUntilRebuffer:a,currentTimeline:this.currentTimeline_,syncController:this.syncController_});if(s){var o=r-a,u=o-s.rebufferingImpact,l=.5;return a<=S.TIME_FUDGE_FACTOR&&(l=1),!s.playlist||s.playlist.uri===this.playlist_.uri||u<l?!1:(this.bandwidth=s.playlist.attributes.BANDWIDTH*m["default"].BANDWIDTH_VARIANCE+1,this.abort(),this.trigger("earlyabort"),!0)}}},{key:"handleProgress_",value:function(e,t){this.pendingSegment_&&t.requestId===this.pendingSegment_.requestId&&!this.abortRequestEarly_(t.stats)&&this.trigger("progress")}},{key:"loadSegment_",value:function(e){this.state="WAITING",this.pendingSegment_=e,this.trimBackBuffer_(e),e.abortRequests=(0,T.mediaSegmentRequest)(this.hls_.xhr,this.xhrOptions_,this.decrypter_,this.hls_,this.createSimplifiedSegmentObj_(e),this.handleProgress_.bind(this),this.segmentRequestFinished_.bind(this))}},{key:"trimBackBuffer_",value:function(e){var t=A(this.seekable_(),this.currentTime_(),this.playlist_.targetDuration||10);t>0&&this.remove(0,t)}},{key:"createSimplifiedSegmentObj_",value:function(e){var t=e.segment,i={resolvedUri:t.resolvedUri,byterange:t.byterange,requestId:e.requestId};if(t.key){var n=t.key.iv||new Uint32Array([0,0,0,e.mediaIndex+e.playlist.mediaSequence]);i.key={resolvedUri:t.key.resolvedUri,iv:n},i.playlist={},e.playlist.keyFormat&&(i.playlist.keyFormat=e.playlist.keyFormat)}return t.map&&(i.map=this.initSegment(t.map)),i}},{key:"segmentRequestFinished_",value:function(e,t){if(this.mediaRequests+=1,t.stats&&(this.mediaBytesTransferred+=t.stats.bytesReceived,this.mediaTransferDuration+=t.stats.roundTripTime),!this.pendingSegment_)return void(this.mediaRequestsAborted+=1);if(t.requestId===this.pendingSegment_.requestId){if(e)return this.pendingSegment_=null,this.state="READY",e.code===T.REQUEST_ERRORS.ABORTED?void(this.mediaRequestsAborted+=1):(this.pause(),e.code===T.REQUEST_ERRORS.TIMEOUT?(this.mediaRequestsTimedout+=1,this.bandwidth=1,this.roundTrip=NaN,void this.trigger("bandwidthupdate")):(this.mediaRequestsErrored+=1,this.error(e),void this.trigger("error")));this.bandwidth=t.stats.bandwidth,this.roundTrip=t.stats.roundTripTime,t.map&&(t.map=this.initSegment(t.map,!0)),this.processSegmentResponse_(t)}}},{key:"processSegmentResponse_",value:function(e){var t=this.pendingSegment_;t.bytes=e.bytes,e.map&&(t.segment.map.bytes=e.map.bytes),t.endOfAllRequests=e.endOfAllRequests,this.handleSegment_()}},{key:"handleSegment_",value:function(){var e=this;if(!this.pendingSegment_)return void(this.state="READY");var t=this.pendingSegment_,i=t.segment,n=this.syncController_.probeSegmentInfo(t);"undefined"==typeof this.startingMedia_&&n&&(n.containsAudio||n.containsVideo)&&(this.startingMedia_={containsAudio:n.containsAudio,containsVideo:n.containsVideo});var r=O(this.loaderType_,this.startingMedia_,n);if(r)return this.error({message:r,blacklistDuration:Infinity}),void this.trigger("error");if(t.isSyncRequest)return this.trigger("syncinfoupdate"),this.pendingSegment_=null,void(this.state="READY");null!==t.timestampOffset&&t.timestampOffset!==this.sourceUpdater_.timestampOffset()&&(this.sourceUpdater_.timestampOffset(t.timestampOffset),this.trigger("timestampoffset"));var a=this.syncController_.mappingForTimeline(t.timeline);null!==a&&this.trigger({type:"segmenttimemapping",mapping:a}),this.state="APPENDING",i.map&&function(){var t=(0,b.initSegmentId)(i.map);if(!e.activeInitSegmentId_||e.activeInitSegmentId_!==t){var n=e.initSegment(i.map);e.sourceUpdater_.appendBuffer(n.bytes,function(){e.activeInitSegmentId_=t})}}(),t.byteLength=t.bytes.byteLength,"number"==typeof i.start&&"number"==typeof i.end?this.mediaSecondsLoaded+=i.end-i.start:this.mediaSecondsLoaded+=i.duration,this.sourceUpdater_.appendBuffer(t.bytes,this.handleUpdateEnd_.bind(this))}},{key:"handleUpdateEnd_",value:function(){if(this.logger_("handleUpdateEnd_","segmentInfo:",this.pendingSegment_),!this.pendingSegment_)return this.state="READY",void(this.paused()||this.monitorBuffer_());var e=this.pendingSegment_,t=e.segment,i=null!==this.mediaIndex;if(this.pendingSegment_=null,this.recordThroughput_(e),this.addSegmentMetadataCue_(e),this.state="READY",this.mediaIndex=e.mediaIndex,this.fetchAtBuffer_=!0,this.currentTimeline_=e.timeline,this.trigger("syncinfoupdate"),t.end&&this.currentTime_()-t.end>3*e.playlist.targetDuration)return void this.resetEverything();i&&this.trigger("bandwidthupdate"),this.trigger("progress"),k(e.playlist,this.mediaSource_,e.mediaIndex+1)&&this.endOfStream(),this.paused()||this.monitorBuffer_()}},{key:"recordThroughput_",value:function(e){var t=this.throughput.rate,i=Date.now()-e.endOfAllRequests+1,n=Math.floor(e.byteLength/i*8*1e3);this.throughput.rate+=(n-t)/++this.throughput.count}},{key:"logger_",value:function(){}},{key:"addSegmentMetadataCue_",value:function(e){if(this.segmentMetadataTrack_){var t=e.segment,i=t.start,n=t.end;if(E(i)&&E(n)){(0,v["default"])(i,n,this.segmentMetadataTrack_);var r=y["default"].WebKitDataCue||y["default"].VTTCue,a={bandwidth:e.playlist.attributes.BANDWIDTH,resolution:e.playlist.attributes.RESOLUTION,codecs:e.playlist.attributes.CODECS,byteLength:e.byteLength,uri:e.uri,timeline:e.timeline,playlist:e.playlist.uri,start:i,end:n},s=JSON.stringify(a),o=new r(i,n,s);o.value=a,this.segmentMetadataTrack_.addCue(o)}}}}]),t}(f["default"].EventTarget);i["default"]=L}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],18:[function(e,t,i){(function(e){"use strict";function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(i,"__esModule",{value:!0});var r=function(){function e(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,i,n){return i&&e(t.prototype,i),n&&e(t,n),t}}(),a="undefined"!=typeof window?window.videojs:void 0!==e?e.videojs:null,s=function(e){return e&&e.__esModule?e:{"default":e}}(a),o=function(){},u=function(){function e(t,i){var r=this;n(this,e);var a=function(){r.sourceBuffer_=t.addSourceBuffer(i),r.onUpdateendCallback_=function(){var e=r.pendingCallback_;r.pendingCallback_=null,e&&e(),r.runCallback_()},r.sourceBuffer_.addEventListener("updateend",r.onUpdateendCallback_),r.runCallback_()};this.callbacks_=[],this.pendingCallback_=null,this.timestampOffset_=0,this.mediaSource=t,this.processedAppend_=!1,"closed"===t.readyState?t.addEventListener("sourceopen",a):a()}return r(e,[{key:"abort",value:function(e){var t=this;this.processedAppend_&&this.queueCallback_(function(){t.sourceBuffer_.abort()},e)}},{key:"appendBuffer",value:function(e,t){var i=this;this.processedAppend_=!0,this.queueCallback_(function(){i.sourceBuffer_.appendBuffer(e)},t)}},{key:"buffered",value:function(){return this.sourceBuffer_?this.sourceBuffer_.buffered:s["default"].createTimeRanges()}},{key:"remove",value:function(e,t){var i=this;this.processedAppend_&&this.queueCallback_(function(){i.sourceBuffer_.remove(e,t)},o)}},{key:"updating",value:function(){return!this.sourceBuffer_||this.sourceBuffer_.updating||this.pendingCallback_}},{key:"timestampOffset",value:function(e){var t=this;return void 0!==e&&(this.queueCallback_(function(){t.sourceBuffer_.timestampOffset=e}),this.timestampOffset_=e),this.timestampOffset_}},{key:"queueCallback_",value:function(e,t){this.callbacks_.push([e.bind(this),t]),this.runCallback_()}},{key:"runCallback_",value:function(){var e=undefined;!this.updating()&&this.callbacks_.length&&(e=this.callbacks_.shift(),this.pendingCallback_=e[1],e[0]())}},{key:"dispose",value:function(){this.sourceBuffer_.removeEventListener("updateend",this.onUpdateendCallback_),this.sourceBuffer_&&"open"===this.mediaSource.readyState&&this.sourceBuffer_.abort()}}]),e}();i["default"]=u,t.exports=i["default"]}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],19:[function(e,t,i){(function(t){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function a(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}Object.defineProperty(i,"__esModule",{value:!0});var s=function(){function e(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,i,n){return i&&e(t.prototype,i),n&&e(t,n),t}}(),o=function(e,t,i){for(var n=!0;n;){var r=e,a=t,s=i;n=!1,null===r&&(r=Function.prototype);var o=Object.getOwnPropertyDescriptor(r,a);if(o!==undefined){if("value"in o)return o.value;var u=o.get;return u===undefined?undefined:u.call(s)}var d=Object.getPrototypeOf(r);if(null===d)return undefined;e=d,t=a,i=s,n=!0,o=d=undefined}},u=e(55),d=n(u),l=e(57),f=e(12),c="undefined"!=typeof window?window.videojs:void 0!==t?t.videojs:null,h=n(c),p=[{name:"VOD",run:function(e,t,i,n,r){if(i!==Infinity){return{time:0,segmentIndex:0}}return null}},{name:"ProgramDateTime",run:function(e,t,i,n,r){if(e.datetimeToDisplayTime&&t.dateTimeObject){return{time:t.dateTimeObject.getTime()/1e3+e.datetimeToDisplayTime,segmentIndex:0}}return null}},{name:"Segment",run:function(e,t,i,n,r){var a=t.segments||[],s=null,o=null;r=r||0;for(var u=0;u<a.length;u++){var d=a[u];if(d.timeline===n&&"undefined"!=typeof d.start){var l=Math.abs(r-d.start);if(null!==o&&o<l)break;(!s||null===o||o>=l)&&(o=l,s={time:d.start,segmentIndex:u})}}return s}},{name:"Discontinuity",run:function(e,t,i,n,r){var a=null;if(r=r||0,t.discontinuityStarts&&t.discontinuityStarts.length)for(var s=null,o=0;o<t.discontinuityStarts.length;o++){var u=t.discontinuityStarts[o],d=t.discontinuitySequence+o+1,l=e.discontinuities[d];if(l){var f=Math.abs(r-l.time);if(null!==s&&s<f)break;(!a||null===s||s>=f)&&(s=f,a={time:l.time,segmentIndex:u})}}return a}},{name:"Playlist",run:function(e,t,i,n,r){if(t.syncInfo){return{time:t.syncInfo.time,segmentIndex:t.syncInfo.mediaSequence-t.mediaSequence}}return null}}];i.syncPointStrategies=p;var m=function(e){function t(){var e=arguments.length<=0||arguments[0]===undefined?{}:arguments[0];r(this,t),o(Object.getPrototypeOf(t.prototype),"constructor",this).call(this),this.inspectCache_=undefined,this.timelines=[],this.discontinuities=[],this.datetimeToDisplayTime=null,e.debug&&(this.logger_=h["default"].log.bind(h["default"],"sync-controller ->"))}return a(t,e),s(t,[{key:"getSyncPoint",value:function(e,t,i,n){var r=this.runStrategies_(e,t,i,n);return r.length?this.selectSyncPoint_(r,{key:"time",value:n}):null}},{key:"getExpiredTime",value:function(e,t){if(!e||!e.segments)return null;var i=this.runStrategies_(e,t,e.discontinuitySequence,0);if(!i.length)return null;var n=this.selectSyncPoint_(i,{key:"segmentIndex",value:0});return n.segmentIndex>0&&(n.time*=-1),Math.abs(n.time+(0,f.sumDurations)(e,n.segmentIndex,0))}},{key:"runStrategies_",value:function(e,t,i,n){for(var r=[],a=0;a<p.length;a++){var s=p[a],o=s.run(this,e,t,i,n);o&&(o.strategy=s.name,r.push({strategy:s.name,syncPoint:o}),this.logger_("syncPoint found via <"+s.name+">:",o))}return r}},{key:"selectSyncPoint_",value:function(e,t){for(var i=e[0].syncPoint,n=Math.abs(e[0].syncPoint[t.key]-t.value),r=e[0].strategy,a=1;a<e.length;a++){var s=Math.abs(e[a].syncPoint[t.key]-t.value);s<n&&(n=s,i=e[a].syncPoint,r=e[a].strategy)}return this.logger_("syncPoint with strategy <"+r+"> chosen: ",i),i}},{key:"saveExpiredSegmentInfo",value:function(e,t){for(var i=t.mediaSequence-e.mediaSequence,n=i-1;n>=0;n--){var r=e.segments[n];if(r&&"undefined"!=typeof r.start){t.syncInfo={mediaSequence:e.mediaSequence+n,time:r.start},this.logger_("playlist sync:",t.syncInfo),this.trigger("syncinfoupdate");break}}}},{key:"setDateTimeMapping",value:function(e){if(!this.datetimeToDisplayTime&&e.dateTimeObject){var t=e.dateTimeObject.getTime()/1e3;this.datetimeToDisplayTime=-t}}},{key:"reset",value:function(){this.inspectCache_=undefined}},{key:"probeSegmentInfo",value:function(e){var t=e.segment,i=e.playlist,n=undefined;return n=t.map?this.probeMp4Segment_(e):this.probeTsSegment_(e),n&&this.calculateSegmentTimeMapping_(e,n)&&(this.saveDiscontinuitySyncInfo_(e),i.syncInfo||(i.syncInfo={mediaSequence:i.mediaSequence+e.mediaIndex,time:t.start})),n}},{key:"probeMp4Segment_",value:function(e){var t=e.segment,i=d["default"].timescale(t.map.bytes),n=d["default"].startTime(i,e.bytes);return null!==e.timestampOffset&&(e.timestampOffset-=n),{start:n,end:n+t.duration}}},{key:"probeTsSegment_",value:function(e){var t=(0,l.inspect)(e.bytes,this.inspectCache_),i=undefined,n=undefined;return t?(t.video&&2===t.video.length?(this.inspectCache_=t.video[1].dts,i=t.video[0].dtsTime,n=t.video[1].dtsTime):t.audio&&2===t.audio.length&&(this.inspectCache_=t.audio[1].dts,i=t.audio[0].dtsTime,n=t.audio[1].dtsTime),{start:i,end:n,containsVideo:t.video&&2===t.video.length,containsAudio:t.audio&&2===t.audio.length}):null}},{key:"timestampOffsetForTimeline",value:function(e){return"undefined"==typeof this.timelines[e]?null:this.timelines[e].time}},{key:"mappingForTimeline",value:function(e){return"undefined"==typeof this.timelines[e]?null:this.timelines[e].mapping}},{key:"calculateSegmentTimeMapping_",value:function(e,t){var i=e.segment,n=this.timelines[e.timeline];if(null!==e.timestampOffset)this.logger_("tsO:",e.timestampOffset),n={time:e.startOfSegment,mapping:e.startOfSegment-t.start},this.timelines[e.timeline]=n,this.trigger("timestampoffset"),i.start=e.startOfSegment,i.end=t.end+n.mapping;else{if(!n)return!1;i.start=t.start+n.mapping,i.end=t.end+n.mapping}return!0}},{key:"saveDiscontinuitySyncInfo_",value:function(e){var t=e.playlist,i=e.segment;if(i.discontinuity)this.discontinuities[i.timeline]={time:i.start,accuracy:0};else if(t.discontinuityStarts.length)for(var n=0;n<t.discontinuityStarts.length;n++){var r=t.discontinuityStarts[n],a=t.discontinuitySequence+n+1,s=r-e.mediaIndex,o=Math.abs(s);if(!this.discontinuities[a]||this.discontinuities[a].accuracy>o){var u=undefined;u=s<0?i.start-(0,f.sumDurations)(t,e.mediaIndex,r):i.end+(0,f.sumDurations)(t,e.mediaIndex+1,r),this.discontinuities[a]={time:u,accuracy:o}}}}},{key:"logger_",value:function(){}}]),t}(h["default"].EventTarget);i["default"]=m}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],20:[function(e,t,i){"use strict";Object.defineProperty(i,"__esModule",{value:!0});var n=function(){var e=arguments.length<=0||arguments[0]===undefined?"":arguments[0],t={codecCount:0},i=undefined;return t.codecCount=e.split(",").length,t.codecCount=t.codecCount||2,i=/(^|\s|,)+(avc1)([^ ,]*)/i.exec(e),i&&(t.videoCodec=i[2],t.videoObjectTypeIndicator=i[3]),t.audioProfile=/(^|\s|,)+mp4a.[0-9A-Fa-f]+\.([0-9A-Fa-f]+)/i.exec(e),t.audioProfile=t.audioProfile&&t.audioProfile[2],t};i.parseCodecs=n},{}],21:[function(e,t,i){(function(n){"use strict";function r(e){return e&&e.__esModule?e:{"default":e}}function a(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function s(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}Object.defineProperty(i,"__esModule",{value:!0});var o=function(){function e(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,i,n){return i&&e(t.prototype,i),n&&e(t,n),t}}(),u=function(e,t,i){for(var n=!0;n;){var r=e,a=t,s=i;n=!1,null===r&&(r=Function.prototype);var o=Object.getOwnPropertyDescriptor(r,a);if(o!==undefined){if("value"in o)return o.value;var u=o.get;return u===undefined?undefined:u.call(s)}var d=Object.getPrototypeOf(r);if(null===d)return undefined;e=d,t=a,i=s,n=!0,o=d=undefined}},d=e(17),l=r(d),f="undefined"!=typeof window?window.videojs:void 0!==n?n.videojs:null,c=r(f),h=e(30),p=r(h),m=e(73),g=r(m),y=e(3),_=new Uint8Array("\n\n".split("").map(function(e){return e.charCodeAt(0)})),v=function(e){return String.fromCharCode.apply(null,e)},b=function(e){function t(e){var i=arguments.length<=1||arguments[1]===undefined?{}:arguments[1];a(this,t),u(Object.getPrototypeOf(t.prototype),"constructor",this).call(this,e,i),this.mediaSource_=null,this.subtitlesTrack_=null}return s(t,e),o(t,[{key:"buffered_",value:function(){if(!this.subtitlesTrack_||!this.subtitlesTrack_.cues.length)return c["default"].createTimeRanges();var e=this.subtitlesTrack_.cues,t=e[0].startTime,i=e[e.length-1].startTime;return c["default"].createTimeRanges([[t,i]])}},{key:"initSegment",value:function(e){var t=!(arguments.length<=1||arguments[1]===undefined)&&arguments[1];if(!e)return null;var i=(0,y.initSegmentId)(e),n=this.initSegments_[i];if(t&&!n&&e.bytes){var r=_.byteLength+e.bytes.byteLength,a=new Uint8Array(r);a.set(e.bytes),a.set(_,e.bytes.byteLength),this.initSegments_[i]=n={resolvedUri:e.resolvedUri,byterange:e.byterange,bytes:a}}return n||e}},{key:"couldBeginLoading_",value:function(){return this.playlist_&&this.subtitlesTrack_&&!this.paused()}},{key:"init_",value:function(){return this.state="READY",this.resetEverything(),this.monitorBuffer_()}},{key:"track",value:function(e){return void 0===e?this.subtitlesTrack_:(this.subtitlesTrack_=e,"INIT"===this.state&&this.couldBeginLoading_()&&this.init_(),this.subtitlesTrack_)}},{key:"remove",value:function(e,t){(0,g["default"])(e,t,this.subtitlesTrack_)}},{key:"fillBuffer_",value:function(){var e=this;this.syncPoint_||(this.syncPoint_=this.syncController_.getSyncPoint(this.playlist_,this.duration_(),this.currentTimeline_,this.currentTime_()));var t=this.checkBuffer_(this.buffered_(),this.playlist_,this.mediaIndex,this.hasPlayed_(),this.currentTime_(),this.syncPoint_);if(t=this.skipEmptySegments_(t)){if(null===this.syncController_.timestampOffsetForTimeline(t.timeline)){var i=function(){e.state="READY",e.paused()||e.monitorBuffer_()};return this.syncController_.one("timestampoffset",i),void(this.state="WAITING_ON_TIMELINE")}this.loadSegment_(t)}}},{key:"skipEmptySegments_",value:function(e){for(;e&&e.segment.empty;)e=this.generateSegmentInfo_(e.playlist,e.mediaIndex+1,e.startOfSegment+e.duration,e.isSyncRequest);return e}},{key:"handleSegment_",value:function(){var e=this;if(!this.pendingSegment_||!this.subtitlesTrack_)return void(this.state="READY");this.state="APPENDING";var t=this.pendingSegment_,i=t.segment;if("function"!=typeof p["default"].WebVTT&&this.subtitlesTrack_&&this.subtitlesTrack_.tech_){var n=function(){var t=function(){e.handleSegment_()};return e.state="WAITING_ON_VTTJS",e.subtitlesTrack_.tech_.one("vttjsloaded",t),e.subtitlesTrack_.tech_.one("vttjserror",function(){e.subtitlesTrack_.tech_.off("vttjsloaded",t),e.error({message:"Error loading vtt.js"}),e.state="READY",e.pause(),e.trigger("error")}),
{v:undefined}}();if("object"==typeof n)return n.v}i.requested=!0;try{this.parseVTTCues_(t)}catch(r){return this.error({message:r.message}),this.state="READY",this.pause(),this.trigger("error")}if(this.updateTimeMapping_(t,this.syncController_.timelines[t.timeline],this.playlist_),t.isSyncRequest)return this.trigger("syncinfoupdate"),this.pendingSegment_=null,void(this.state="READY");t.byteLength=t.bytes.byteLength,this.mediaSecondsLoaded+=i.duration,t.cues.length&&this.remove(t.cues[0].endTime,t.cues[t.cues.length-1].endTime),t.cues.forEach(function(t){e.subtitlesTrack_.addCue(t)}),this.handleUpdateEnd_()}},{key:"parseVTTCues_",value:function(e){var t=undefined,i=!1;"function"==typeof p["default"].TextDecoder?t=new p["default"].TextDecoder("utf8"):(t=p["default"].WebVTT.StringDecoder(),i=!0);var n=new p["default"].WebVTT.Parser(p["default"],p["default"].vttjs,t);if(e.cues=[],e.timestampmap={MPEGTS:0,LOCAL:0},n.oncue=e.cues.push.bind(e.cues),n.ontimestampmap=function(t){return e.timestampmap=t},n.onparsingerror=function(e){c["default"].log.warn("Error encountered when parsing cues: "+e.message)},e.segment.map){var r=e.segment.map.bytes;i&&(r=v(r)),n.parse(r)}var a=e.bytes;i&&(a=v(a)),n.parse(a),n.flush()}},{key:"updateTimeMapping_",value:function(e,t,i){var n=e.segment;if(t){if(!e.cues.length)return void(n.empty=!0);var r=e.timestampmap,a=r.MPEGTS/9e4-r.LOCAL+t.mapping;if(e.cues.forEach(function(e){e.startTime+=a,e.endTime+=a}),!i.syncInfo){var s=e.cues[0].startTime,o=e.cues[e.cues.length-1].startTime;i.syncInfo={mediaSequence:i.mediaSequence+e.mediaIndex,time:Math.min(s,o-n.duration)}}}}}]),t}(l["default"]);i["default"]=b,t.exports=i["default"]}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],22:[function(e,t,i){(function(e){"use strict";Object.defineProperty(i,"__esModule",{value:!0});var n="undefined"!=typeof window?window.videojs:void 0!==e?e.videojs:null,r=function(e){return e&&e.__esModule?e:{"default":e}}(n),a=function(){return function e(t,i){t=(0,n.mergeOptions)({timeout:45e3},t);var a=e.beforeRequest||r["default"].Hls.xhr.beforeRequest;if(a&&"function"==typeof a){var s=a(t);s&&(t=s)}var o=(0,n.xhr)(t,function(e,t){var n=o.response;!e&&n&&(o.responseTime=Date.now(),o.roundTripTime=o.responseTime-o.requestTime,o.bytesReceived=n.byteLength||n.length,o.bandwidth||(o.bandwidth=Math.floor(o.bytesReceived/o.roundTripTime*8*1e3))),e&&"ETIMEDOUT"===e.code&&(o.timedout=!0),e||o.aborted||200===t.statusCode||206===t.statusCode||0===t.statusCode||(e=new Error("XHR Failed with a response of: "+(o&&(n||o.responseText)))),i(e,o)}),u=o.abort;return o.abort=function(){return o.aborted=!0,u.apply(o,arguments)},o.uri=t.uri,o.requestTime=Date.now(),o}};i["default"]=a,t.exports=i["default"]}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],23:[function(e,t,i){"use strict";function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(i,"__esModule",{value:!0});var r=function(){function e(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,i,n){return i&&e(t.prototype,i),n&&e(t,n),t}}(),a=function(){var e=[[[],[],[],[],[]],[[],[],[],[],[]]],t=e[0],i=e[1],n=t[4],r=i[4],a=undefined,s=undefined,o=undefined,u=[],d=[],l=undefined,f=undefined,c=undefined,h=undefined,p=undefined,m=undefined;for(a=0;a<256;a++)d[(u[a]=a<<1^283*(a>>7))^a]=a;for(s=o=0;!n[s];s^=l||1,o=d[o]||1)for(h=o^o<<1^o<<2^o<<3^o<<4,h=h>>8^255&h^99,n[s]=h,r[h]=s,c=u[f=u[l=u[s]]],m=16843009*c^65537*f^257*l^16843008*s,p=257*u[h]^16843008*h,a=0;a<4;a++)t[a][s]=p=p<<24^p>>>8,i[a][h]=m=m<<24^m>>>8;for(a=0;a<5;a++)t[a]=t[a].slice(0),i[a]=i[a].slice(0);return e},s=null,o=function(){function e(t){n(this,e),s||(s=a()),this._tables=[[s[0][0].slice(),s[0][1].slice(),s[0][2].slice(),s[0][3].slice(),s[0][4].slice()],[s[1][0].slice(),s[1][1].slice(),s[1][2].slice(),s[1][3].slice(),s[1][4].slice()]];var i=undefined,r=undefined,o=undefined,u=undefined,d=undefined,l=this._tables[0][4],f=this._tables[1],c=t.length,h=1;if(4!==c&&6!==c&&8!==c)throw new Error("Invalid aes key size");for(u=t.slice(0),d=[],this._key=[u,d],i=c;i<4*c+28;i++)o=u[i-1],(i%c==0||8===c&&i%c==4)&&(o=l[o>>>24]<<24^l[o>>16&255]<<16^l[o>>8&255]<<8^l[255&o],i%c==0&&(o=o<<8^o>>>24^h<<24,h=h<<1^283*(h>>7))),u[i]=u[i-c]^o;for(r=0;i;r++,i--)o=u[3&r?i:i-4],d[r]=i<=4||r<4?o:f[0][l[o>>>24]]^f[1][l[o>>16&255]]^f[2][l[o>>8&255]]^f[3][l[255&o]]}return r(e,[{key:"decrypt",value:function(e,t,i,n,r,a){var s=this._key[1],o=e^s[0],u=n^s[1],d=i^s[2],l=t^s[3],f=undefined,c=undefined,h=undefined,p=s.length/4-2,m=undefined,g=4,y=this._tables[1],_=y[0],v=y[1],b=y[2],T=y[3],S=y[4];for(m=0;m<p;m++)f=_[o>>>24]^v[u>>16&255]^b[d>>8&255]^T[255&l]^s[g],c=_[u>>>24]^v[d>>16&255]^b[l>>8&255]^T[255&o]^s[g+1],h=_[d>>>24]^v[l>>16&255]^b[o>>8&255]^T[255&u]^s[g+2],l=_[l>>>24]^v[o>>16&255]^b[u>>8&255]^T[255&d]^s[g+3],g+=4,o=f,u=c,d=h;for(m=0;m<4;m++)r[(3&-m)+a]=S[o>>>24]<<24^S[u>>16&255]<<16^S[d>>8&255]<<8^S[255&l]^s[g++],f=o,o=u,u=d,d=l,l=f}}]),e}();i["default"]=o,t.exports=i["default"]},{}],24:[function(e,t,i){"use strict";function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function r(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}Object.defineProperty(i,"__esModule",{value:!0});var a=function(){function e(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,i,n){return i&&e(t.prototype,i),n&&e(t,n),t}}(),s=function(e,t,i){for(var n=!0;n;){var r=e,a=t,s=i;n=!1,null===r&&(r=Function.prototype);var o=Object.getOwnPropertyDescriptor(r,a);if(o!==undefined){if("value"in o)return o.value;var u=o.get;return u===undefined?undefined:u.call(s)}var d=Object.getPrototypeOf(r);if(null===d)return undefined;e=d,t=a,i=s,n=!0,o=d=undefined}},o=e(27),u=function(e){return e&&e.__esModule?e:{"default":e}}(o),d=function(e){function t(){n(this,t),s(Object.getPrototypeOf(t.prototype),"constructor",this).call(this,u["default"]),this.jobs=[],this.delay=1,this.timeout_=null}return r(t,e),a(t,[{key:"processJob_",value:function(){this.jobs.shift()(),this.jobs.length?this.timeout_=setTimeout(this.processJob_.bind(this),this.delay):this.timeout_=null}},{key:"push",value:function(e){this.jobs.push(e),this.timeout_||(this.timeout_=setTimeout(this.processJob_.bind(this),this.delay))}}]),t}(u["default"]);i["default"]=d,t.exports=i["default"]},{}],25:[function(e,t,i){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(i,"__esModule",{value:!0});var a=function(){function e(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,i,n){return i&&e(t.prototype,i),n&&e(t,n),t}}(),s=e(23),o=n(s),u=e(24),d=n(u),l=e(62),f=function(e){return e<<24|(65280&e)<<8|(16711680&e)>>8|e>>>24},c=function(e,t,i){var n=new Int32Array(e.buffer,e.byteOffset,e.byteLength>>2),r=new o["default"](Array.prototype.slice.call(t)),a=new Uint8Array(e.byteLength),s=new Int32Array(a.buffer),u=undefined,d=undefined,l=undefined,c=undefined,h=undefined,p=undefined,m=undefined,g=undefined,y=undefined;for(u=i[0],d=i[1],l=i[2],c=i[3],y=0;y<n.length;y+=4)h=f(n[y]),p=f(n[y+1]),m=f(n[y+2]),g=f(n[y+3]),r.decrypt(h,p,m,g,s,y),s[y]=f(s[y]^u),s[y+1]=f(s[y+1]^d),s[y+2]=f(s[y+2]^l),s[y+3]=f(s[y+3]^c),u=h,d=p,l=m,c=g;return a};i.decrypt=c;var h=function(){function e(t,i,n,a){r(this,e);var s=e.STEP,o=new Int32Array(t.buffer),u=new Uint8Array(t.byteLength),c=0;for(this.asyncStream_=new d["default"],this.asyncStream_.push(this.decryptChunk_(o.subarray(c,c+s),i,n,u)),c=s;c<o.length;c+=s)n=new Uint32Array([f(o[c-4]),f(o[c-3]),f(o[c-2]),f(o[c-1])]),this.asyncStream_.push(this.decryptChunk_(o.subarray(c,c+s),i,n,u));this.asyncStream_.push(function(){a(null,(0,l.unpad)(u))})}return a(e,[{key:"decryptChunk_",value:function(e,t,i,n){return function(){var r=c(e,t,i);n.set(r,e.byteOffset)}}}],[{key:"STEP",get:function(){return 32e3}}]),e}();i.Decrypter=h,i["default"]={Decrypter:h,decrypt:c}},{}],26:[function(e,t,i){"use strict";Object.defineProperty(i,"__esModule",{value:!0});var n=e(25),r=e(24),a=function(e){return e&&e.__esModule?e:{"default":e}}(r);i["default"]={decrypt:n.decrypt,Decrypter:n.Decrypter,AsyncStream:a["default"]},t.exports=i["default"]},{}],27:[function(e,t,i){"use strict";function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(i,"__esModule",{value:!0});var r=function(){function e(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,i,n){return i&&e(t.prototype,i),n&&e(t,n),t}}(),a=function(){function e(){n(this,e),this.listeners={}}return r(e,[{key:"on",value:function(e,t){this.listeners[e]||(this.listeners[e]=[]),this.listeners[e].push(t)}},{key:"off",value:function(e,t){var i=undefined;return!!this.listeners[e]&&(i=this.listeners[e].indexOf(t),this.listeners[e].splice(i,1),i>-1)}},{key:"trigger",value:function(e){var t=undefined,i=undefined,n=undefined,r=undefined;if(t=this.listeners[e])if(2===arguments.length)for(n=t.length,i=0;i<n;++i)t[i].call(this,arguments[1]);else for(r=Array.prototype.slice.call(arguments,1),n=t.length,i=0;i<n;++i)t[i].apply(this,r)}},{key:"dispose",value:function(){this.listeners={}}},{key:"pipe",value:function(e){this.on("data",function(t){e.push(t)})}}]),e}();i["default"]=a,t.exports=i["default"]},{}],28:[function(e,t,i){},{}],29:[function(e,t,i){(function(i){var n,r=void 0!==i?i:"undefined"!=typeof window?window:{},a=e(28);"undefined"!=typeof document?n=document:(n=r["__GLOBAL_DOCUMENT_CACHE@4"])||(n=r["__GLOBAL_DOCUMENT_CACHE@4"]=a),t.exports=n}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],30:[function(e,t,i){(function(e){var i;i="undefined"!=typeof window?window:void 0!==e?e:"undefined"!=typeof self?self:{},t.exports=i}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],31:[function(e,t,i){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}var r=e(32),a=n(r),s=e(33),o=n(s),u=e(34),d=n(u);t.exports={LineStream:a["default"],ParseStream:o["default"],Parser:d["default"]}},{}],32:[function(e,t,i){"use strict";function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function r(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function a(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}Object.defineProperty(i,"__esModule",{value:!0});var s=function(){function e(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,i,n){return i&&e(t.prototype,i),n&&e(t,n),t}}(),o=e(35),u=function(e){return e&&e.__esModule?e:{"default":e}}(o),d=function(e){function t(){n(this,t);var e=r(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));return e.buffer="",e}return a(t,e),s(t,[{key:"push",value:function(e){var t=void 0;for(this.buffer+=e,t=this.buffer.indexOf("\n");t>-1;t=this.buffer.indexOf("\n"))this.trigger("data",this.buffer.substring(0,t)),this.buffer=this.buffer.substring(t+1)}}]),t}(u["default"]);i["default"]=d},{}],33:[function(e,t,i){"use strict";function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function r(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function a(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}Object.defineProperty(i,"__esModule",{value:!0});var s=function(){function e(e,t){var i=[],n=!0,r=!1,a=undefined;try{for(var s,o=e[Symbol.iterator]();!(n=(s=o.next()).done)&&(i.push(s.value),!t||i.length!==t);n=!0);}catch(u){r=!0,a=u}finally{try{!n&&o["return"]&&o["return"]()}finally{if(r)throw a}}return i}return function(t,i){if(Array.isArray(t))return t;if(Symbol.iterator in Object(t))return e(t,i);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),o=function(){function e(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,i,n){return i&&e(t.prototype,i),n&&e(t,n),t}}(),u=e(35),d=function(e){return e&&e.__esModule?e:{"default":e}}(u),l=function(){return new RegExp('(?:^|,)((?:[^=]*)=(?:"[^"]*"|[^,]*))')},f=function(e){for(var t=e.split(l()),i={},n=t.length,r=void 0;n--;)""!==t[n]&&(r=/([^=]*)=(.*)/.exec(t[n]).slice(1),r[0]=r[0].replace(/^\s+|\s+$/g,""),r[1]=r[1].replace(/^\s+|\s+$/g,""),r[1]=r[1].replace(/^['"](.*)['"]$/g,"$1"),i[r[0]]=r[1]);return i},c=function(e){function t(){return n(this,t),r(this,(t.__proto__||Object.getPrototypeOf(t)).call(this))}return a(t,e),o(t,[{key:"push",value:function(e){var t=void 0,i=void 0;if(e=e.replace(/^[\u0000\s]+|[\u0000\s]+$/g,""),0!==e.length){if("#"!==e[0])return void this.trigger("data",{type:"uri",uri:e});if(0!==e.indexOf("#EXT"))return void this.trigger("data",{type:"comment",text:e.slice(1)});if(e=e.replace("\r",""),t=/^#EXTM3U/.exec(e))return void this.trigger("data",{type:"tag",tagType:"m3u"});if(t=/^#EXTINF:?([0-9\.]*)?,?(.*)?$/.exec(e))return i={type:"tag",tagType:"inf"},t[1]&&(i.duration=parseFloat(t[1])),t[2]&&(i.title=t[2]),void this.trigger("data",i);if(t=/^#EXT-X-TARGETDURATION:?([0-9.]*)?/.exec(e))return i={type:"tag",tagType:"targetduration"},t[1]&&(i.duration=parseInt(t[1],10)),void this.trigger("data",i);if(t=/^#ZEN-TOTAL-DURATION:?([0-9.]*)?/.exec(e))return i={type:"tag",tagType:"totalduration"},t[1]&&(i.duration=parseInt(t[1],10)),void this.trigger("data",i);if(t=/^#EXT-X-VERSION:?([0-9.]*)?/.exec(e))return i={type:"tag",tagType:"version"},t[1]&&(i.version=parseInt(t[1],10)),void this.trigger("data",i);if(t=/^#EXT-X-MEDIA-SEQUENCE:?(\-?[0-9.]*)?/.exec(e))return i={type:"tag",tagType:"media-sequence"},t[1]&&(i.number=parseInt(t[1],10)),void this.trigger("data",i);if(t=/^#EXT-X-DISCONTINUITY-SEQUENCE:?(\-?[0-9.]*)?/.exec(e))return i={type:"tag",tagType:"discontinuity-sequence"},t[1]&&(i.number=parseInt(t[1],10)),void this.trigger("data",i);if(t=/^#EXT-X-PLAYLIST-TYPE:?(.*)?$/.exec(e))return i={type:"tag",tagType:"playlist-type"},t[1]&&(i.playlistType=t[1]),void this.trigger("data",i);if(t=/^#EXT-X-BYTERANGE:?([0-9.]*)?@?([0-9.]*)?/.exec(e))return i={type:"tag",tagType:"byterange"},t[1]&&(i.length=parseInt(t[1],10)),t[2]&&(i.offset=parseInt(t[2],10)),void this.trigger("data",i);if(t=/^#EXT-X-ALLOW-CACHE:?(YES|NO)?/.exec(e))return i={type:"tag",tagType:"allow-cache"},t[1]&&(i.allowed=!/NO/.test(t[1])),void this.trigger("data",i);if(t=/^#EXT-X-MAP:?(.*)$/.exec(e)){if(i={type:"tag",tagType:"map"},t[1]){var n=f(t[1]);if(n.URI&&(i.uri=n.URI),n.BYTERANGE){var r=n.BYTERANGE.split("@"),a=s(r,2),o=a[0],u=a[1];i.byterange={},o&&(i.byterange.length=parseInt(o,10)),u&&(i.byterange.offset=parseInt(u,10))}}return void this.trigger("data",i)}if(t=/^#EXT-X-STREAM-INF:?(.*)$/.exec(e)){if(i={type:"tag",tagType:"stream-inf"},t[1]){if(i.attributes=f(t[1]),i.attributes.RESOLUTION){var d=i.attributes.RESOLUTION.split("x"),l={};d[0]&&(l.width=parseInt(d[0],10)),d[1]&&(l.height=parseInt(d[1],10)),i.attributes.RESOLUTION=l}i.attributes.BANDWIDTH&&(i.attributes.BANDWIDTH=parseInt(i.attributes.BANDWIDTH,10)),i.attributes["PROGRAM-ID"]&&(i.attributes["PROGRAM-ID"]=parseInt(i.attributes["PROGRAM-ID"],10))}return void this.trigger("data",i)}if(t=/^#EXT-X-MEDIA:?(.*)$/.exec(e))return i={type:"tag",tagType:"media"},t[1]&&(i.attributes=f(t[1])),void this.trigger("data",i);if(t=/^#EXT-X-ENDLIST/.exec(e))return void this.trigger("data",{type:"tag",tagType:"endlist"});if(t=/^#EXT-X-DISCONTINUITY/.exec(e))return void this.trigger("data",{type:"tag",tagType:"discontinuity"});if(t=/^#EXT-X-PROGRAM-DATE-TIME:?(.*)$/.exec(e))return i={type:"tag",tagType:"program-date-time"},t[1]&&(i.dateTimeString=t[1],i.dateTimeObject=new Date(t[1])),void this.trigger("data",i);if(t=/^#EXT-X-KEY:?(.*)$/.exec(e))return i={type:"tag",tagType:"key"},t[1]&&(i.attributes=f(t[1]),i.attributes.IV&&("0x"===i.attributes.IV.substring(0,2).toLowerCase()&&(i.attributes.IV=i.attributes.IV.substring(2)),i.attributes.IV=i.attributes.IV.match(/.{8}/g),i.attributes.IV[0]=parseInt(i.attributes.IV[0],16),i.attributes.IV[1]=parseInt(i.attributes.IV[1],16),i.attributes.IV[2]=parseInt(i.attributes.IV[2],16),i.attributes.IV[3]=parseInt(i.attributes.IV[3],16),i.attributes.IV=new Uint32Array(i.attributes.IV))),void this.trigger("data",i);if(t=/^#EXT-X-CUE-OUT-CONT:?(.*)?$/.exec(e))return i={type:"tag",tagType:"cue-out-cont"},t[1]?i.data=t[1]:i.data="",void this.trigger("data",i);if(t=/^#EXT-X-CUE-OUT:?(.*)?$/.exec(e))return i={type:"tag",tagType:"cue-out"},t[1]?i.data=t[1]:i.data="",void this.trigger("data",i);if(t=/^#EXT-X-CUE-IN:?(.*)?$/.exec(e))return i={type:"tag",tagType:"cue-in"},t[1]?i.data=t[1]:i.data="",void this.trigger("data",i);this.trigger("data",{type:"tag",data:e.slice(4)})}}}]),t}(d["default"]);i["default"]=c},{}],34:[function(e,t,i){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function a(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function s(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}Object.defineProperty(i,"__esModule",{value:!0});var o=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var i=arguments[t];for(var n in i)Object.prototype.hasOwnProperty.call(i,n)&&(e[n]=i[n])}return e},u=function(){function e(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,i,n){return i&&e(t.prototype,i),n&&e(t,n),t}}(),d=e(35),l=n(d),f=e(32),c=n(f),h=e(33),p=n(h),m=function(e){function t(){r(this,t);var e=a(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));e.lineStream=new c["default"],e.parseStream=new p["default"],e.lineStream.pipe(e.parseStream);var i=e,n=[],s={},u=void 0,d=void 0,l=function(){},f={AUDIO:{},VIDEO:{},"CLOSED-CAPTIONS":{},SUBTITLES:{}},h=0;return e.manifest={allowCache:!0,discontinuityStarts:[],segments:[]},e.parseStream.on("data",function(e){var t=void 0,r=void 0;({tag:function(){(({"allow-cache":function(){this.manifest.allowCache=e.allowed,"allowed"in e||(this.trigger("info",{message:"defaulting allowCache to YES"}),this.manifest.allowCache=!0)},byterange:function(){var t={};"length"in e&&(s.byterange=t,t.length=e.length,"offset"in e||(this.trigger("info",{message:"defaulting offset to zero"}),e.offset=0)),"offset"in e&&(s.byterange=t,t.offset=e.offset)},endlist:function(){this.manifest.endList=!0},inf:function(){"mediaSequence"in this.manifest||(this.manifest.mediaSequence=0,this.trigger("info",{message:"defaulting media sequence to zero"})),"discontinuitySequence"in this.manifest||(this.manifest.discontinuitySequence=0,this.trigger("info",{message:"defaulting discontinuity sequence to zero"})),e.duration>0&&(s.duration=e.duration),0===e.duration&&(s.duration=.01,this.trigger("info",{message:"updating zero segment duration to a small value"})),this.manifest.segments=n},key:function(){return e.attributes?"NONE"===e.attributes.METHOD?void(d=null):e.attributes.URI?(e.attributes.METHOD||this.trigger("warn",{message:"defaulting key method to AES-128"}),d={method:e.attributes.METHOD||"AES-128",uri:e.attributes.URI},void("undefined"!=typeof e.attributes.IV&&(d.iv=e.attributes.IV))):void this.trigger("warn",{message:"ignoring key declaration without URI"}):void this.trigger("warn",{message:"ignoring key declaration without attribute list"})},"media-sequence":function(){if(!isFinite(e.number))return void this.trigger("warn",{message:"ignoring invalid media sequence: "+e.number});this.manifest.mediaSequence=e.number},"discontinuity-sequence":function(){if(!isFinite(e.number))return void this.trigger("warn",{message:"ignoring invalid discontinuity sequence: "+e.number});this.manifest.discontinuitySequence=e.number,h=e.number},"playlist-type":function(){if(!/VOD|EVENT/.test(e.playlistType))return void this.trigger("warn",{message:"ignoring unknown playlist type: "+e.playlist});this.manifest.playlistType=e.playlistType},map:function(){u={},e.uri&&(u.uri=e.uri),e.byterange&&(u.byterange=e.byterange)},"stream-inf":function(){if(this.manifest.playlists=n,this.manifest.mediaGroups=this.manifest.mediaGroups||f,!e.attributes)return void this.trigger("warn",{message:"ignoring empty stream-inf attributes"});s.attributes||(s.attributes={}),o(s.attributes,e.attributes)},media:function(){if(this.manifest.mediaGroups=this.manifest.mediaGroups||f,!(e.attributes&&e.attributes.TYPE&&e.attributes["GROUP-ID"]&&e.attributes.NAME))return void this.trigger("warn",{message:"ignoring incomplete or missing media group"});var i=this.manifest.mediaGroups[e.attributes.TYPE];i[e.attributes["GROUP-ID"]]=i[e.attributes["GROUP-ID"]]||{},t=i[e.attributes["GROUP-ID"]],r={"default":/yes/i.test(e.attributes.DEFAULT)},r["default"]?r.autoselect=!0:r.autoselect=/yes/i.test(e.attributes.AUTOSELECT),e.attributes.LANGUAGE&&(r.language=e.attributes.LANGUAGE),e.attributes.URI&&(r.uri=e.attributes.URI),e.attributes["INSTREAM-ID"]&&(r.instreamId=e.attributes["INSTREAM-ID"]),e.attributes.CHARACTERISTICS&&(r.characteristics=e.attributes.CHARACTERISTICS),e.attributes.FORCED&&(r.forced=/yes/i.test(e.attributes.FORCED)),t[e.attributes.NAME]=r},discontinuity:function(){h+=1,s.discontinuity=!0,this.manifest.discontinuityStarts.push(n.length)},"program-date-time":function(){this.manifest.dateTimeString=e.dateTimeString,this.manifest.dateTimeObject=e.dateTimeObject},targetduration:function(){if(!isFinite(e.duration)||e.duration<0)return void this.trigger("warn",{message:"ignoring invalid target duration: "+e.duration});this.manifest.targetDuration=e.duration},totalduration:function(){if(!isFinite(e.duration)||e.duration<0)return void this.trigger("warn",{message:"ignoring invalid total duration: "+e.duration});this.manifest.totalDuration=e.duration},"cue-out":function(){s.cueOut=e.data},"cue-out-cont":function(){s.cueOutCont=e.data},"cue-in":function(){s.cueIn=e.data}})[e.tagType]||l).call(i)},uri:function(){s.uri=e.uri,n.push(s),!this.manifest.targetDuration||"duration"in s||(this.trigger("warn",{message:"defaulting segment duration to the target duration"}),s.duration=this.manifest.targetDuration),d&&(s.key=d),s.timeline=h,u&&(s.map=u),s={}},comment:function(){}})[e.type].call(i)}),e}return s(t,e),u(t,[{key:"push",value:function(e){this.lineStream.push(e)}},{key:"end",value:function(){this.lineStream.push("\n")}}]),t}(l["default"]);i["default"]=m},{}],35:[function(e,t,i){"use strict";function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(i,"__esModule",{value:!0});var r=function(){function e(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,i,n){return i&&e(t.prototype,i),n&&e(t,n),t}}(),a=function(){function e(){n(this,e),this.listeners={}}return r(e,[{key:"on",value:function(e,t){this.listeners[e]||(this.listeners[e]=[]),this.listeners[e].push(t)}},{key:"off",value:function(e,t){if(!this.listeners[e])return!1;var i=this.listeners[e].indexOf(t);return this.listeners[e].splice(i,1),i>-1}},{key:"trigger",value:function(e){var t=this.listeners[e],i=void 0,n=void 0,r=void 0;if(t)if(2===arguments.length)for(n=t.length,i=0;i<n;++i)t[i].call(this,arguments[1]);else for(r=Array.prototype.slice.call(arguments,1),n=t.length,i=0;i<n;++i)t[i].apply(this,r)}},{key:"dispose",value:function(){this.listeners={}}},{key:"pipe",value:function(e){this.on("data",function(t){e.push(t)})}}]),e}();i["default"]=a},{}],36:[function(e,t,i){"use strict";var n,r=e(60);n=function(){var e=new Uint8Array,t=0;n.prototype.init.call(this),this.setTimestamp=function(e){t=e},this.parseId3TagSize=function(e,t){var i=e[t+6]<<21|e[t+7]<<14|e[t+8]<<7|e[t+9];return(16&e[t+5])>>4?i+20:i+10},this.parseAdtsSize=function(e,t){var i=(224&e[t+5])>>5,n=e[t+4]<<3;return 6144&e[t+3]|n|i},this.push=function(i){var n,r,a,s,o=0,u=0;for(e.length?(s=e.length,e=new Uint8Array(i.byteLength+s),e.set(e.subarray(0,s)),e.set(i,s)):e=i;e.length-u>=3;)if(e[u]!=="I".charCodeAt(0)||e[u+1]!=="D".charCodeAt(0)||e[u+2]!=="3".charCodeAt(0))if(!0&e[u]&&240==(240&e[u+1])){if(e.length-u<7)break;if((o=this.parseAdtsSize(e,u))>e.length)break;a={type:"audio",data:e.subarray(u,u+o),pts:t,dts:t},this.trigger("data",a),u+=o}else u++;else{if(e.length-u<10)break;if((o=this.parseId3TagSize(e,u))>e.length)break;r={type:"timed-metadata",data:e.subarray(u,u+o)},this.trigger("data",r),u+=o}n=e.length-u,e=n>0?e.subarray(u):new Uint8Array}},n.prototype=new r,t.exports=n},{}],37:[function(e,t,i){"use strict";var n=[96e3,88200,64e3,48e3,44100,32e3,24e3,22050,16e3,12e3,11025,8e3,7350],r=function(e){return e[0]<<21|e[1]<<14|e[2]<<7|e[3]},a=function(e,t,i){var n,r="";for(n=t;n<i;n++)r+="%"+("00"+e[n].toString(16)).slice(-2);return r},s=function(e,t,i){return unescape(a(e,t,i))},o=function(e,t){var i=e[t+6]<<21|e[t+7]<<14|e[t+8]<<7|e[t+9];return(16&e[t+5])>>4?i+20:i+10},u=function(e,t){var i=(224&e[t+5])>>5,n=e[t+4]<<3;return 6144&e[t+3]|n|i},d=function(e,t){return e[t]==="I".charCodeAt(0)&&e[t+1]==="D".charCodeAt(0)&&e[t+2]==="3".charCodeAt(0)?"timed-metadata":!0&e[t]&&240==(240&e[t+1])?"audio":null},l=function(e){for(var t=0;t+5<e.length;){if(255===e[t]&&240==(246&e[t+1]))return n[(60&e[t+2])>>>2];t++}return null},f=function(e){var t,i,n;t=10,64&e[5]&&(t+=4,t+=r(e.subarray(10,14)));do{if((i=r(e.subarray(t+4,t+8)))<1)return null;if("PRIV"===String.fromCharCode(e[t],e[t+1],e[t+2],e[t+3])){n=e.subarray(t+10,t+i+10);for(var a=0;a<n.byteLength;a++)if(0===n[a]){var o=s(n,0,a);if("com.apple.streaming.transportStreamTimestamp"===o){var u=n.subarray(a+1),d=(1&u[3])<<30|u[4]<<22|u[5]<<14|u[6]<<6|u[7]>>>2;return d*=4,d+=3&u[7]}break}}t+=10,t+=i}while(t<e.byteLength);return null};t.exports={parseId3TagSize:o,parseAdtsSize:u,parseType:d,parseSampleRate:l,parseAacTimestamp:f}},{}],38:[function(e,t,i){"use strict";var n,r=e(60),a=[96e3,88200,64e3,48e3,44100,32e3,24e3,22050,16e3,12e3,11025,8e3,7350];n=function(){var e;n.prototype.init.call(this),this.push=function(t){var i,n,r,s,o,u,d=0,l=0;if("audio"===t.type)for(e?(s=e,e=new Uint8Array(s.byteLength+t.data.byteLength),e.set(s),e.set(t.data,s.byteLength)):e=t.data;d+5<e.length;)if(255===e[d]&&240==(246&e[d+1])){if(n=2*(1&~e[d+1]),i=(3&e[d+3])<<11|e[d+4]<<3|(224&e[d+5])>>5,o=1024*(1+(3&e[d+6])),u=9e4*o/a[(60&e[d+2])>>>2],r=d+i,e.byteLength<r)return;if(this.trigger("data",{pts:t.pts+l*u,dts:t.dts+l*u,sampleCount:o,audioobjecttype:1+(e[d+2]>>>6&3),channelcount:(1&e[d+2])<<2|(192&e[d+3])>>>6,samplerate:a[(60&e[d+2])>>>2],samplingfrequencyindex:(60&e[d+2])>>>2,samplesize:16,data:e.subarray(d+7+n,r)}),e.byteLength===r)return void(e=undefined);l++,e=e.subarray(r)}else d++},this.flush=function(){this.trigger("done")}},n.prototype=new r,t.exports=n},{}],39:[function(e,t,i){"use strict";var n,r,a,s=e(60),o=e(59);r=function(){var e,t,i=0;r.prototype.init.call(this),this.push=function(n){var r;for(t?(r=new Uint8Array(t.byteLength+n.data.byteLength),r.set(t),r.set(n.data,t.byteLength),t=r):t=n.data;i<t.byteLength-3;i++)if(1===t[i+2]){e=i+5;break}for(;e<t.byteLength;)switch(t[e]){case 0:if(0!==t[e-1]){e+=2;break}if(0!==t[e-2]){e++;break}i+3!==e-2&&this.trigger("data",t.subarray(i+3,e-2));do{e++}while(1!==t[e]&&e<t.length);i=e-2,e+=3;break;case 1:if(0!==t[e-1]||0!==t[e-2]){e+=3;break}this.trigger("data",t.subarray(i+3,e-2)),i=e-2,e+=3;break;default:e+=3}t=t.subarray(i),e-=i,i=0},this.flush=function(){t&&t.byteLength>3&&this.trigger("data",t.subarray(i+3)),t=null,i=0,this.trigger("done")}},r.prototype=new s,a={100:!0,110:!0,122:!0,244:!0,44:!0,83:!0,86:!0,118:!0,128:!0,138:!0,139:!0,134:!0},n=function(){var e,t,i,s,u,d,l,f=new r;n.prototype.init.call(this),e=this,this.push=function(e){"video"===e.type&&(t=e.trackId,i=e.pts,s=e.dts,f.push(e))},f.on("data",function(n){var r={trackId:t,pts:i,dts:s,data:n};switch(31&n[0]){case 5:r.nalUnitType="slice_layer_without_partitioning_rbsp_idr";break;case 6:r.nalUnitType="sei_rbsp",r.escapedRBSP=u(n.subarray(1));break;case 7:r.nalUnitType="seq_parameter_set_rbsp",r.escapedRBSP=u(n.subarray(1)),r.config=d(r.escapedRBSP);break;case 8:r.nalUnitType="pic_parameter_set_rbsp";break;case 9:r.nalUnitType="access_unit_delimiter_rbsp"}e.trigger("data",r)}),f.on("done",function(){e.trigger("done")}),this.flush=function(){f.flush()},l=function(e,t){var i,n,r=8,a=8;for(i=0;i<e;i++)0!==a&&(n=t.readExpGolomb(),a=(r+n+256)%256),r=0===a?r:a},u=function(e){for(var t,i,n=e.byteLength,r=[],a=1;a<n-2;)0===e[a]&&0===e[a+1]&&3===e[a+2]?(r.push(a+2),a+=2):a++;if(0===r.length)return e;t=n-r.length,i=new Uint8Array(t);var s=0;for(a=0;a<t;s++,a++)s===r[0]&&(s++,r.shift()),i[a]=e[s];return i},d=function(e){var t,i,n,r,s,u,d,f,c,h,p,m,g,y=0,_=0,v=0,b=0,T=1;if(t=new o(e),i=t.readUnsignedByte(),r=t.readUnsignedByte(),n=t.readUnsignedByte(),t.skipUnsignedExpGolomb(),a[i]&&(s=t.readUnsignedExpGolomb(),3===s&&t.skipBits(1),t.skipUnsignedExpGolomb(),t.skipUnsignedExpGolomb(),t.skipBits(1),t.readBoolean()))for(p=3!==s?8:12,g=0;g<p;g++)t.readBoolean()&&(g<6?l(16,t):l(64,t));if(t.skipUnsignedExpGolomb(),0===(u=t.readUnsignedExpGolomb()))t.readUnsignedExpGolomb();else if(1===u)for(t.skipBits(1),t.skipExpGolomb(),t.skipExpGolomb(),d=t.readUnsignedExpGolomb(),g=0;g<d;g++)t.skipExpGolomb();if(t.skipUnsignedExpGolomb(),t.skipBits(1),f=t.readUnsignedExpGolomb(),c=t.readUnsignedExpGolomb(),h=t.readBits(1),0===h&&t.skipBits(1),t.skipBits(1),t.readBoolean()&&(y=t.readUnsignedExpGolomb(),_=t.readUnsignedExpGolomb(),v=t.readUnsignedExpGolomb(),b=t.readUnsignedExpGolomb()),t.readBoolean()&&t.readBoolean()){switch(t.readUnsignedByte()){case 1:m=[1,1];break;case 2:m=[12,11];break;case 3:m=[10,11];break;case 4:m=[16,11];break;case 5:m=[40,33];break;case 6:m=[24,11];break;case 7:m=[20,11];break;case 8:m=[32,11];break;case 9:m=[80,33];break;case 10:m=[18,11];break;case 11:m=[15,11];break;case 12:m=[64,33];break;case 13:m=[160,99];break;case 14:m=[4,3];break;case 15:m=[3,2];break;case 16:
m=[2,1];break;case 255:m=[t.readUnsignedByte()<<8|t.readUnsignedByte(),t.readUnsignedByte()<<8|t.readUnsignedByte()]}m&&(T=m[0]/m[1])}return{profileIdc:i,levelIdc:n,profileCompatibility:r,width:Math.ceil((16*(f+1)-2*y-2*_)*T),height:(2-h)*(c+1)*16-2*v-2*b}}},n.prototype=new s,t.exports={H264Stream:n,NalByteStream:r}},{}],40:[function(e,t,i){var n=[33,16,5,32,164,27],r=[33,65,108,84,1,2,4,8,168,2,4,8,17,191,252],a=function(e){for(var t=[];e--;)t.push(0);return t},s={96e3:[n,[227,64],a(154),[56]],88200:[n,[231],a(170),[56]],64e3:[n,[248,192],a(240),[56]],48e3:[n,[255,192],a(268),[55,148,128],a(54),[112]],44100:[n,[255,192],a(268),[55,163,128],a(84),[112]],32e3:[n,[255,192],a(268),[55,234],a(226),[112]],24e3:[n,[255,192],a(268),[55,255,128],a(268),[111,112],a(126),[224]],16e3:[n,[255,192],a(268),[55,255,128],a(268),[111,255],a(269),[223,108],a(195),[1,192]],12e3:[r,a(268),[3,127,248],a(268),[6,255,240],a(268),[13,255,224],a(268),[27,253,128],a(259),[56]],11025:[r,a(268),[3,127,248],a(268),[6,255,240],a(268),[13,255,224],a(268),[27,255,192],a(268),[55,175,128],a(108),[112]],8e3:[r,a(268),[3,121,16],a(47),[7]]};t.exports=function(e){return Object.keys(e).reduce(function(t,i){return t[i]=new Uint8Array(e[i].reduce(function(e,t){return e.concat(t)},[])),t},{})}(s)},{}],41:[function(e,t,i){"use strict";var n=e(60),r=function(e){this.numberOfTracks=0,this.metadataStream=e.metadataStream,this.videoTags=[],this.audioTags=[],this.videoTrack=null,this.audioTrack=null,this.pendingCaptions=[],this.pendingMetadata=[],this.pendingTracks=0,this.processedTracks=0,r.prototype.init.call(this),this.push=function(e){return e.text?this.pendingCaptions.push(e):e.frames?this.pendingMetadata.push(e):("video"===e.track.type&&(this.videoTrack=e.track,this.videoTags=e.tags,this.pendingTracks++),void("audio"===e.track.type&&(this.audioTrack=e.track,this.audioTags=e.tags,this.pendingTracks++)))}};r.prototype=new n,r.prototype.flush=function(e){var t,i,n,r,a={tags:{},captions:[],captionStreams:{},metadata:[]};if(this.pendingTracks<this.numberOfTracks){if("VideoSegmentStream"!==e&&"AudioSegmentStream"!==e)return;if(0===this.pendingTracks&&++this.processedTracks<this.numberOfTracks)return}if(this.processedTracks+=this.pendingTracks,this.pendingTracks=0,!(this.processedTracks<this.numberOfTracks)){for(this.videoTrack?r=this.videoTrack.timelineStartInfo.pts:this.audioTrack&&(r=this.audioTrack.timelineStartInfo.pts),a.tags.videoTags=this.videoTags,a.tags.audioTags=this.audioTags,n=0;n<this.pendingCaptions.length;n++)i=this.pendingCaptions[n],i.startTime=i.startPts-r,i.startTime/=9e4,i.endTime=i.endPts-r,i.endTime/=9e4,a.captionStreams[i.stream]=!0,a.captions.push(i);for(n=0;n<this.pendingMetadata.length;n++)t=this.pendingMetadata[n],t.cueTime=t.pts-r,t.cueTime/=9e4,a.metadata.push(t);a.metadata.dispatchType=this.metadataStream.dispatchType,this.videoTrack=null,this.audioTrack=null,this.videoTags=[],this.audioTags=[],this.pendingCaptions.length=0,this.pendingMetadata.length=0,this.pendingTracks=0,this.processedTracks=0,this.trigger("data",a),this.trigger("done")}},t.exports=r},{}],42:[function(e,t,i){"use strict";var n=e(43),r=function(e,t,i){var r,a,s,o=new Uint8Array(9),u=new DataView(o.buffer);return e=e||0,t=t===undefined||t,i=i===undefined||i,u.setUint8(0,70),u.setUint8(1,76),u.setUint8(2,86),u.setUint8(3,1),u.setUint8(4,(t?4:0)|(i?1:0)),u.setUint32(5,o.byteLength),e<=0?(a=new Uint8Array(o.byteLength+4),a.set(o),a.set([0,0,0,0],o.byteLength),a):(r=new n(n.METADATA_TAG),r.pts=r.dts=0,r.writeMetaDataDouble("duration",e),s=r.finalize().length,a=new Uint8Array(o.byteLength+s),a.set(o),a.set(u.byteLength,s),a)};t.exports=r},{}],43:[function(e,t,i){"use strict";var n;n=function(e,t){var i,r=0,a=16384,s=function(e,t){var i,n=e.position+t;n<e.bytes.byteLength||(i=new Uint8Array(2*n),i.set(e.bytes.subarray(0,e.position),0),e.bytes=i,e.view=new DataView(e.bytes.buffer))},o=n.widthBytes||new Uint8Array("width".length),u=n.heightBytes||new Uint8Array("height".length),d=n.videocodecidBytes||new Uint8Array("videocodecid".length);if(!n.widthBytes){for(i=0;i<"width".length;i++)o[i]="width".charCodeAt(i);for(i=0;i<"height".length;i++)u[i]="height".charCodeAt(i);for(i=0;i<"videocodecid".length;i++)d[i]="videocodecid".charCodeAt(i);n.widthBytes=o,n.heightBytes=u,n.videocodecidBytes=d}switch(this.keyFrame=!1,e){case n.VIDEO_TAG:this.length=16,a*=6;break;case n.AUDIO_TAG:this.length=13,this.keyFrame=!0;break;case n.METADATA_TAG:this.length=29,this.keyFrame=!0;break;default:throw new Error("Unknown FLV tag type")}this.bytes=new Uint8Array(a),this.view=new DataView(this.bytes.buffer),this.bytes[0]=e,this.position=this.length,this.keyFrame=t,this.pts=0,this.dts=0,this.writeBytes=function(e,t,i){var n,r=t||0;i=i||e.byteLength,n=r+i,s(this,i),this.bytes.set(e.subarray(r,n),this.position),this.position+=i,this.length=Math.max(this.length,this.position)},this.writeByte=function(e){s(this,1),this.bytes[this.position]=e,this.position++,this.length=Math.max(this.length,this.position)},this.writeShort=function(e){s(this,2),this.view.setUint16(this.position,e),this.position+=2,this.length=Math.max(this.length,this.position)},this.negIndex=function(e){return this.bytes[this.length-e]},this.nalUnitSize=function(){return 0===r?0:this.length-(r+4)},this.startNalUnit=function(){if(r>0)throw new Error("Attempted to create new NAL wihout closing the old one");r=this.length,this.length+=4,this.position=this.length},this.endNalUnit=function(e){var t,i;this.length===r+4?this.length-=4:r>0&&(t=r+4,i=this.length-t,this.position=r,this.view.setUint32(this.position,i),this.position=this.length,e&&e.push(this.bytes.subarray(t,t+i))),r=0},this.writeMetaDataDouble=function(e,t){var i;if(s(this,2+e.length+9),this.view.setUint16(this.position,e.length),this.position+=2,"width"===e)this.bytes.set(o,this.position),this.position+=5;else if("height"===e)this.bytes.set(u,this.position),this.position+=6;else if("videocodecid"===e)this.bytes.set(d,this.position),this.position+=12;else for(i=0;i<e.length;i++)this.bytes[this.position]=e.charCodeAt(i),this.position++;this.position++,this.view.setFloat64(this.position,t),this.position+=8,this.length=Math.max(this.length,this.position),++r},this.writeMetaDataBoolean=function(e,t){var i;for(s(this,2),this.view.setUint16(this.position,e.length),this.position+=2,i=0;i<e.length;i++)s(this,1),this.bytes[this.position]=e.charCodeAt(i),this.position++;s(this,2),this.view.setUint8(this.position,1),this.position++,this.view.setUint8(this.position,t?1:0),this.position++,this.length=Math.max(this.length,this.position),++r},this.finalize=function(){var e,i;switch(this.bytes[0]){case n.VIDEO_TAG:this.bytes[11]=7|(this.keyFrame||t?16:32),this.bytes[12]=t?0:1,e=this.pts-this.dts,this.bytes[13]=(16711680&e)>>>16,this.bytes[14]=(65280&e)>>>8,this.bytes[15]=(255&e)>>>0;break;case n.AUDIO_TAG:this.bytes[11]=175,this.bytes[12]=t?0:1;break;case n.METADATA_TAG:this.position=11,this.view.setUint8(this.position,2),this.position++,this.view.setUint16(this.position,10),this.position+=2,this.bytes.set([111,110,77,101,116,97,68,97,116,97],this.position),this.position+=10,this.bytes[this.position]=8,this.position++,this.view.setUint32(this.position,r),this.position=this.length,this.bytes.set([0,0,9],this.position),this.position+=3,this.length=this.position}return i=this.length-11,this.bytes[1]=(16711680&i)>>>16,this.bytes[2]=(65280&i)>>>8,this.bytes[3]=(255&i)>>>0,this.bytes[4]=(16711680&this.dts)>>>16,this.bytes[5]=(65280&this.dts)>>>8,this.bytes[6]=(255&this.dts)>>>0,this.bytes[7]=(4278190080&this.dts)>>>24,this.bytes[8]=0,this.bytes[9]=0,this.bytes[10]=0,s(this,4),this.view.setUint32(this.length,this.length),this.length+=4,this.position+=4,this.bytes=this.bytes.subarray(0,this.length),this.frameTime=n.frameTime(this.bytes),this}},n.AUDIO_TAG=8,n.VIDEO_TAG=9,n.METADATA_TAG=18,n.isAudioFrame=function(e){return n.AUDIO_TAG===e[0]},n.isVideoFrame=function(e){return n.VIDEO_TAG===e[0]},n.isMetaData=function(e){return n.METADATA_TAG===e[0]},n.isKeyFrame=function(e){return n.isVideoFrame(e)?23===e[11]:!!n.isAudioFrame(e)||!!n.isMetaData(e)},n.frameTime=function(e){var t=e[4]<<16;return t|=e[5]<<8,t|=e[6]<<0,t|=e[7]<<24},t.exports=n},{}],44:[function(e,t,i){t.exports={tag:e(43),Transmuxer:e(46),getFlvHeader:e(42)}},{}],45:[function(e,t,i){"use strict";var n=function(){var e=this;this.list=[],this.push=function(e){this.list.push({bytes:e.bytes,dts:e.dts,pts:e.pts,keyFrame:e.keyFrame,metaDataTag:e.metaDataTag})},Object.defineProperty(this,"length",{get:function(){return e.list.length}})};t.exports=n},{}],46:[function(e,t,i){"use strict";var n,r,a,s,o,u,d=e(60),l=e(43),f=e(48),c=e(38),h=e(39).H264Stream,p=e(41),m=e(45);s=function(e,t){"number"==typeof t.pts&&(e.timelineStartInfo.pts===undefined?e.timelineStartInfo.pts=t.pts:e.timelineStartInfo.pts=Math.min(e.timelineStartInfo.pts,t.pts)),"number"==typeof t.dts&&(e.timelineStartInfo.dts===undefined?e.timelineStartInfo.dts=t.dts:e.timelineStartInfo.dts=Math.min(e.timelineStartInfo.dts,t.dts))},o=function(e,t){var i=new l(l.METADATA_TAG);return i.dts=t,i.pts=t,i.writeMetaDataDouble("videocodecid",7),i.writeMetaDataDouble("width",e.width),i.writeMetaDataDouble("height",e.height),i},u=function(e,t){var i,n=new l(l.VIDEO_TAG,!0);for(n.dts=t,n.pts=t,n.writeByte(1),n.writeByte(e.profileIdc),n.writeByte(e.profileCompatibility),n.writeByte(e.levelIdc),n.writeByte(255),n.writeByte(225),n.writeShort(e.sps[0].length),n.writeBytes(e.sps[0]),n.writeByte(e.pps.length),i=0;i<e.pps.length;++i)n.writeShort(e.pps[i].length),n.writeBytes(e.pps[i]);return n},a=function(e){var t,i=[],n=[];a.prototype.init.call(this),this.push=function(t){s(e,t),e&&(e.audioobjecttype=t.audioobjecttype,e.channelcount=t.channelcount,e.samplerate=t.samplerate,e.samplingfrequencyindex=t.samplingfrequencyindex,e.samplesize=t.samplesize,e.extraData=e.audioobjecttype<<11|e.samplingfrequencyindex<<7|e.channelcount<<3),t.pts=Math.round(t.pts/90),t.dts=Math.round(t.dts/90),i.push(t)},this.flush=function(){var r,a,s,o=new m;if(0===i.length)return void this.trigger("done","AudioSegmentStream");for(s=-Infinity;i.length;)r=i.shift(),n.length&&r.pts>=n[0]&&(s=n.shift(),this.writeMetaDataTags(o,s)),(e.extraData!==t||r.pts-s>=1e3)&&(this.writeMetaDataTags(o,r.pts),t=e.extraData,s=r.pts),a=new l(l.AUDIO_TAG),a.pts=r.pts,a.dts=r.dts,a.writeBytes(r.data),o.push(a.finalize());n.length=0,t=null,this.trigger("data",{track:e,tags:o.list}),this.trigger("done","AudioSegmentStream")},this.writeMetaDataTags=function(t,i){var n;n=new l(l.METADATA_TAG),n.pts=i,n.dts=i,n.writeMetaDataDouble("audiocodecid",10),n.writeMetaDataBoolean("stereo",2===e.channelcount),n.writeMetaDataDouble("audiosamplerate",e.samplerate),n.writeMetaDataDouble("audiosamplesize",16),t.push(n.finalize()),n=new l(l.AUDIO_TAG,!0),n.pts=i,n.dts=i,n.view.setUint16(n.position,e.extraData),n.position+=2,n.length=Math.max(n.length,n.position),t.push(n.finalize())},this.onVideoKeyFrame=function(e){n.push(e)}},a.prototype=new d,r=function(e){var t,i,n=[];r.prototype.init.call(this),this.finishFrame=function(n,r){if(r){if(t&&e&&e.newMetadata&&(r.keyFrame||0===n.length)){var a=o(t,r.dts).finalize(),s=u(e,r.dts).finalize();a.metaDataTag=s.metaDataTag=!0,n.push(a),n.push(s),e.newMetadata=!1,this.trigger("keyframe",r.dts)}r.endNalUnit(),n.push(r.finalize()),i=null}},this.push=function(t){s(e,t),t.pts=Math.round(t.pts/90),t.dts=Math.round(t.dts/90),n.push(t)},this.flush=function(){for(var r,a=new m;n.length&&"access_unit_delimiter_rbsp"!==n[0].nalUnitType;)n.shift();if(0===n.length)return void this.trigger("done","VideoSegmentStream");for(;n.length;)r=n.shift(),"seq_parameter_set_rbsp"===r.nalUnitType?(e.newMetadata=!0,t=r.config,e.width=t.width,e.height=t.height,e.sps=[r.data],e.profileIdc=t.profileIdc,e.levelIdc=t.levelIdc,e.profileCompatibility=t.profileCompatibility,i.endNalUnit()):"pic_parameter_set_rbsp"===r.nalUnitType?(e.newMetadata=!0,e.pps=[r.data],i.endNalUnit()):"access_unit_delimiter_rbsp"===r.nalUnitType?(i&&this.finishFrame(a,i),i=new l(l.VIDEO_TAG),i.pts=r.pts,i.dts=r.dts):("slice_layer_without_partitioning_rbsp_idr"===r.nalUnitType&&(i.keyFrame=!0),i.endNalUnit()),i.startNalUnit(),i.writeBytes(r.data);i&&this.finishFrame(a,i),this.trigger("data",{track:e,tags:a.list}),this.trigger("done","VideoSegmentStream")}},r.prototype=new d,n=function(e){var t,i,s,o,u,d,l,m,g,y,_,v,b=this;n.prototype.init.call(this),e=e||{},this.metadataStream=new f.MetadataStream,e.metadataStream=this.metadataStream,t=new f.TransportPacketStream,i=new f.TransportParseStream,s=new f.ElementaryStream,o=new f.TimestampRolloverStream("video"),u=new f.TimestampRolloverStream("audio"),d=new f.TimestampRolloverStream("timed-metadata"),l=new c,m=new h,v=new p(e),t.pipe(i).pipe(s),s.pipe(o).pipe(m),s.pipe(u).pipe(l),s.pipe(d).pipe(this.metadataStream).pipe(v),_=new f.CaptionStream,m.pipe(_).pipe(v),s.on("data",function(e){var t,i,n;if("metadata"===e.type){for(t=e.tracks.length;t--;)"video"===e.tracks[t].type?i=e.tracks[t]:"audio"===e.tracks[t].type&&(n=e.tracks[t]);i&&!g&&(v.numberOfTracks++,g=new r(i),m.pipe(g).pipe(v)),n&&!y&&(v.numberOfTracks++,y=new a(n),l.pipe(y).pipe(v),g&&g.on("keyframe",y.onVideoKeyFrame))}}),this.push=function(e){t.push(e)},this.flush=function(){t.flush()},this.resetCaptions=function(){_.reset()},v.on("data",function(e){b.trigger("data",e)}),v.on("done",function(){b.trigger("done")})},n.prototype=new d,t.exports=n},{}],47:[function(e,t,i){"use strict";var n=e(60),r=function(e){for(var t=0,i={payloadType:-1,payloadSize:0},n=0,r=0;t<e.byteLength&&128!==e[t];){for(;255===e[t];)n+=255,t++;for(n+=e[t++];255===e[t];)r+=255,t++;if(r+=e[t++],!i.payload&&4===n){i.payloadType=n,i.payloadSize=r,i.payload=e.subarray(t,t+r);break}t+=r,n=0,r=0}return i},a=function(e){return 181!==e.payload[0]?null:49!=(e.payload[1]<<8|e.payload[2])?null:"GA94"!==String.fromCharCode(e.payload[3],e.payload[4],e.payload[5],e.payload[6])?null:3!==e.payload[7]?null:e.payload.subarray(8,e.payload.length-1)},s=function(e,t){var i,n,r,a,s=[];if(!(64&t[0]))return s;for(n=31&t[0],i=0;i<n;i++)r=3*i,a={type:3&t[r+2],pts:e},4&t[r+2]&&(a.ccData=t[r+3]<<8|t[r+4],s.push(a));return s},o=function(){o.prototype.init.call(this),this.captionPackets_=[],this.ccStreams_=[new c(0,0),new c(0,1),new c(1,0),new c(1,1)],this.reset(),this.ccStreams_.forEach(function(e){e.on("data",this.trigger.bind(this,"data")),e.on("done",this.trigger.bind(this,"done"))},this)};o.prototype=new n,o.prototype.push=function(e){var t,i;if("sei_rbsp"===e.nalUnitType&&(t=r(e.escapedRBSP),4===t.payloadType&&(i=a(t)))){if(e.dts<this.latestDts_)return void(this.ignoreNextEqualDts_=!0);if(e.dts===this.latestDts_&&this.ignoreNextEqualDts_)return void(this.ignoreNextEqualDts_=!1);this.captionPackets_=this.captionPackets_.concat(s(e.pts,i)),this.latestDts_=e.dts}},o.prototype.flush=function(){if(!this.captionPackets_.length)return void this.ccStreams_.forEach(function(e){e.flush()},this);this.captionPackets_.forEach(function(e,t){e.presortIndex=t}),this.captionPackets_.sort(function(e,t){return e.pts===t.pts?e.presortIndex-t.presortIndex:e.pts-t.pts}),this.captionPackets_.forEach(function(e){e.type<2&&this.dispatchCea608Packet(e)},this),this.captionPackets_.length=0,this.ccStreams_.forEach(function(e){e.flush()},this)},o.prototype.reset=function(){this.latestDts_=null,this.ignoreNextEqualDts_=!1,this.activeCea608Channel_=[null,null],this.ccStreams_.forEach(function(e){e.reset()})},o.prototype.dispatchCea608Packet=function(e){this.setsChannel1Active(e)?this.activeCea608Channel_[e.type]=0:this.setsChannel2Active(e)&&(this.activeCea608Channel_[e.type]=1),null!==this.activeCea608Channel_[e.type]&&this.ccStreams_[(e.type<<1)+this.activeCea608Channel_[e.type]].push(e)},o.prototype.setsChannel1Active=function(e){return 4096==(30720&e.ccData)},o.prototype.setsChannel2Active=function(e){return 6144==(30720&e.ccData)};var u={42:225,92:233,94:237,95:243,96:250,123:231,124:247,125:209,126:241,127:9608,304:174,305:176,306:189,307:191,308:8482,309:162,310:163,311:9834,312:224,313:160,314:232,315:226,316:234,317:238,318:244,319:251,544:193,545:201,546:211,547:218,548:220,549:252,550:8216,551:161,552:42,553:39,554:8212,555:169,556:8480,557:8226,558:8220,559:8221,560:192,561:194,562:199,563:200,564:202,565:203,566:235,567:206,568:207,569:239,570:212,571:217,572:249,573:219,574:171,575:187,800:195,801:227,802:205,803:204,804:236,805:210,806:242,807:213,808:245,809:123,810:125,811:92,812:94,813:95,814:124,815:126,816:196,817:228,818:214,819:246,820:223,821:165,822:164,823:9474,824:197,825:229,826:216,827:248,828:9484,829:9488,830:9492,831:9496},d=function(e){return null===e?"":(e=u[e]||e,String.fromCharCode(e))},l=[4352,4384,4608,4640,5376,5408,5632,5664,5888,5920,4096,4864,4896,5120,5152],f=function(){for(var e=[],t=15;t--;)e.push("");return e},c=function(e,t){c.prototype.init.call(this),this.field_=e||0,this.dataChannel_=t||0,this.name_="CC"+(1+(this.field_<<1|this.dataChannel_)),this.setConstants(),this.reset(),this.push=function(e){var t,i,n,r,a;if((t=32639&e.ccData)===this.lastControlCode_)return void(this.lastControlCode_=null);if(4096==(61440&t)?this.lastControlCode_=t:t!==this.PADDING_&&(this.lastControlCode_=null),n=t>>>8,r=255&t,t!==this.PADDING_)if(t===this.RESUME_CAPTION_LOADING_)this.mode_="popOn";else if(t===this.END_OF_CAPTION_)this.clearFormatting(e.pts),this.flushDisplayed(e.pts),i=this.displayed_,this.displayed_=this.nonDisplayed_,this.nonDisplayed_=i,this.startPts_=e.pts;else if(t===this.ROLL_UP_2_ROWS_)this.topRow_=13,this.mode_="rollUp";else if(t===this.ROLL_UP_3_ROWS_)this.topRow_=12,this.mode_="rollUp";else if(t===this.ROLL_UP_4_ROWS_)this.topRow_=11,this.mode_="rollUp";else if(t===this.CARRIAGE_RETURN_)this.clearFormatting(e.pts),this.flushDisplayed(e.pts),this.shiftRowsUp_(),this.startPts_=e.pts;else if(t===this.BACKSPACE_)"popOn"===this.mode_?this.nonDisplayed_[14]=this.nonDisplayed_[14].slice(0,-1):this.displayed_[14]=this.displayed_[14].slice(0,-1);else if(t===this.ERASE_DISPLAYED_MEMORY_)this.flushDisplayed(e.pts),this.displayed_=f();else if(t===this.ERASE_NON_DISPLAYED_MEMORY_)this.nonDisplayed_=f();else if(t===this.RESUME_DIRECT_CAPTIONING_)this.mode_="paintOn";else if(this.isSpecialCharacter(n,r))n=(3&n)<<8,a=d(n|r),this[this.mode_](e.pts,a),this.column_++;else if(this.isExtCharacter(n,r))"popOn"===this.mode_?this.nonDisplayed_[this.row_]=this.nonDisplayed_[this.row_].slice(0,-1):this.displayed_[14]=this.displayed_[14].slice(0,-1),n=(3&n)<<8,a=d(n|r),this[this.mode_](e.pts,a),this.column_++;else if(this.isMidRowCode(n,r))this.clearFormatting(e.pts),this[this.mode_](e.pts," "),this.column_++,14==(14&r)&&this.addFormatting(e.pts,["i"]),1==(1&r)&&this.addFormatting(e.pts,["u"]);else if(this.isOffsetControlCode(n,r))this.column_+=3&r;else if(this.isPAC(n,r)){var s=l.indexOf(7968&t);s!==this.row_&&(this.clearFormatting(e.pts),this.row_=s),1&r&&-1===this.formatting_.indexOf("u")&&this.addFormatting(e.pts,["u"]),16==(16&t)&&(this.column_=4*((14&t)>>1)),this.isColorPAC(r)&&14==(14&r)&&this.addFormatting(e.pts,["i"])}else this.isNormalChar(n)&&(0===r&&(r=null),a=d(n),a+=d(r),this[this.mode_](e.pts,a),this.column_+=a.length)}};c.prototype=new n,c.prototype.flushDisplayed=function(e){var t=this.displayed_.map(function(e){return e.trim()}).join("\n").replace(/^\n+|\n+$/g,"");t.length&&this.trigger("data",{startPts:this.startPts_,endPts:e,text:t,stream:this.name_})},c.prototype.reset=function(){this.mode_="popOn",this.topRow_=0,this.startPts_=0,this.displayed_=f(),this.nonDisplayed_=f(),this.lastControlCode_=null,this.column_=0,this.row_=14,this.formatting_=[]},c.prototype.setConstants=function(){0===this.dataChannel_?(this.BASE_=16,this.EXT_=17,this.CONTROL_=(20|this.field_)<<8,this.OFFSET_=23):1===this.dataChannel_&&(this.BASE_=24,this.EXT_=25,this.CONTROL_=(28|this.field_)<<8,this.OFFSET_=31),this.PADDING_=0,this.RESUME_CAPTION_LOADING_=32|this.CONTROL_,this.END_OF_CAPTION_=47|this.CONTROL_,this.ROLL_UP_2_ROWS_=37|this.CONTROL_,this.ROLL_UP_3_ROWS_=38|this.CONTROL_,this.ROLL_UP_4_ROWS_=39|this.CONTROL_,this.CARRIAGE_RETURN_=45|this.CONTROL_,this.RESUME_DIRECT_CAPTIONING_=41|this.CONTROL_,this.BACKSPACE_=33|this.CONTROL_,this.ERASE_DISPLAYED_MEMORY_=44|this.CONTROL_,this.ERASE_NON_DISPLAYED_MEMORY_=46|this.CONTROL_},c.prototype.isSpecialCharacter=function(e,t){return e===this.EXT_&&t>=48&&t<=63},c.prototype.isExtCharacter=function(e,t){return(e===this.EXT_+1||e===this.EXT_+2)&&t>=32&&t<=63},c.prototype.isMidRowCode=function(e,t){return e===this.EXT_&&t>=32&&t<=47},c.prototype.isOffsetControlCode=function(e,t){return e===this.OFFSET_&&t>=33&&t<=35},c.prototype.isPAC=function(e,t){return e>=this.BASE_&&e<this.BASE_+8&&t>=64&&t<=127},c.prototype.isColorPAC=function(e){return e>=64&&e<=79||e>=96&&e<=127},c.prototype.isNormalChar=function(e){return e>=32&&e<=127},c.prototype.addFormatting=function(e,t){this.formatting_=this.formatting_.concat(t);var i=t.reduce(function(e,t){return e+"<"+t+">"},"");this[this.mode_](e,i)},c.prototype.clearFormatting=function(e){if(this.formatting_.length){var t=this.formatting_.reverse().reduce(function(e,t){return e+"</"+t+">"},"");this.formatting_=[],this[this.mode_](e,t)}},c.prototype.popOn=function(e,t){var i=this.nonDisplayed_[this.row_];i+=t,this.nonDisplayed_[this.row_]=i},c.prototype.rollUp=function(e,t){var i=this.displayed_[14];i+=t,this.displayed_[14]=i},c.prototype.shiftRowsUp_=function(){var e;for(e=0;e<this.topRow_;e++)this.displayed_[e]="";for(e=this.topRow_;e<14;e++)this.displayed_[e]=this.displayed_[e+1];this.displayed_[14]=""},c.prototype.paintOn=function(){},t.exports={CaptionStream:o,Cea608Stream:c}},{}],48:[function(e,t,i){"use strict";var n,r,a,s=e(60),o=e(47),u=e(51),d=e(52).TimestampRolloverStream,l=e(51);n=function(){var e=new Uint8Array(188),t=0;n.prototype.init.call(this),this.push=function(i){var n,r=0,a=188;for(t?(n=new Uint8Array(i.byteLength+t),n.set(e.subarray(0,t)),n.set(i,t),t=0):n=i;a<n.byteLength;)71!==n[r]||71!==n[a]?(r++,a++):(this.trigger("data",n.subarray(r,a)),r+=188,a+=188);r<n.byteLength&&(e.set(n.subarray(r),0),t=n.byteLength-r)},this.flush=function(){188===t&&71===e[0]&&(this.trigger("data",e),t=0),this.trigger("done")}},n.prototype=new s,r=function(){var e,t,i,n;r.prototype.init.call(this),n=this,this.packetsWaitingForPmt=[],this.programMapTable=undefined,e=function(e,n){var r=0;n.payloadUnitStartIndicator&&(r+=e[r]+1),"pat"===n.type?t(e.subarray(r),n):i(e.subarray(r),n)},t=function(e,t){t.section_number=e[7],t.last_section_number=e[8],n.pmtPid=(31&e[10])<<8|e[11],t.pmtPid=n.pmtPid},i=function(e,t){var i,r,a,s;if(1&e[5]){for(n.programMapTable={video:null,audio:null,"timed-metadata":{}},i=(15&e[1])<<8|e[2],r=3+i-4,a=(15&e[10])<<8|e[11],s=12+a;s<r;){var o=e[s],d=(31&e[s+1])<<8|e[s+2];o===u.H264_STREAM_TYPE&&null===n.programMapTable.video?n.programMapTable.video=d:o===u.ADTS_STREAM_TYPE&&null===n.programMapTable.audio?n.programMapTable.audio=d:o===u.METADATA_STREAM_TYPE&&(n.programMapTable["timed-metadata"][d]=o),s+=5+((15&e[s+3])<<8|e[s+4])}t.programMapTable=n.programMapTable}},this.push=function(t){var i={},n=4;if(i.payloadUnitStartIndicator=!!(64&t[1]),i.pid=31&t[1],i.pid<<=8,i.pid|=t[2],(48&t[3])>>>4>1&&(n+=t[n]+1),0===i.pid)i.type="pat",e(t.subarray(n),i),this.trigger("data",i);else if(i.pid===this.pmtPid)for(i.type="pmt",e(t.subarray(n),i),this.trigger("data",i);this.packetsWaitingForPmt.length;)this.processPes_.apply(this,this.packetsWaitingForPmt.shift());else this.programMapTable===undefined?this.packetsWaitingForPmt.push([t,n,i]):this.processPes_(t,n,i)},this.processPes_=function(e,t,i){i.pid===this.programMapTable.video?i.streamType=u.H264_STREAM_TYPE:i.pid===this.programMapTable.audio?i.streamType=u.ADTS_STREAM_TYPE:i.streamType=this.programMapTable["timed-metadata"][i.pid],i.type="pes",i.data=e.subarray(t),this.trigger("data",i)}},r.prototype=new s,r.STREAM_TYPES={h264:27,adts:15},a=function(){var e=this,t={data:[],size:0},i={data:[],size:0},n={data:[],size:0},r=function(e,t){var i;t.packetLength=6+(e[4]<<8|e[5]),t.dataAlignmentIndicator=0!=(4&e[6]),i=e[7],192&i&&(t.pts=(14&e[9])<<27|(255&e[10])<<20|(254&e[11])<<12|(255&e[12])<<5|(254&e[13])>>>3,t.pts*=4,t.pts+=(6&e[13])>>>1,t.dts=t.pts,64&i&&(t.dts=(14&e[14])<<27|(255&e[15])<<20|(254&e[16])<<12|(255&e[17])<<5|(254&e[18])>>>3,t.dts*=4,t.dts+=(6&e[18])>>>1)),t.data=e.subarray(9+e[8])},s=function(t,i,n){var a,s=new Uint8Array(t.size),o={type:i},u=0,d=0,l=!1;if(t.data.length&&!(t.size<9)){for(o.trackId=t.data[0].pid,u=0;u<t.data.length;u++)a=t.data[u],s.set(a.data,d),d+=a.data.byteLength;r(s,o),l="video"===i||o.packetLength<=t.size,(n||l)&&(t.size=0,t.data.length=0),l&&e.trigger("data",o)}};a.prototype.init.call(this),this.push=function(r){({pat:function(){},pes:function(){var e,a;switch(r.streamType){case u.H264_STREAM_TYPE:case l.H264_STREAM_TYPE:e=t,a="video";break;case u.ADTS_STREAM_TYPE:e=i,a="audio";break;case u.METADATA_STREAM_TYPE:e=n,a="timed-metadata";break;default:return}r.payloadUnitStartIndicator&&s(e,a,!0),e.data.push(r),e.size+=r.data.byteLength},pmt:function(){var t={type:"metadata",tracks:[]},i=r.programMapTable;null!==i.video&&t.tracks.push({timelineStartInfo:{baseMediaDecodeTime:0},id:+i.video,codec:"avc",type:"video"}),null!==i.audio&&t.tracks.push({timelineStartInfo:{baseMediaDecodeTime:0},id:+i.audio,codec:"adts",type:"audio"}),e.trigger("data",t)}})[r.type]()},this.flush=function(){s(t,"video"),s(i,"audio"),s(n,"timed-metadata"),this.trigger("done")}},a.prototype=new s;var f={PAT_PID:0,MP2T_PACKET_LENGTH:188,TransportPacketStream:n,TransportParseStream:r,ElementaryStream:a,TimestampRolloverStream:d,CaptionStream:o.CaptionStream,Cea608Stream:o.Cea608Stream,MetadataStream:e(49)};for(var c in u)u.hasOwnProperty(c)&&(f[c]=u[c]);t.exports=f},{}],49:[function(e,t,i){"use strict";var n,r=e(60),a=e(51),s=function(e,t,i){var n,r="";for(n=t;n<i;n++)r+="%"+("00"+e[n].toString(16)).slice(-2);return r},o=function(e,t,i){return decodeURIComponent(s(e,t,i))},u=function(e,t,i){return unescape(s(e,t,i))},d=function(e){return e[0]<<21|e[1]<<14|e[2]<<7|e[3]},l={TXXX:function(e){var t;if(3===e.data[0]){for(t=1;t<e.data.length;t++)if(0===e.data[t]){e.description=o(e.data,1,t),e.value=o(e.data,t+1,e.data.length).replace(/\0*$/,"");break}e.data=e.value}},WXXX:function(e){var t;if(3===e.data[0])for(t=1;t<e.data.length;t++)if(0===e.data[t]){e.description=o(e.data,1,t),e.url=o(e.data,t+1,e.data.length);break}},PRIV:function(e){var t;for(t=0;t<e.data.length;t++)if(0===e.data[t]){e.owner=u(e.data,0,t);break}e.privateData=e.data.subarray(t+1),e.data=e.privateData}};n=function(e){var t,i={debug:!(!e||!e.debug),descriptor:e&&e.descriptor},r=0,s=[],o=0;if(n.prototype.init.call(this),this.dispatchType=a.METADATA_STREAM_TYPE.toString(16),i.descriptor)for(t=0;t<i.descriptor.length;t++)this.dispatchType+=("00"+i.descriptor[t].toString(16)).slice(-2);this.push=function(e){var t,n,a,u,f,c;if("timed-metadata"===e.type){if(e.dataAlignmentIndicator&&(o=0,s.length=0),0===s.length&&(e.data.length<10||e.data[0]!=="I".charCodeAt(0)||e.data[1]!=="D".charCodeAt(0)||e.data[2]!=="3".charCodeAt(0)))return void(i.debug&&console.log("Skipping unrecognized metadata packet"));if(s.push(e),o+=e.data.byteLength,1===s.length&&(r=d(e.data.subarray(6,10)),r+=10),!(o<r)){for(t={data:new Uint8Array(r),frames:[],pts:s[0].pts,dts:s[0].dts},f=0;f<r;)t.data.set(s[0].data.subarray(0,r-f),f),f+=s[0].data.byteLength,o-=s[0].data.byteLength,s.shift();n=10,64&t.data[5]&&(n+=4,n+=d(t.data.subarray(10,14)),r-=d(t.data.subarray(16,20)));do{if((a=d(t.data.subarray(n+4,n+8)))<1)return console.log("Malformed ID3 frame encountered. Skipping metadata parsing.");if(c=String.fromCharCode(t.data[n],t.data[n+1],t.data[n+2],t.data[n+3]),u={id:c,data:t.data.subarray(n+10,n+a+10)},u.key=u.id,l[u.id]&&(l[u.id](u),"com.apple.streaming.transportStreamTimestamp"===u.owner)){var h=u.data,p=(1&h[3])<<30|h[4]<<22|h[5]<<14|h[6]<<6|h[7]>>>2;p*=4,p+=3&h[7],u.timeStamp=p,t.pts===undefined&&t.dts===undefined&&(t.pts=u.timeStamp,t.dts=u.timeStamp),this.trigger("timestamp",u)}t.frames.push(u),n+=10,n+=a}while(n<r);this.trigger("data",t)}}}},n.prototype=new r,t.exports=n},{}],50:[function(e,t,i){"use strict";var n=e(51),r=function(e){var t=31&e[1];return t<<=8,t|=e[2]},a=function(e){return!!(64&e[1])},s=function(e){var t=0;return(48&e[3])>>>4>1&&(t+=e[4]+1),t},o=function(e,t){var i=r(e);return 0===i?"pat":i===t?"pmt":t?"pes":null},u=function(e){var t=a(e),i=4+s(e);return t&&(i+=e[i]+1),(31&e[i+10])<<8|e[i+11]},d=function(e){var t={},i=a(e),n=4+s(e);if(i&&(n+=e[n]+1),1&e[n+5]){var r,o,u;r=(15&e[n+1])<<8|e[n+2],o=3+r-4,u=(15&e[n+10])<<8|e[n+11];for(var d=12+u;d<o;){var l=n+d;t[(31&e[l+1])<<8|e[l+2]]=e[l],d+=5+((15&e[l+3])<<8|e[l+4])}return t}},l=function(e,t){switch(t[r(e)]){case n.H264_STREAM_TYPE:return"video";case n.ADTS_STREAM_TYPE:return"audio";case n.METADATA_STREAM_TYPE:return"timed-metadata";default:return null}},f=function(e){if(!a(e))return null;var t=4+s(e);if(t>=e.byteLength)return null;var i,n=null;return i=e[t+7],192&i&&(n={},n.pts=(14&e[t+9])<<27|(255&e[t+10])<<20|(254&e[t+11])<<12|(255&e[t+12])<<5|(254&e[t+13])>>>3,n.pts*=4,n.pts+=(6&e[t+13])>>>1,n.dts=n.pts,64&i&&(n.dts=(14&e[t+14])<<27|(255&e[t+15])<<20|(254&e[t+16])<<12|(255&e[t+17])<<5|(254&e[t+18])>>>3,n.dts*=4,n.dts+=(6&e[t+18])>>>1)),n},c=function(e){switch(e){case 5:return"slice_layer_without_partitioning_rbsp_idr";case 6:return"sei_rbsp";case 7:return"seq_parameter_set_rbsp";case 8:return"pic_parameter_set_rbsp";case 9:return"access_unit_delimiter_rbsp";default:return null}},h=function(e){for(var t,i=4+s(e),n=e.subarray(i),r=0,a=0,o=!1;a<n.byteLength-3;a++)if(1===n[a+2]){r=a+5;break}for(;r<n.byteLength;)switch(n[r]){case 0:if(0!==n[r-1]){r+=2;break}if(0!==n[r-2]){r++;break}a+3!==r-2&&"slice_layer_without_partitioning_rbsp_idr"===(t=c(31&n[a+3]))&&(o=!0);do{r++}while(1!==n[r]&&r<n.length);a=r-2,r+=3;break;case 1:if(0!==n[r-1]||0!==n[r-2]){r+=3;break}t=c(31&n[a+3]),"slice_layer_without_partitioning_rbsp_idr"===t&&(o=!0),a=r-2,r+=3;break;default:r+=3}return n=n.subarray(a),r-=a,a=0,n&&n.byteLength>3&&"slice_layer_without_partitioning_rbsp_idr"===(t=c(31&n[a+3]))&&(o=!0),o};t.exports={parseType:o,parsePat:u,parsePmt:d,parsePayloadUnitStartIndicator:a,parsePesType:l,parsePesTime:f,videoPacketContainsKeyFrame:h}},{}],51:[function(e,t,i){"use strict";t.exports={H264_STREAM_TYPE:27,ADTS_STREAM_TYPE:15,METADATA_STREAM_TYPE:21}},{}],52:[function(e,t,i){"use strict";var n=e(60),r=function(e,t){var i=1;for(e>t&&(i=-1);Math.abs(t-e)>4294967296;)e+=8589934592*i;return e},a=function(e){var t,i;a.prototype.init.call(this),this.type_=e,this.push=function(e){e.type===this.type_&&(i===undefined&&(i=e.dts),e.dts=r(e.dts,i),e.pts=r(e.pts,i),t=e.dts,this.trigger("data",e))},this.flush=function(){i=t,this.trigger("done")},this.discontinuity=function(){i=void 0,t=void 0}};a.prototype=new n,t.exports={TimestampRolloverStream:a,handleRollover:r}},{}],53:[function(e,t,i){t.exports={generator:e(54),Transmuxer:e(56).Transmuxer,AudioSegmentStream:e(56).AudioSegmentStream,VideoSegmentStream:e(56).VideoSegmentStream}},{}],54:[function(e,t,i){"use strict";var n,r,a,s,o,u,d,l,f,c,h,p,m,g,y,_,v,b,T,S,w,k,E,O,A,L,P,I,C,U,M,D,R,x,B,j,N=Math.pow(2,32)-1;!function(){var e;if(E={avc1:[],avcC:[],btrt:[],dinf:[],dref:[],esds:[],ftyp:[],hdlr:[],mdat:[],mdhd:[],mdia:[],mfhd:[],minf:[],moof:[],moov:[],mp4a:[],mvex:[],mvhd:[],sdtp:[],smhd:[],stbl:[],stco:[],stsc:[],stsd:[],stsz:[],stts:[],styp:[],tfdt:[],tfhd:[],traf:[],trak:[],trun:[],trex:[],tkhd:[],vmhd:[]},"undefined"!=typeof Uint8Array){for(e in E)E.hasOwnProperty(e)&&(E[e]=[e.charCodeAt(0),e.charCodeAt(1),e.charCodeAt(2),e.charCodeAt(3)]);O=new Uint8Array(["i".charCodeAt(0),"s".charCodeAt(0),"o".charCodeAt(0),"m".charCodeAt(0)]),L=new Uint8Array(["a".charCodeAt(0),"v".charCodeAt(0),"c".charCodeAt(0),"1".charCodeAt(0)]),A=new Uint8Array([0,0,0,1]),
P=new Uint8Array([0,0,0,0,0,0,0,0,118,105,100,101,0,0,0,0,0,0,0,0,0,0,0,0,86,105,100,101,111,72,97,110,100,108,101,114,0]),I=new Uint8Array([0,0,0,0,0,0,0,0,115,111,117,110,0,0,0,0,0,0,0,0,0,0,0,0,83,111,117,110,100,72,97,110,100,108,101,114,0]),C={video:P,audio:I},D=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,12,117,114,108,32,0,0,0,1]),M=new Uint8Array([0,0,0,0,0,0,0,0]),R=new Uint8Array([0,0,0,0,0,0,0,0]),x=R,B=new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0]),j=R,U=new Uint8Array([0,0,0,1,0,0,0,0,0,0,0,0])}}(),n=function(e){var t,i,n,r=[],a=0;for(t=1;t<arguments.length;t++)r.push(arguments[t]);for(t=r.length;t--;)a+=r[t].byteLength;for(i=new Uint8Array(a+8),n=new DataView(i.buffer,i.byteOffset,i.byteLength),n.setUint32(0,i.byteLength),i.set(e,4),t=0,a=8;t<r.length;t++)i.set(r[t],a),a+=r[t].byteLength;return i},r=function(){return n(E.dinf,n(E.dref,D))},a=function(e){return n(E.esds,new Uint8Array([0,0,0,0,3,25,0,0,0,4,17,64,21,0,6,0,0,0,218,192,0,0,218,192,5,2,e.audioobjecttype<<3|e.samplingfrequencyindex>>>1,e.samplingfrequencyindex<<7|e.channelcount<<3,6,1,2]))},s=function(){return n(E.ftyp,O,A,O,L)},_=function(e){return n(E.hdlr,C[e])},o=function(e){return n(E.mdat,e)},y=function(e){var t=new Uint8Array([0,0,0,0,0,0,0,2,0,0,0,3,0,1,95,144,e.duration>>>24&255,e.duration>>>16&255,e.duration>>>8&255,255&e.duration,85,196,0,0]);return e.samplerate&&(t[12]=e.samplerate>>>24&255,t[13]=e.samplerate>>>16&255,t[14]=e.samplerate>>>8&255,t[15]=255&e.samplerate),n(E.mdhd,t)},g=function(e){return n(E.mdia,y(e),_(e.type),d(e))},u=function(e){return n(E.mfhd,new Uint8Array([0,0,0,0,(4278190080&e)>>24,(16711680&e)>>16,(65280&e)>>8,255&e]))},d=function(e){return n(E.minf,"video"===e.type?n(E.vmhd,U):n(E.smhd,M),r(),b(e))},l=function(e,t){for(var i=[],r=t.length;r--;)i[r]=S(t[r]);return n.apply(null,[E.moof,u(e)].concat(i))},f=function(e){for(var t=e.length,i=[];t--;)i[t]=p(e[t]);return n.apply(null,[E.moov,h(4294967295)].concat(i).concat(c(e)))},c=function(e){for(var t=e.length,i=[];t--;)i[t]=w(e[t]);return n.apply(null,[E.mvex].concat(i))},h=function(e){var t=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,2,0,1,95,144,(4278190080&e)>>24,(16711680&e)>>16,(65280&e)>>8,255&e,0,1,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,64,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,255,255,255,255]);return n(E.mvhd,t)},v=function(e){var t,i,r=e.samples||[],a=new Uint8Array(4+r.length);for(i=0;i<r.length;i++)t=r[i].flags,a[i+4]=t.dependsOn<<4|t.isDependedOn<<2|t.hasRedundancy;return n(E.sdtp,a)},b=function(e){return n(E.stbl,T(e),n(E.stts,j),n(E.stsc,x),n(E.stsz,B),n(E.stco,R))},function(){var e,t;T=function(i){return n(E.stsd,new Uint8Array([0,0,0,0,0,0,0,1]),"video"===i.type?e(i):t(i))},e=function(e){var t,i=e.sps||[],r=e.pps||[],a=[],s=[];for(t=0;t<i.length;t++)a.push((65280&i[t].byteLength)>>>8),a.push(255&i[t].byteLength),a=a.concat(Array.prototype.slice.call(i[t]));for(t=0;t<r.length;t++)s.push((65280&r[t].byteLength)>>>8),s.push(255&r[t].byteLength),s=s.concat(Array.prototype.slice.call(r[t]));return n(E.avc1,new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,(65280&e.width)>>8,255&e.width,(65280&e.height)>>8,255&e.height,0,72,0,0,0,72,0,0,0,0,0,0,0,1,19,118,105,100,101,111,106,115,45,99,111,110,116,114,105,98,45,104,108,115,0,0,0,0,0,0,0,0,0,0,0,0,0,24,17,17]),n(E.avcC,new Uint8Array([1,e.profileIdc,e.profileCompatibility,e.levelIdc,255].concat([i.length]).concat(a).concat([r.length]).concat(s))),n(E.btrt,new Uint8Array([0,28,156,128,0,45,198,192,0,45,198,192])))},t=function(e){return n(E.mp4a,new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,(65280&e.channelcount)>>8,255&e.channelcount,(65280&e.samplesize)>>8,255&e.samplesize,0,0,0,0,(65280&e.samplerate)>>8,255&e.samplerate,0,0]),a(e))}}(),m=function(e){var t=new Uint8Array([0,0,0,7,0,0,0,0,0,0,0,0,(4278190080&e.id)>>24,(16711680&e.id)>>16,(65280&e.id)>>8,255&e.id,0,0,0,0,(4278190080&e.duration)>>24,(16711680&e.duration)>>16,(65280&e.duration)>>8,255&e.duration,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,64,0,0,0,(65280&e.width)>>8,255&e.width,0,0,(65280&e.height)>>8,255&e.height,0,0]);return n(E.tkhd,t)},S=function(e){var t,i,r,a,s,o,u;return t=n(E.tfhd,new Uint8Array([0,0,0,58,(4278190080&e.id)>>24,(16711680&e.id)>>16,(65280&e.id)>>8,255&e.id,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0])),o=Math.floor(e.baseMediaDecodeTime/(N+1)),u=Math.floor(e.baseMediaDecodeTime%(N+1)),i=n(E.tfdt,new Uint8Array([1,0,0,0,o>>>24&255,o>>>16&255,o>>>8&255,255&o,u>>>24&255,u>>>16&255,u>>>8&255,255&u])),s=92,"audio"===e.type?(r=k(e,s),n(E.traf,t,i,r)):(a=v(e),r=k(e,a.length+s),n(E.traf,t,i,r,a))},p=function(e){return e.duration=e.duration||4294967295,n(E.trak,m(e),g(e))},w=function(e){var t=new Uint8Array([0,0,0,0,(4278190080&e.id)>>24,(16711680&e.id)>>16,(65280&e.id)>>8,255&e.id,0,0,0,1,0,0,0,0,0,0,0,0,0,1,0,1]);return"video"!==e.type&&(t[t.length-1]=0),n(E.trex,t)},function(){var e,t,i;i=function(e,t){var i=0,n=0,r=0,a=0;return e.length&&(e[0].duration!==undefined&&(i=1),e[0].size!==undefined&&(n=2),e[0].flags!==undefined&&(r=4),e[0].compositionTimeOffset!==undefined&&(a=8)),[0,0,i|n|r|a,1,(4278190080&e.length)>>>24,(16711680&e.length)>>>16,(65280&e.length)>>>8,255&e.length,(4278190080&t)>>>24,(16711680&t)>>>16,(65280&t)>>>8,255&t]},t=function(e,t){var r,a,s,o;for(a=e.samples||[],t+=20+16*a.length,r=i(a,t),o=0;o<a.length;o++)s=a[o],r=r.concat([(4278190080&s.duration)>>>24,(16711680&s.duration)>>>16,(65280&s.duration)>>>8,255&s.duration,(4278190080&s.size)>>>24,(16711680&s.size)>>>16,(65280&s.size)>>>8,255&s.size,s.flags.isLeading<<2|s.flags.dependsOn,s.flags.isDependedOn<<6|s.flags.hasRedundancy<<4|s.flags.paddingValue<<1|s.flags.isNonSyncSample,61440&s.flags.degradationPriority,15&s.flags.degradationPriority,(4278190080&s.compositionTimeOffset)>>>24,(16711680&s.compositionTimeOffset)>>>16,(65280&s.compositionTimeOffset)>>>8,255&s.compositionTimeOffset]);return n(E.trun,new Uint8Array(r))},e=function(e,t){var r,a,s,o;for(a=e.samples||[],t+=20+8*a.length,r=i(a,t),o=0;o<a.length;o++)s=a[o],r=r.concat([(4278190080&s.duration)>>>24,(16711680&s.duration)>>>16,(65280&s.duration)>>>8,255&s.duration,(4278190080&s.size)>>>24,(16711680&s.size)>>>16,(65280&s.size)>>>8,255&s.size]);return n(E.trun,new Uint8Array(r))},k=function(i,n){return"audio"===i.type?e(i,n):t(i,n)}}(),t.exports={ftyp:s,mdat:o,moof:l,moov:f,initSegment:function(e){var t,i=s(),n=f(e);return t=new Uint8Array(i.byteLength+n.byteLength),t.set(i),t.set(n,i.byteLength),t}}},{}],55:[function(e,t,i){"use strict";var n,r,a,s;n=function(e,t){var i,a,s,o,u,d=[];if(!t.length)return null;for(i=0;i<e.byteLength;)a=e[i]<<24,a|=e[i+1]<<16,a|=e[i+2]<<8,a|=e[i+3],s=r(e.subarray(i+4,i+8)),o=a>1?i+a:e.byteLength,s===t[0]&&(1===t.length?d.push(e.subarray(i+8,o)):(u=n(e.subarray(i+8,o),t.slice(1)),u.length&&(d=d.concat(u)))),i=o;return d},r=function(e){var t="";return t+=String.fromCharCode(e[0]),t+=String.fromCharCode(e[1]),t+=String.fromCharCode(e[2]),t+=String.fromCharCode(e[3])},a=function(e){var t={};return n(e,["moov","trak"]).reduce(function(e,t){var i,r,a,s,o;return(i=n(t,["tkhd"])[0])?(r=i[0],a=0===r?12:20,s=i[a]<<24|i[a+1]<<16|i[a+2]<<8|i[a+3],(o=n(t,["mdia","mdhd"])[0])?(r=o[0],a=0===r?12:20,e[s]=o[a]<<24|o[a+1]<<16|o[a+2]<<8|o[a+3],e):null):null},t)},s=function(e,t){var i,r,a;return i=n(t,["moof","traf"]),r=[].concat.apply([],i.map(function(t){return n(t,["tfhd"]).map(function(i){var r,a,s;return r=i[4]<<24|i[5]<<16|i[6]<<8|i[7],a=e[r]||9e4,s=n(t,["tfdt"]).map(function(e){var t,i;return t=e[0],i=e[4]<<24|e[5]<<16|e[6]<<8|e[7],1===t&&(i*=Math.pow(2,32),i+=e[8]<<24|e[9]<<16|e[10]<<8|e[11]),i})[0],(s=s||Infinity)/a})})),a=Math.min.apply(null,r),isFinite(a)?a:0},t.exports={parseType:r,timescale:a,startTime:s}},{}],56:[function(e,t,i){"use strict";var n,r,a,s,o,u,d,l,f,c,h,p=e(60),m=e(54),g=e(48),y=e(38),_=e(39).H264Stream,v=e(36),b=e(40),T=e(58),S=["audioobjecttype","channelcount","samplerate","samplingfrequencyindex","samplesize"],w=["width","height","profileIdc","levelIdc","profileCompatibility"];o=function(){return{size:0,flags:{isLeading:0,dependsOn:1,isDependedOn:0,hasRedundancy:0,degradationPriority:0}}},u=function(e){return e[0]==="I".charCodeAt(0)&&e[1]==="D".charCodeAt(0)&&e[2]==="3".charCodeAt(0)},c=function(e,t){var i;if(e.length!==t.length)return!1;for(i=0;i<e.length;i++)if(e[i]!==t[i])return!1;return!0},h=function(e){var t,i,n=0;for(t=0;t<e.length;t++)i=e[t],n+=i.data.byteLength;return n},r=function(e){var t=[],i=0,n=0,a=0,s=Infinity;r.prototype.init.call(this),this.push=function(i){d(e,i),e&&S.forEach(function(t){e[t]=i[t]}),t.push(i)},this.setEarliestDts=function(t){n=t-e.timelineStartInfo.baseMediaDecodeTime},this.setVideoBaseMediaDecodeTime=function(e){s=e},this.setAudioAppendStart=function(e){a=e},this.flush=function(){var n,r,a,s;if(0===t.length)return void this.trigger("done","AudioSegmentStream");n=this.trimAdtsFramesByEarliestDts_(t),e.baseMediaDecodeTime=f(e),this.prefixWithSilence_(e,n),e.samples=this.generateSampleTable_(n),a=m.mdat(this.concatenateFrameData_(n)),t=[],r=m.moof(i,[e]),s=new Uint8Array(r.byteLength+a.byteLength),i++,s.set(r),s.set(a,r.byteLength),l(e),this.trigger("data",{track:e,boxes:s}),this.trigger("done","AudioSegmentStream")},this.prefixWithSilence_=function(e,t){var i,n,r,o=0,u=0,d=0,l=0;if(t.length&&(i=T.audioTsToVideoTs(e.baseMediaDecodeTime,e.samplerate),o=Math.ceil(9e4/(e.samplerate/1024)),a&&s&&(u=i-Math.max(a,s),d=Math.floor(u/o),l=d*o),!(d<1||l>45e3))){for(n=b[e.samplerate],n||(n=t[0].data),r=0;r<d;r++)t.splice(r,0,{data:n});e.baseMediaDecodeTime-=Math.floor(T.videoTsToAudioTs(l,e.samplerate))}},this.trimAdtsFramesByEarliestDts_=function(t){return e.minSegmentDts>=n?t:(e.minSegmentDts=Infinity,t.filter(function(t){return t.dts>=n&&(e.minSegmentDts=Math.min(e.minSegmentDts,t.dts),e.minSegmentPts=e.minSegmentDts,!0)}))},this.generateSampleTable_=function(e){var t,i,n=[];for(t=0;t<e.length;t++)i=e[t],n.push({size:i.data.byteLength,duration:1024});return n},this.concatenateFrameData_=function(e){var t,i,n=0,r=new Uint8Array(h(e));for(t=0;t<e.length;t++)i=e[t],r.set(i.data,n),n+=i.data.byteLength;return r}},r.prototype=new p,n=function(e,t){var i,r,a=0,s=[],u=[];t=t||{},n.prototype.init.call(this),delete e.minPTS,this.gopCache_=[],this.push=function(t){d(e,t),"seq_parameter_set_rbsp"!==t.nalUnitType||i||(i=t.config,e.sps=[t.data],w.forEach(function(t){e[t]=i[t]},this)),"pic_parameter_set_rbsp"!==t.nalUnitType||r||(r=t.data,e.pps=[t.data]),s.push(t)},this.flush=function(){for(var i,n,r,o,c,h;s.length&&"access_unit_delimiter_rbsp"!==s[0].nalUnitType;)s.shift();if(0===s.length)return this.resetStream_(),void this.trigger("done","VideoSegmentStream");if(i=this.groupNalsIntoFrames_(s),r=this.groupFramesIntoGops_(i),r[0][0].keyFrame||(n=this.getGopForFusion_(s[0],e),n?(r.unshift(n),r.byteLength+=n.byteLength,r.nalCount+=n.nalCount,r.pts=n.pts,r.dts=n.dts,r.duration+=n.duration):r=this.extendFirstKeyFrame_(r)),u.length){var p;if(!(p=t.alignGopsAtEnd?this.alignGopsAtEnd_(r):this.alignGopsAtStart_(r)))return this.gopCache_.unshift({gop:r.pop(),pps:e.pps,sps:e.sps}),this.gopCache_.length=Math.min(6,this.gopCache_.length),s=[],this.resetStream_(),void this.trigger("done","VideoSegmentStream");l(e),r=p}d(e,r),e.samples=this.generateSampleTable_(r),c=m.mdat(this.concatenateNalData_(r)),e.baseMediaDecodeTime=f(e),this.trigger("processedGopsInfo",r.map(function(e){return{pts:e.pts,dts:e.dts,byteLength:e.byteLength}})),this.gopCache_.unshift({gop:r.pop(),pps:e.pps,sps:e.sps}),this.gopCache_.length=Math.min(6,this.gopCache_.length),s=[],this.trigger("baseMediaDecodeTime",e.baseMediaDecodeTime),this.trigger("timelineStartInfo",e.timelineStartInfo),o=m.moof(a,[e]),h=new Uint8Array(o.byteLength+c.byteLength),a++,h.set(o),h.set(c,o.byteLength),this.trigger("data",{track:e,boxes:h}),this.resetStream_(),this.trigger("done","VideoSegmentStream")},this.resetStream_=function(){l(e),i=undefined,r=undefined},this.getGopForFusion_=function(t){var i,n,r,a,s,o=Infinity;for(s=0;s<this.gopCache_.length;s++)a=this.gopCache_[s],r=a.gop,e.pps&&c(e.pps[0],a.pps[0])&&e.sps&&c(e.sps[0],a.sps[0])&&(r.dts<e.timelineStartInfo.dts||(i=t.dts-r.dts-r.duration)>=-1e4&&i<=45e3&&(!n||o>i)&&(n=a,o=i));return n?n.gop:null},this.extendFirstKeyFrame_=function(e){var t;return!e[0][0].keyFrame&&e.length>1&&(t=e.shift(),e.byteLength-=t.byteLength,e.nalCount-=t.nalCount,e[0][0].dts=t.dts,e[0][0].pts=t.pts,e[0][0].duration+=t.duration),e},this.groupNalsIntoFrames_=function(e){var t,i,n=[],r=[];for(n.byteLength=0,t=0;t<e.length;t++)i=e[t],"access_unit_delimiter_rbsp"===i.nalUnitType?(n.length&&(n.duration=i.dts-n.dts,r.push(n)),n=[i],n.byteLength=i.data.byteLength,n.pts=i.pts,n.dts=i.dts):("slice_layer_without_partitioning_rbsp_idr"===i.nalUnitType&&(n.keyFrame=!0),n.duration=i.dts-n.dts,n.byteLength+=i.data.byteLength,n.push(i));return r.length&&(!n.duration||n.duration<=0)&&(n.duration=r[r.length-1].duration),r.push(n),r},this.groupFramesIntoGops_=function(e){var t,i,n=[],r=[];for(n.byteLength=0,n.nalCount=0,n.duration=0,n.pts=e[0].pts,n.dts=e[0].dts,r.byteLength=0,r.nalCount=0,r.duration=0,r.pts=e[0].pts,r.dts=e[0].dts,t=0;t<e.length;t++)i=e[t],i.keyFrame?(n.length&&(r.push(n),r.byteLength+=n.byteLength,r.nalCount+=n.nalCount,r.duration+=n.duration),n=[i],n.nalCount=i.length,n.byteLength=i.byteLength,n.pts=i.pts,n.dts=i.dts,n.duration=i.duration):(n.duration+=i.duration,n.nalCount+=i.length,n.byteLength+=i.byteLength,n.push(i));return r.length&&n.duration<=0&&(n.duration=r[r.length-1].duration),r.byteLength+=n.byteLength,r.nalCount+=n.nalCount,r.duration+=n.duration,r.push(n),r},this.generateSampleTable_=function(e,t){var i,n,r,a,s,u=t||0,d=[];for(i=0;i<e.length;i++)for(a=e[i],n=0;n<a.length;n++)s=a[n],r=o(),r.dataOffset=u,r.compositionTimeOffset=s.pts-s.dts,r.duration=s.duration,r.size=4*s.length,r.size+=s.byteLength,s.keyFrame&&(r.flags.dependsOn=2),u+=r.size,d.push(r);return d},this.concatenateNalData_=function(e){var t,i,n,r,a,s,o=0,u=e.byteLength,d=e.nalCount,l=u+4*d,f=new Uint8Array(l),c=new DataView(f.buffer);for(t=0;t<e.length;t++)for(r=e[t],i=0;i<r.length;i++)for(a=r[i],n=0;n<a.length;n++)s=a[n],c.setUint32(o,s.data.byteLength),o+=4,f.set(s.data,o),o+=s.data.byteLength;return f},this.alignGopsAtStart_=function(e){var t,i,n,r,a,s,o,d;for(a=e.byteLength,s=e.nalCount,o=e.duration,t=i=0;t<u.length&&i<e.length&&(n=u[t],r=e[i],n.pts!==r.pts);)r.pts>n.pts?t++:(i++,a-=r.byteLength,s-=r.nalCount,o-=r.duration);return 0===i?e:i===e.length?null:(d=e.slice(i),d.byteLength=a,d.duration=o,d.nalCount=s,d.pts=d[0].pts,d.dts=d[0].dts,d)},this.alignGopsAtEnd_=function(e){var t,i,n,r,a,s;for(t=u.length-1,i=e.length-1,a=null,s=!1;t>=0&&i>=0;){if(n=u[t],r=e[i],n.pts===r.pts){s=!0;break}n.pts>r.pts?t--:(t===u.length-1&&(a=i),i--)}if(!s&&null===a)return null;var o;if(0===(o=s?i:a))return e;var d=e.slice(o),l=d.reduce(function(e,t){return e.byteLength+=t.byteLength,e.duration+=t.duration,e.nalCount+=t.nalCount,e},{byteLength:0,duration:0,nalCount:0});return d.byteLength=l.byteLength,d.duration=l.duration,d.nalCount=l.nalCount,d.pts=d[0].pts,d.dts=d[0].dts,d},this.alignGopsWith=function(e){u=e}},n.prototype=new p,d=function(e,t){"number"==typeof t.pts&&(e.timelineStartInfo.pts===undefined&&(e.timelineStartInfo.pts=t.pts),e.minSegmentPts===undefined?e.minSegmentPts=t.pts:e.minSegmentPts=Math.min(e.minSegmentPts,t.pts),e.maxSegmentPts===undefined?e.maxSegmentPts=t.pts:e.maxSegmentPts=Math.max(e.maxSegmentPts,t.pts)),"number"==typeof t.dts&&(e.timelineStartInfo.dts===undefined&&(e.timelineStartInfo.dts=t.dts),e.minSegmentDts===undefined?e.minSegmentDts=t.dts:e.minSegmentDts=Math.min(e.minSegmentDts,t.dts),e.maxSegmentDts===undefined?e.maxSegmentDts=t.dts:e.maxSegmentDts=Math.max(e.maxSegmentDts,t.dts))},l=function(e){delete e.minSegmentDts,delete e.maxSegmentDts,delete e.minSegmentPts,delete e.maxSegmentPts},f=function(e){var t,i,n=e.minSegmentDts-e.timelineStartInfo.dts;return t=e.timelineStartInfo.baseMediaDecodeTime,t+=n,t=Math.max(0,t),"audio"===e.type&&(i=e.samplerate/9e4,t*=i,t=Math.floor(t)),t},s=function(e,t){this.numberOfTracks=0,this.metadataStream=t,"undefined"!=typeof e.remux?this.remuxTracks=!!e.remux:this.remuxTracks=!0,this.pendingTracks=[],this.videoTrack=null,this.pendingBoxes=[],this.pendingCaptions=[],this.pendingMetadata=[],this.pendingBytes=0,this.emittedTracks=0,s.prototype.init.call(this),this.push=function(e){return e.text?this.pendingCaptions.push(e):e.frames?this.pendingMetadata.push(e):(this.pendingTracks.push(e.track),this.pendingBoxes.push(e.boxes),this.pendingBytes+=e.boxes.byteLength,"video"===e.track.type&&(this.videoTrack=e.track),void("audio"===e.track.type&&(this.audioTrack=e.track)))}},s.prototype=new p,s.prototype.flush=function(e){var t,i,n,r,a=0,s={captions:[],captionStreams:{},metadata:[],info:{}},o=0;if(this.pendingTracks.length<this.numberOfTracks){if("VideoSegmentStream"!==e&&"AudioSegmentStream"!==e)return;if(this.remuxTracks)return;if(0===this.pendingTracks.length)return void(++this.emittedTracks>=this.numberOfTracks&&(this.trigger("done"),this.emittedTracks=0))}for(this.videoTrack?(o=this.videoTrack.timelineStartInfo.pts,w.forEach(function(e){s.info[e]=this.videoTrack[e]},this)):this.audioTrack&&(o=this.audioTrack.timelineStartInfo.pts,S.forEach(function(e){s.info[e]=this.audioTrack[e]},this)),1===this.pendingTracks.length?s.type=this.pendingTracks[0].type:s.type="combined",this.emittedTracks+=this.pendingTracks.length,n=m.initSegment(this.pendingTracks),s.initSegment=new Uint8Array(n.byteLength),s.initSegment.set(n),s.data=new Uint8Array(this.pendingBytes),r=0;r<this.pendingBoxes.length;r++)s.data.set(this.pendingBoxes[r],a),a+=this.pendingBoxes[r].byteLength;for(r=0;r<this.pendingCaptions.length;r++)t=this.pendingCaptions[r],t.startTime=t.startPts-o,t.startTime/=9e4,t.endTime=t.endPts-o,t.endTime/=9e4,s.captionStreams[t.stream]=!0,s.captions.push(t);for(r=0;r<this.pendingMetadata.length;r++)i=this.pendingMetadata[r],i.cueTime=i.pts-o,i.cueTime/=9e4,s.metadata.push(i);s.metadata.dispatchType=this.metadataStream.dispatchType,this.pendingTracks.length=0,this.videoTrack=null,this.pendingBoxes.length=0,this.pendingCaptions.length=0,this.pendingBytes=0,this.pendingMetadata.length=0,this.trigger("data",s),this.emittedTracks>=this.numberOfTracks&&(this.trigger("done"),this.emittedTracks=0)},a=function(e){var t,i,o=this,d=!0;a.prototype.init.call(this),e=e||{},this.baseMediaDecodeTime=e.baseMediaDecodeTime||0,this.transmuxPipeline_={},this.setupAacPipeline=function(){var t={};this.transmuxPipeline_=t,t.type="aac",t.metadataStream=new g.MetadataStream,t.aacStream=new v,t.audioTimestampRolloverStream=new g.TimestampRolloverStream("audio"),t.timedMetadataTimestampRolloverStream=new g.TimestampRolloverStream("timed-metadata"),t.adtsStream=new y,t.coalesceStream=new s(e,t.metadataStream),t.headOfPipeline=t.aacStream,t.aacStream.pipe(t.audioTimestampRolloverStream).pipe(t.adtsStream),t.aacStream.pipe(t.timedMetadataTimestampRolloverStream).pipe(t.metadataStream).pipe(t.coalesceStream),t.metadataStream.on("timestamp",function(e){t.aacStream.setTimestamp(e.timeStamp)}),t.aacStream.on("data",function(e){"timed-metadata"!==e.type||t.audioSegmentStream||(i=i||{timelineStartInfo:{baseMediaDecodeTime:o.baseMediaDecodeTime},codec:"adts",type:"audio"},t.coalesceStream.numberOfTracks++,t.audioSegmentStream=new r(i),t.adtsStream.pipe(t.audioSegmentStream).pipe(t.coalesceStream))}),t.coalesceStream.on("data",this.trigger.bind(this,"data")),t.coalesceStream.on("done",this.trigger.bind(this,"done"))},this.setupTsPipeline=function(){var a={};this.transmuxPipeline_=a,a.type="ts",a.metadataStream=new g.MetadataStream,a.packetStream=new g.TransportPacketStream,a.parseStream=new g.TransportParseStream,a.elementaryStream=new g.ElementaryStream,a.videoTimestampRolloverStream=new g.TimestampRolloverStream("video"),a.audioTimestampRolloverStream=new g.TimestampRolloverStream("audio"),a.timedMetadataTimestampRolloverStream=new g.TimestampRolloverStream("timed-metadata"),a.adtsStream=new y,a.h264Stream=new _,a.captionStream=new g.CaptionStream,a.coalesceStream=new s(e,a.metadataStream),a.headOfPipeline=a.packetStream,a.packetStream.pipe(a.parseStream).pipe(a.elementaryStream),a.elementaryStream.pipe(a.videoTimestampRolloverStream).pipe(a.h264Stream),a.elementaryStream.pipe(a.audioTimestampRolloverStream).pipe(a.adtsStream),a.elementaryStream.pipe(a.timedMetadataTimestampRolloverStream).pipe(a.metadataStream).pipe(a.coalesceStream),a.h264Stream.pipe(a.captionStream).pipe(a.coalesceStream),a.elementaryStream.on("data",function(s){var u;if("metadata"===s.type){for(u=s.tracks.length;u--;)t||"video"!==s.tracks[u].type?i||"audio"!==s.tracks[u].type||(i=s.tracks[u],i.timelineStartInfo.baseMediaDecodeTime=o.baseMediaDecodeTime):(t=s.tracks[u],t.timelineStartInfo.baseMediaDecodeTime=o.baseMediaDecodeTime);t&&!a.videoSegmentStream&&(a.coalesceStream.numberOfTracks++,a.videoSegmentStream=new n(t,e),a.videoSegmentStream.on("timelineStartInfo",function(e){i&&(i.timelineStartInfo=e,a.audioSegmentStream.setEarliestDts(e.dts))}),a.videoSegmentStream.on("processedGopsInfo",o.trigger.bind(o,"gopInfo")),a.videoSegmentStream.on("baseMediaDecodeTime",function(e){i&&a.audioSegmentStream.setVideoBaseMediaDecodeTime(e)}),a.h264Stream.pipe(a.videoSegmentStream).pipe(a.coalesceStream)),i&&!a.audioSegmentStream&&(a.coalesceStream.numberOfTracks++,a.audioSegmentStream=new r(i),a.adtsStream.pipe(a.audioSegmentStream).pipe(a.coalesceStream))}}),a.coalesceStream.on("data",this.trigger.bind(this,"data")),a.coalesceStream.on("done",this.trigger.bind(this,"done"))},this.setBaseMediaDecodeTime=function(e){var n=this.transmuxPipeline_;this.baseMediaDecodeTime=e,i&&(i.timelineStartInfo.dts=undefined,i.timelineStartInfo.pts=undefined,l(i),i.timelineStartInfo.baseMediaDecodeTime=e,n.audioTimestampRolloverStream&&n.audioTimestampRolloverStream.discontinuity()),t&&(n.videoSegmentStream&&(n.videoSegmentStream.gopCache_=[],n.videoTimestampRolloverStream.discontinuity()),t.timelineStartInfo.dts=undefined,t.timelineStartInfo.pts=undefined,l(t),n.captionStream.reset(),t.timelineStartInfo.baseMediaDecodeTime=e),n.timedMetadataTimestampRolloverStream&&n.timedMetadataTimestampRolloverStream.discontinuity()},this.setAudioAppendStart=function(e){i&&this.transmuxPipeline_.audioSegmentStream.setAudioAppendStart(e)},this.alignGopsWith=function(e){t&&this.transmuxPipeline_.videoSegmentStream&&this.transmuxPipeline_.videoSegmentStream.alignGopsWith(e)},this.push=function(e){if(d){var t=u(e);t&&"aac"!==this.transmuxPipeline_.type?this.setupAacPipeline():t||"ts"===this.transmuxPipeline_.type||this.setupTsPipeline(),d=!1}this.transmuxPipeline_.headOfPipeline.push(e)},this.flush=function(){d=!0,this.transmuxPipeline_.headOfPipeline.flush()},this.resetCaptions=function(){this.transmuxPipeline_.captionStream&&this.transmuxPipeline_.captionStream.reset()}},a.prototype=new p,t.exports={Transmuxer:a,VideoSegmentStream:n,AudioSegmentStream:r,AUDIO_PROPERTIES:S,VIDEO_PROPERTIES:w}},{}],57:[function(e,t,i){"use strict";var n=e(51),r=e(52).handleRollover,a={};a.ts=e(50),a.aac=e(37);var s=function(e){return e[0]==="I".charCodeAt(0)&&e[1]==="D".charCodeAt(0)&&e[2]==="3".charCodeAt(0)},o=function(e,t){for(var i,n=0,r=188;r<e.byteLength;)if(71!==e[n]||71!==e[r])n++,r++;else{switch(i=e.subarray(n,r),a.ts.parseType(i,t.pid)){case"pat":t.pid||(t.pid=a.ts.parsePat(i));break;case"pmt":t.table||(t.table=a.ts.parsePmt(i))}if(t.pid&&t.table)return;n+=188,r+=188}},u=function(e,t,i){for(var n,r,s,o,u=0,d=188,l=!1;d<e.byteLength;)if(71!==e[u]||71!==e[d])u++,d++;else{switch(n=e.subarray(u,d),a.ts.parseType(n,t.pid)){case"pes":r=a.ts.parsePesType(n,t.table),s=a.ts.parsePayloadUnitStartIndicator(n),"audio"===r&&s&&(o=a.ts.parsePesTime(n))&&(o.type="audio",i.audio.push(o),l=!0)}if(l)break;u+=188,d+=188}for(d=e.byteLength,u=d-188,l=!1;u>=0;)if(71!==e[u]||71!==e[d])u--,d--;else{switch(n=e.subarray(u,d),a.ts.parseType(n,t.pid)){case"pes":r=a.ts.parsePesType(n,t.table),s=a.ts.parsePayloadUnitStartIndicator(n),"audio"===r&&s&&(o=a.ts.parsePesTime(n))&&(o.type="audio",i.audio.push(o),l=!0)}if(l)break;u-=188,d-=188}},d=function(e,t,i){for(var n,r,s,o,u,d,l,f=0,c=188,h=!1,p={data:[],size:0};c<e.byteLength;)if(71!==e[f]||71!==e[c])f++,c++;else{switch(n=e.subarray(f,c),a.ts.parseType(n,t.pid)){case"pes":if(r=a.ts.parsePesType(n,t.table),s=a.ts.parsePayloadUnitStartIndicator(n),"video"===r&&(s&&!h&&(o=a.ts.parsePesTime(n))&&(o.type="video",i.video.push(o),h=!0),!i.firstKeyFrame)){if(s&&0!==p.size){for(u=new Uint8Array(p.size),d=0;p.data.length;)l=p.data.shift(),u.set(l,d),d+=l.byteLength;a.ts.videoPacketContainsKeyFrame(u)&&(i.firstKeyFrame=a.ts.parsePesTime(u),i.firstKeyFrame.type="video"),p.size=0}p.data.push(n),p.size+=n.byteLength}}if(h&&i.firstKeyFrame)break;f+=188,c+=188}for(c=e.byteLength,f=c-188,h=!1;f>=0;)if(71!==e[f]||71!==e[c])f--,c--;else{switch(n=e.subarray(f,c),a.ts.parseType(n,t.pid)){case"pes":r=a.ts.parsePesType(n,t.table),s=a.ts.parsePayloadUnitStartIndicator(n),"video"===r&&s&&(o=a.ts.parsePesTime(n))&&(o.type="video",i.video.push(o),h=!0)}if(h)break;f-=188,c-=188}},l=function(e,t){if(e.audio&&e.audio.length){var i=t;void 0===i&&(i=e.audio[0].dts),e.audio.forEach(function(e){e.dts=r(e.dts,i),e.pts=r(e.pts,i),e.dtsTime=e.dts/9e4,e.ptsTime=e.pts/9e4})}if(e.video&&e.video.length){var n=t;if(void 0===n&&(n=e.video[0].dts),e.video.forEach(function(e){e.dts=r(e.dts,n),e.pts=r(e.pts,n),e.dtsTime=e.dts/9e4,e.ptsTime=e.pts/9e4}),e.firstKeyFrame){var a=e.firstKeyFrame;a.dts=r(a.dts,n),a.pts=r(a.pts,n),a.dtsTime=a.dts/9e4,a.ptsTime=a.dts/9e4}}},f=function(e){for(var t,i=!1,n=0,r=null,s=null,o=0,u=0;e.length-u>=3;){switch(a.aac.parseType(e,u)){case"timed-metadata":if(e.length-u<10){i=!0;break}if((o=a.aac.parseId3TagSize(e,u))>e.length){i=!0;break}null===s&&(t=e.subarray(u,u+o),s=a.aac.parseAacTimestamp(t)),u+=o;break;case"audio":if(e.length-u<7){i=!0;break}if((o=a.aac.parseAdtsSize(e,u))>e.length){i=!0;break}null===r&&(t=e.subarray(u,u+o),r=a.aac.parseSampleRate(t)),n++,u+=o;break;default:u++}if(i)return null}if(null===r||null===s)return null;var d=9e4/r;return{audio:[{type:"audio",dts:s,pts:s},{type:"audio",dts:s+1024*n*d,pts:s+1024*n*d}]}},c=function(e){var t={pid:null,table:null},i={};o(e,t);for(var r in t.table)if(t.table.hasOwnProperty(r)){var a=t.table[r];switch(a){case n.H264_STREAM_TYPE:i.video=[],d(e,t,i),0===i.video.length&&delete i.video;break;case n.ADTS_STREAM_TYPE:i.audio=[],u(e,t,i),0===i.audio.length&&delete i.audio}}return i},h=function(e,t){var i,n=s(e);return(i=n?f(e):c(e))&&(i.audio||i.video)?(l(i,t),i):null};t.exports={inspect:h}},{}],58:[function(e,t,i){var n,r,a,s,o,u;n=function(e){return 9e4*e},r=function(e,t){return e*t},a=function(e){return e/9e4},s=function(e,t){return e/t},o=function(e,t){return n(s(e,t))},u=function(e,t){return r(a(e),t)},t.exports={secondsToVideoTs:n,secondsToAudioTs:r,videoTsToSeconds:a,audioTsToSeconds:s,audioTsToVideoTs:o,videoTsToAudioTs:u}},{}],59:[function(e,t,i){"use strict";var n;n=function(e){var t=e.byteLength,i=0,n=0;this.length=function(){return 8*t},this.bitsAvailable=function(){return 8*t+n},this.loadWord=function(){var r=e.byteLength-t,a=new Uint8Array(4),s=Math.min(4,t);if(0===s)throw new Error("no bytes available");a.set(e.subarray(r,r+s)),i=new DataView(a.buffer).getUint32(0),n=8*s,t-=s},this.skipBits=function(e){var r;n>e?(i<<=e,n-=e):(e-=n,r=Math.floor(e/8),e-=8*r,t-=r,this.loadWord(),i<<=e,n-=e)},this.readBits=function(e){var r=Math.min(n,e),a=i>>>32-r;return n-=r,n>0?i<<=r:t>0&&this.loadWord(),r=e-r,r>0?a<<r|this.readBits(r):a},this.skipLeadingZeros=function(){var e;for(e=0;e<n;++e)if(0!=(i&2147483648>>>e))return i<<=e,n-=e,e;return this.loadWord(),e+this.skipLeadingZeros()},this.skipUnsignedExpGolomb=function(){this.skipBits(1+this.skipLeadingZeros())},this.skipExpGolomb=function(){this.skipBits(1+this.skipLeadingZeros())},this.readUnsignedExpGolomb=function(){var e=this.skipLeadingZeros();return this.readBits(e+1)-1},this.readExpGolomb=function(){var e=this.readUnsignedExpGolomb();return 1&e?1+e>>>1:-1*(e>>>1)},this.readBoolean=function(){return 1===this.readBits(1)},this.readUnsignedByte=function(){return this.readBits(8)},this.loadWord()},t.exports=n},{}],60:[function(e,t,i){"use strict";var n=function(){this.init=function(){var e={};this.on=function(t,i){e[t]||(e[t]=[]),e[t]=e[t].concat(i)},this.off=function(t,i){var n;return!!e[t]&&(n=e[t].indexOf(i),e[t]=e[t].slice(),e[t].splice(n,1),n>-1)},this.trigger=function(t){var i,n,r,a;if(i=e[t])if(2===arguments.length)for(r=i.length,n=0;n<r;++n)i[n].call(this,arguments[1]);else{for(a=[],n=arguments.length,n=1;n<arguments.length;++n)a.push(arguments[n]);for(r=i.length,n=0;n<r;++n)i[n].apply(this,a)}},this.dispose=function(){e={}}}};n.prototype.pipe=function(e){return this.on("data",function(t){e.push(t)}),this.on("done",function(t){e.flush(t)}),e},n.prototype.push=function(e){this.trigger("data",e)},n.prototype.flush=function(e){this.trigger("done",e)},t.exports=n},{}],61:[function(e,t,i){"use strict";var n;t.exports=function(e){var t=n[e.byteLength%16||0],i=new Uint8Array(e.byteLength+t.length);return i.set(e),i.set(t,e.byteLength),i},n=[[16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16],[15,15,15,15,15,15,15,15,15,15,15,15,15,15,15],[14,14,14,14,14,14,14,14,14,14,14,14,14,14],[13,13,13,13,13,13,13,13,13,13,13,13,13],[12,12,12,12,12,12,12,12,12,12,12,12],[11,11,11,11,11,11,11,11,11,11,11],[10,10,10,10,10,10,10,10,10,10],[9,9,9,9,9,9,9,9,9],[8,8,8,8,8,8,8,8],[7,7,7,7,7,7,7],[6,6,6,6,6,6],[5,5,5,5,5],[4,4,4,4],[3,3,3],[2,2],[1]]},{}],62:[function(e,t,i){"use strict";i.pad=e(61),i.unpad=e(63)},{}],63:[function(e,t,i){"use strict";t.exports=function(e){return e.subarray(0,e.byteLength-e[e.byteLength-1])}},{}],64:[function(e,t,i){!function(e){var n=/^((?:[a-zA-Z0-9+\-.]+:)?)(\/\/[^\/\;?#]*)?(.*?)??(;.*?)?(\?.*?)?(#.*?)?$/,r=/^([^\/;?#]*)(.*)$/,a=/(?:\/|^)\.(?=\/)/g,s=/(?:\/|^)\.\.\/(?!\.\.\/).*?(?=\/)/g,o={buildAbsoluteURL:function(e,t,i){if(i=i||{},e=e.trim(),!(t=t.trim())){if(!i.alwaysNormalize)return e;var n=o.parseURL(e);if(!n)throw new Error("Error trying to parse base URL.");return n.path=o.normalizePath(n.path),o.buildURLFromParts(n)}var a=o.parseURL(t);if(!a)throw new Error("Error trying to parse relative URL.");if(a.scheme)return i.alwaysNormalize?(a.path=o.normalizePath(a.path),o.buildURLFromParts(a)):t;var s=o.parseURL(e);if(!s)throw new Error("Error trying to parse base URL.");if(!s.netLoc&&s.path&&"/"!==s.path[0]){var u=r.exec(s.path);s.netLoc=u[1],s.path=u[2]}s.netLoc&&!s.path&&(s.path="/");var d={scheme:s.scheme,netLoc:a.netLoc,path:null,params:a.params,query:a.query,fragment:a.fragment};if(!a.netLoc&&(d.netLoc=s.netLoc,"/"!==a.path[0]))if(a.path){var l=s.path,f=l.substring(0,l.lastIndexOf("/")+1)+a.path;d.path=o.normalizePath(f)}else d.path=s.path,a.params||(d.params=s.params,a.query||(d.query=s.query));return null===d.path&&(d.path=i.alwaysNormalize?o.normalizePath(a.path):a.path),o.buildURLFromParts(d)},parseURL:function(e){var t=n.exec(e);return t?{scheme:t[1]||"",netLoc:t[2]||"",path:t[3]||"",params:t[4]||"",query:t[5]||"",fragment:t[6]||""}:null},normalizePath:function(e){for(e=e.split("").reverse().join("").replace(a,"");e.length!==(e=e.replace(s,"")).length;);return e.split("").reverse().join("")},buildURLFromParts:function(e){return e.scheme+e.netLoc+e.path+e.params+e.query+e.fragment}};"object"==typeof i&&"object"==typeof t?t.exports=o:"function"==typeof define&&define.amd?define([],function(){return o}):"object"==typeof i?i.URLToolkit=o:e.URLToolkit=o}(this)},{}],65:[function(e,t,i){(function(n){"use strict";function r(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(i,"__esModule",{value:!0});var a=e(30),s=r(a),o="undefined"!=typeof window?window.videojs:void 0!==n?n.videojs:null,u=r(o),d=function(e){Object.defineProperties(e.frame,{id:{get:function(){
return u["default"].log.warn("cue.frame.id is deprecated. Use cue.value.key instead."),e.value.key}},value:{get:function(){return u["default"].log.warn("cue.frame.value is deprecated. Use cue.value.data instead."),e.value.data}},privateData:{get:function(){return u["default"].log.warn("cue.frame.privateData is deprecated. Use cue.value.data instead."),e.value.data}}})},l=function(e){undefined;return isNaN(e)||Math.abs(e)===Infinity?Number.MAX_VALUE:e},f=function(e,t,i){var n=s["default"].WebKitDataCue||s["default"].VTTCue;t&&t.forEach(function(e){var t=e.stream;this.inbandTextTracks_[t].addCue(new n(e.startTime+this.timestampOffset,e.endTime+this.timestampOffset,e.text))},e),i&&function(){var t=l(e.mediaSource_.duration);i.forEach(function(e){var t=e.cueTime+this.timestampOffset;e.frames.forEach(function(e){var i=new n(t,t,e.value||e.url||e.data||"");i.frame=e,i.value=e,d(i),this.metadataTrack_.addCue(i)},this)},e),e.metadataTrack_&&e.metadataTrack_.cues&&e.metadataTrack_.cues.length&&function(){for(var i=e.metadataTrack_.cues,n=[],r=0;r<i.length;r++)i[r]&&n.push(i[r]);var a=n.reduce(function(e,t){var i=e[t.startTime]||[];return i.push(t),e[t.startTime]=i,e},{}),s=Object.keys(a).sort(function(e,t){return Number(e)-Number(t)});s.forEach(function(e,i){var n=a[e],r=Number(s[i+1])||t;n.forEach(function(e){e.endTime=r})})}()}()};i["default"]={addTextTrackData:f,durationOfVideo:l},t.exports=i["default"]}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],66:[function(e,t,i){"use strict";Object.defineProperty(i,"__esModule",{value:!0});var n=function(e){return/mp4a\.\d+.\d+/i.test(e)},r=function(e){return/avc1\.[\da-f]+/i.test(e)},a=function(e){var t={type:"",parameters:{}},i=e.trim().split(";");return t.type=i.shift().trim(),i.forEach(function(e){var i=e.trim().split("=");if(i.length>1){var n=i[0].replace(/"/g,"").trim(),r=i[1].replace(/"/g,"").trim();t.parameters[n]=r}}),t},s=function(e){return e.map(function(e){return e.replace(/avc1\.(\d+)\.(\d+)/i,function(e,t,i){return"avc1."+("00"+Number(t).toString(16)).slice(-2)+"00"+("00"+Number(i).toString(16)).slice(-2)})})};i["default"]={isAudioCodec:n,parseContentType:a,isVideoCodec:r,translateLegacyCodecs:s},t.exports=i["default"]},{}],67:[function(e,t,i){"use strict";Object.defineProperty(i,"__esModule",{value:!0});var n=function(e,t,i){var n=t.player_;if(i.captions&&i.captions.length){e.inbandTextTracks_||(e.inbandTextTracks_={});for(var r in i.captionStreams)if(!e.inbandTextTracks_[r]){n.tech_.trigger({type:"usage",name:"hls-608"});var a=n.textTracks().getTrackById(r);e.inbandTextTracks_[r]=a||n.addRemoteTextTrack({kind:"captions",id:r,label:r},!1).track}}i.metadata&&i.metadata.length&&!e.metadataTrack_&&(e.metadataTrack_=n.addRemoteTextTrack({kind:"metadata",label:"Timed Metadata"},!1).track,e.metadataTrack_.inBandMetadataTrackDispatchType=i.metadata.dispatchType)};i["default"]=n,t.exports=i["default"]},{}],68:[function(e,t,i){"use strict";Object.defineProperty(i,"__esModule",{value:!0});var n={TIME_BETWEEN_CHUNKS:1,BYTES_PER_CHUNK:32768};i["default"]=n,t.exports=i["default"]},{}],69:[function(e,t,i){(function(n){"use strict";function r(e){return e&&e.__esModule?e:{"default":e}}function a(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function s(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}Object.defineProperty(i,"__esModule",{value:!0});var o=function(){function e(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,i,n){return i&&e(t.prototype,i),n&&e(t,n),t}}(),u=function(e,t,i){for(var n=!0;n;){var r=e,a=t,s=i;n=!1,null===r&&(r=Function.prototype);var o=Object.getOwnPropertyDescriptor(r,a);if(o!==undefined){if("value"in o)return o.value;var u=o.get;return u===undefined?undefined:u.call(s)}var d=Object.getPrototypeOf(r);if(null===d)return undefined;e=d,t=a,i=s,n=!0,o=d=undefined}},d=e(29),l=r(d),f="undefined"!=typeof window?window.videojs:void 0!==n?n.videojs:null,c=r(f),h=e(70),p=r(h),m=e(68),g=r(m),y=e(66),_=function(e){function t(){var e=this;a(this,t),u(Object.getPrototypeOf(t.prototype),"constructor",this).call(this),this.sourceBuffers=[],this.readyState="closed",this.on(["sourceopen","webkitsourceopen"],function(t){e.swfObj=l["default"].getElementById(t.swfId),e.player_=(0,c["default"])(e.swfObj.parentNode),e.tech_=e.swfObj.tech,e.readyState="open",e.tech_.on("seeking",function(){for(var t=e.sourceBuffers.length;t--;)e.sourceBuffers[t].abort()}),e.swfObj&&e.swfObj.vjs_load()})}return s(t,e),o(t,[{key:"addSeekableRange_",value:function(){}},{key:"addSourceBuffer",value:function(e){var t=(0,y.parseContentType)(e),i=undefined;if("video/mp2t"!==t.type&&"audio/mp2t"!==t.type)throw new Error("NotSupportedError (Video.js)");return i=new p["default"](this),this.sourceBuffers.push(i),i}},{key:"endOfStream",value:function(e){"network"===e?this.tech_.error(2):"decode"===e&&this.tech_.error(3),"ended"!==this.readyState&&(this.readyState="ended",this.swfObj.vjs_endOfStream())}}]),t}(c["default"].EventTarget);i["default"]=_;try{Object.defineProperty(_.prototype,"duration",{get:function(){return this.swfObj?this.swfObj.vjs_getProperty("duration"):NaN},set:function(e){var t=undefined,i=this.swfObj.vjs_getProperty("duration");if(this.swfObj.vjs_setProperty("duration",e),e<i)for(t=0;t<this.sourceBuffers.length;t++)this.sourceBuffers[t].remove(e,i);return e}})}catch(b){_.prototype.duration=NaN}for(var v in g["default"])_[v]=g["default"][v];t.exports=i["default"]}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],70:[function(e,t,i){(function(n){"use strict";function r(e){return e&&e.__esModule?e:{"default":e}}function a(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function s(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}Object.defineProperty(i,"__esModule",{value:!0});var o=function(){function e(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,i,n){return i&&e(t.prototype,i),n&&e(t,n),t}}(),u=function(e,t,i){for(var n=!0;n;){var r=e,a=t,s=i;n=!1,null===r&&(r=Function.prototype);var o=Object.getOwnPropertyDescriptor(r,a);if(o!==undefined){if("value"in o)return o.value;var u=o.get;return u===undefined?undefined:u.call(s)}var d=Object.getPrototypeOf(r);if(null===d)return undefined;e=d,t=a,i=s,n=!0,o=d=undefined}},d=e(30),l=r(d),f="undefined"!=typeof window?window.videojs:void 0!==n?n.videojs:null,c=r(f),h=e(44),p=r(h),m=e(73),g=r(m),y=e(67),_=r(y),v=e(65),b=e(71),T=r(b),S=e(77),w=r(S),k=e(68),E=r(k),O=function(){var t=undefined;try{t=e.resolve("./flash-transmuxer-worker")}catch(i){}return t},A=function(e){l["default"].setTimeout(e,E["default"].TIME_BETWEEN_CHUNKS)},L=function(){return Math.random().toString(36).slice(2,8)},P=function(e,t){("number"!=typeof t||t<0)&&(t=0);var i=Math.pow(10,t);return Math.round(e*i)/i},I=function(e){function t(e){var i=this;a(this,t),u(Object.getPrototypeOf(t.prototype),"constructor",this).call(this);var n=undefined;this.chunkSize_=E["default"].BYTES_PER_CHUNK,this.buffer_=[],this.bufferSize_=0,this.basePtsOffset_=NaN,this.mediaSource_=e,this.audioBufferEnd_=NaN,this.videoBufferEnd_=NaN,this.updating=!1,this.timestampOffset_=0,n=l["default"].btoa(String.fromCharCode.apply(null,Array.prototype.slice.call(p["default"].getFlvHeader())));var r=this.mediaSource_.player_.id().replace(/[^a-zA-Z0-9]/g,"_");this.flashEncodedHeaderName_="vjs_flashEncodedHeader_"+r+L(),this.flashEncodedDataName_="vjs_flashEncodedData_"+r+L(),l["default"][this.flashEncodedHeaderName_]=function(){return delete l["default"][i.flashEncodedHeaderName_],n},this.mediaSource_.swfObj.vjs_appendChunkReady(this.flashEncodedHeaderName_),this.transmuxer_=(0,w["default"])(T["default"],O()),this.transmuxer_.postMessage({action:"init",options:{}}),this.transmuxer_.onmessage=function(e){"data"===e.data.action&&i.receiveBuffer_(e.data.segment)},this.one("updateend",function(){i.mediaSource_.tech_.trigger("loadedmetadata")}),Object.defineProperty(this,"timestampOffset",{get:function(){return this.timestampOffset_},set:function(e){"number"==typeof e&&e>=0&&(this.timestampOffset_=e,this.mediaSource_.swfObj.vjs_discontinuity(),this.basePtsOffset_=NaN,this.audioBufferEnd_=NaN,this.videoBufferEnd_=NaN,this.transmuxer_.postMessage({action:"reset"}))}}),Object.defineProperty(this,"buffered",{get:function(){if(!(this.mediaSource_&&this.mediaSource_.swfObj&&"vjs_getProperty"in this.mediaSource_.swfObj))return c["default"].createTimeRange();var e=this.mediaSource_.swfObj.vjs_getProperty("buffered");return e&&e.length&&(e[0][0]=P(e[0][0],3),e[0][1]=P(e[0][1],3)),c["default"].createTimeRanges(e)}}),this.mediaSource_.player_.on("seeked",function(){if((0,g["default"])(0,Infinity,i.metadataTrack_),i.inbandTextTracks_)for(var e in i.inbandTextTracks_)(0,g["default"])(0,Infinity,i.inbandTextTracks_[e])});var s=this.onHlsReset_.bind(this);this.mediaSource_.player_.tech_.on("hls-reset",s),this.mediaSource_.player_.tech_.hls.on("dispose",function(){i.transmuxer_.terminate(),i.mediaSource_.player_.tech_.off("hls-reset",s)})}return s(t,e),o(t,[{key:"appendBuffer",value:function(e){var t=undefined;if(this.updating)throw t=new Error("SourceBuffer.append() cannot be called while an update is in progress"),t.name="InvalidStateError",t.code=11,t;this.updating=!0,this.mediaSource_.readyState="open",this.trigger({type:"update"}),this.transmuxer_.postMessage({action:"push",data:e.buffer,byteOffset:e.byteOffset,byteLength:e.byteLength},[e.buffer]),this.transmuxer_.postMessage({action:"flush"})}},{key:"abort",value:function(){this.buffer_=[],this.bufferSize_=0,this.mediaSource_.swfObj.vjs_abort(),this.updating&&(this.updating=!1,this.trigger({type:"updateend"}))}},{key:"remove",value:function(e,t){if((0,g["default"])(e,t,this.metadataTrack_),this.inbandTextTracks_)for(var i in this.inbandTextTracks_)(0,g["default"])(e,t,this.inbandTextTracks_[i]);this.trigger({type:"update"}),this.trigger({type:"updateend"})}},{key:"receiveBuffer_",value:function(e){var t=this;(0,_["default"])(this,this.mediaSource_,e),(0,v.addTextTrackData)(this,e.captions,e.metadata),A(function(){var i=t.convertTagsToData_(e);0===t.buffer_.length&&A(t.processBuffer_.bind(t)),i&&(t.buffer_.push(i),t.bufferSize_+=i.byteLength)})}},{key:"processBuffer_",value:function(){var e=this,t=E["default"].BYTES_PER_CHUNK;if(!this.buffer_.length)return void(!1!==this.updating&&(this.updating=!1,this.trigger({type:"updateend"})));var i=this.buffer_[0].subarray(0,t);i.byteLength<t||this.buffer_[0].byteLength===t?this.buffer_.shift():this.buffer_[0]=this.buffer_[0].subarray(t),this.bufferSize_-=i.byteLength;for(var n=[],r=i.byteLength,a=0;a<r;a++)n.push(String.fromCharCode(i[a]));var s=l["default"].btoa(n.join(""));l["default"][this.flashEncodedDataName_]=function(){return A(e.processBuffer_.bind(e)),delete l["default"][e.flashEncodedDataName_],s},this.mediaSource_.swfObj.vjs_appendChunkReady(this.flashEncodedDataName_)}},{key:"convertTagsToData_",value:function(e){var t=0,i=this.mediaSource_.tech_,n=0,r=undefined,a=e.tags.videoTags,s=e.tags.audioTags;if(isNaN(this.basePtsOffset_)&&(a.length||s.length)){var o=a[0]||{pts:Infinity},u=s[0]||{pts:Infinity};this.basePtsOffset_=Math.min(u.pts,o.pts)}i.seeking()&&(this.videoBufferEnd_=NaN,this.audioBufferEnd_=NaN),isNaN(this.videoBufferEnd_)?(i.buffered().length&&(n=i.buffered().end(0)-this.timestampOffset),i.seeking()&&(n=Math.max(n,i.currentTime()-this.timestampOffset)),n*=1e3,n+=this.basePtsOffset_):n=this.videoBufferEnd_+.1;var d=a.length;if(d&&a[d-1].pts>=n){for(;--d;){var l=a[d];if(!(l.pts>n)&&(l.keyFrame||l.metaDataTag))break}for(;d;){if(!a[d-1].metaDataTag)break;d--}}var f=a.slice(d),c=undefined;for(c=isNaN(this.audioBufferEnd_)?n:this.audioBufferEnd_+.1,f.length&&(c=Math.min(c,f[0].pts)),d=0;d<s.length&&!(s[d].pts>=c);)d++;var h=s.slice(d);h.length&&(this.audioBufferEnd_=h[h.length-1].pts),f.length&&(this.videoBufferEnd_=f[f.length-1].pts);var p=this.getOrderedTags_(f,h);if(0!==p.length){if(p[0].pts<n&&i.seeking()){var m=i.currentTime(),g=(n-p[0].pts)/1e3,y=m-g;y<1/30&&(y=0);try{this.mediaSource_.swfObj.vjs_adjustCurrentTime(y)}catch(b){}}for(var _=0;_<p.length;_++)t+=p[_].bytes.byteLength;r=new Uint8Array(t);for(var _=0,v=0;_<p.length;_++)r.set(p[_].bytes,v),v+=p[_].bytes.byteLength;return r}}},{key:"getOrderedTags_",value:function(e,t){for(var i=undefined,n=[];e.length||t.length;)i=e.length?t.length&&t[0].dts<e[0].dts?t.shift():e.shift():t.shift(),n.push(i);return n}},{key:"onHlsReset_",value:function(){this.transmuxer_.postMessage({action:"resetCaptions"})}}]),t}(c["default"].EventTarget);i["default"]=I,t.exports=i["default"]}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],71:[function(e,t,i){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(i,"__esModule",{value:!0});var a=function(){function e(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,i,n){return i&&e(t.prototype,i),n&&e(t,n),t}}(),s=e(30),o=n(s),u=e(44),d=n(u),l=function(e){e.on("data",function(e){o["default"].postMessage({action:"data",segment:e})}),e.on("done",function(e){o["default"].postMessage({action:"done"})})},f=function(){function e(t){r(this,e),this.options=t||{},this.init()}return a(e,[{key:"init",value:function(){this.transmuxer&&this.transmuxer.dispose(),this.transmuxer=new d["default"].Transmuxer(this.options),l(this.transmuxer)}},{key:"push",value:function(e){var t=new Uint8Array(e.data,e.byteOffset,e.byteLength);this.transmuxer.push(t)}},{key:"reset",value:function(){this.init()}},{key:"flush",value:function(){this.transmuxer.flush()}},{key:"resetCaptions",value:function(){this.transmuxer.resetCaptions()}}]),e}(),c=function(e){e.onmessage=function(e){if("init"===e.data.action&&e.data.options)return void(this.messageHandlers=new f(e.data.options));this.messageHandlers||(this.messageHandlers=new f),e.data&&e.data.action&&"init"!==e.data.action&&this.messageHandlers[e.data.action]&&this.messageHandlers[e.data.action](e.data)}};i["default"]=function(e){return new c(e)},t.exports=i["default"]},{}],72:[function(e,t,i){(function(n){"use strict";function r(e){return e&&e.__esModule?e:{"default":e}}function a(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function s(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}Object.defineProperty(i,"__esModule",{value:!0});var o=function(){function e(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,i,n){return i&&e(t.prototype,i),n&&e(t,n),t}}(),u=function(e,t,i){for(var n=!0;n;){var r=e,a=t,s=i;n=!1,null===r&&(r=Function.prototype);var o=Object.getOwnPropertyDescriptor(r,a);if(o!==undefined){if("value"in o)return o.value;var u=o.get;return u===undefined?undefined:u.call(s)}var d=Object.getPrototypeOf(r);if(null===d)return undefined;e=d,t=a,i=s,n=!0,o=d=undefined}},d=e(30),l=r(d),f=e(29),c=r(f),h="undefined"!=typeof window?window.videojs:void 0!==n?n.videojs:null,p=r(h),m=e(76),g=r(m),y=e(65),_=e(66),v=function(e){function t(){var e=this;a(this,t),u(Object.getPrototypeOf(t.prototype),"constructor",this).call(this);var i=undefined;this.nativeMediaSource_=new l["default"].MediaSource;for(i in this.nativeMediaSource_)i in t.prototype||"function"!=typeof this.nativeMediaSource_[i]||(this[i]=this.nativeMediaSource_[i].bind(this.nativeMediaSource_));this.duration_=NaN,Object.defineProperty(this,"duration",{get:function(){return this.duration_===Infinity?this.duration_:this.nativeMediaSource_.duration},set:function(e){if(this.duration_=e,e!==Infinity)return void(this.nativeMediaSource_.duration=e)}}),Object.defineProperty(this,"seekable",{get:function(){return this.duration_===Infinity?p["default"].createTimeRanges([[0,this.nativeMediaSource_.duration]]):this.nativeMediaSource_.seekable}}),Object.defineProperty(this,"readyState",{get:function(){return this.nativeMediaSource_.readyState}}),Object.defineProperty(this,"activeSourceBuffers",{get:function(){return this.activeSourceBuffers_}}),this.sourceBuffers=[],this.activeSourceBuffers_=[],this.updateActiveSourceBuffers_=function(){if(e.activeSourceBuffers_.length=0,1===e.sourceBuffers.length){var t=e.sourceBuffers[0];return t.appendAudioInitSegment_=!0,t.audioDisabled_=!t.audioCodec_,void e.activeSourceBuffers_.push(t)}for(var i=!1,n=!0,r=0;r<e.player_.audioTracks().length;r++){var a=e.player_.audioTracks()[r];if(a.enabled&&"main"!==a.kind){i=!0,n=!1;break}}e.sourceBuffers.forEach(function(t){if(t.appendAudioInitSegment_=!0,t.videoCodec_&&t.audioCodec_)t.audioDisabled_=i;else if(t.videoCodec_&&!t.audioCodec_)t.audioDisabled_=!0,n=!1;else if(!t.videoCodec_&&t.audioCodec_&&(t.audioDisabled_=n,n))return;e.activeSourceBuffers_.push(t)})},this.onPlayerMediachange_=function(){e.sourceBuffers.forEach(function(e){e.appendAudioInitSegment_=!0})},this.onHlsReset_=function(){e.sourceBuffers.forEach(function(e){e.transmuxer_&&e.transmuxer_.postMessage({action:"resetCaptions"})})},this.onHlsSegmentTimeMapping_=function(t){e.sourceBuffers.forEach(function(e){return e.timeMapping_=t.mapping})},["sourceopen","sourceclose","sourceended"].forEach(function(e){this.nativeMediaSource_.addEventListener(e,this.trigger.bind(this))},this),this.on("sourceopen",function(t){var i=c["default"].querySelector('[src="'+e.url_+'"]');i&&(e.player_=(0,p["default"])(i.parentNode),e.player_.tech_.on("hls-reset",e.onHlsReset_),e.player_.tech_.on("hls-segment-time-mapping",e.onHlsSegmentTimeMapping_),e.player_.audioTracks&&e.player_.audioTracks()&&(e.player_.audioTracks().on("change",e.updateActiveSourceBuffers_),e.player_.audioTracks().on("addtrack",e.updateActiveSourceBuffers_),e.player_.audioTracks().on("removetrack",e.updateActiveSourceBuffers_)),e.player_.on("mediachange",e.onPlayerMediachange_))}),this.on("sourceended",function(t){for(var i=(0,y.durationOfVideo)(e.duration),n=0;n<e.sourceBuffers.length;n++){var r=e.sourceBuffers[n],a=r.metadataTrack_&&r.metadataTrack_.cues;a&&a.length&&(a[a.length-1].endTime=i)}}),this.on("sourceclose",function(e){this.sourceBuffers.forEach(function(e){e.transmuxer_&&e.transmuxer_.terminate()}),this.sourceBuffers.length=0,this.player_&&(this.player_.audioTracks&&this.player_.audioTracks()&&(this.player_.audioTracks().off("change",this.updateActiveSourceBuffers_),this.player_.audioTracks().off("addtrack",this.updateActiveSourceBuffers_),this.player_.audioTracks().off("removetrack",this.updateActiveSourceBuffers_)),this.player_.el_&&(this.player_.off("mediachange",this.onPlayerMediachange_),this.player_.tech_.off("hls-reset",this.onHlsReset_),this.player_.tech_.off("hls-segment-time-mapping",this.onHlsSegmentTimeMapping_)))})}return s(t,e),o(t,[{key:"addSeekableRange_",value:function(e,t){var i=undefined;if(this.duration!==Infinity)throw i=new Error("MediaSource.addSeekableRange() can only be invoked when the duration is Infinity"),i.name="InvalidStateError",i.code=11,i;(t>this.nativeMediaSource_.duration||isNaN(this.nativeMediaSource_.duration))&&(this.nativeMediaSource_.duration=t)}},{key:"addSourceBuffer",value:function(e){var t=undefined,i=(0,_.parseContentType)(e);if(/^(video|audio)\/mp2t$/i.test(i.type)){var n=[];i.parameters&&i.parameters.codecs&&(n=i.parameters.codecs.split(","),n=(0,_.translateLegacyCodecs)(n),n=n.filter(function(e){return(0,_.isAudioCodec)(e)||(0,_.isVideoCodec)(e)})),0===n.length&&(n=["avc1.4d400d","mp4a.40.2"]),t=new g["default"](this,n),0!==this.sourceBuffers.length&&(this.sourceBuffers[0].createRealSourceBuffers_(),t.createRealSourceBuffers_(),this.sourceBuffers[0].audioDisabled_=!0)}else t=this.nativeMediaSource_.addSourceBuffer(e);return this.sourceBuffers.push(t),t}}]),t}(p["default"].EventTarget);i["default"]=v,t.exports=i["default"]}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],73:[function(e,t,i){"use strict";Object.defineProperty(i,"__esModule",{value:!0});var n=function(e,t,i){var n=undefined,r=undefined;if(i&&i.cues)for(n=i.cues.length;n--;)r=i.cues[n],r.startTime<=t&&r.endTime>=e&&i.removeCue(r)};i["default"]=n,t.exports=i["default"]},{}],74:[function(e,t,i){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(i,"__esModule",{value:!0});var a=function(){function e(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,i,n){return i&&e(t.prototype,i),n&&e(t,n),t}}(),s=e(30),o=n(s),u=e(53),d=n(u),l=function(e){e.on("data",function(e){var t=e.initSegment;e.initSegment={data:t.buffer,byteOffset:t.byteOffset,byteLength:t.byteLength};var i=e.data;e.data=i.buffer,o["default"].postMessage({action:"data",segment:e,byteOffset:i.byteOffset,byteLength:i.byteLength},[e.data])}),e.captionStream&&e.captionStream.on("data",function(e){o["default"].postMessage({action:"caption",data:e})}),e.on("done",function(e){o["default"].postMessage({action:"done"})}),e.on("gopInfo",function(e){o["default"].postMessage({action:"gopInfo",gopInfo:e})})},f=function(){function e(t){r(this,e),this.options=t||{},this.init()}return a(e,[{key:"init",value:function(){this.transmuxer&&this.transmuxer.dispose(),this.transmuxer=new d["default"].Transmuxer(this.options),l(this.transmuxer)}},{key:"push",value:function(e){var t=new Uint8Array(e.data,e.byteOffset,e.byteLength);this.transmuxer.push(t)}},{key:"reset",value:function(){this.init()}},{key:"setTimestampOffset",value:function(e){var t=e.timestampOffset||0;this.transmuxer.setBaseMediaDecodeTime(Math.round(9e4*t))}},{key:"setAudioAppendStart",value:function(e){this.transmuxer.setAudioAppendStart(Math.ceil(9e4*e.appendStart))}},{key:"flush",value:function(e){this.transmuxer.flush()}},{key:"resetCaptions",value:function(){this.transmuxer.resetCaptions()}},{key:"alignGopsWith",value:function(e){this.transmuxer.alignGopsWith(e.gopsToAlignWith.slice())}}]),e}(),c=function(e){e.onmessage=function(e){if("init"===e.data.action&&e.data.options)return void(this.messageHandlers=new f(e.data.options));this.messageHandlers||(this.messageHandlers=new f),e.data&&e.data.action&&"init"!==e.data.action&&this.messageHandlers[e.data.action]&&this.messageHandlers[e.data.action](e.data)}};i["default"]=function(e){return new c(e)},t.exports=i["default"]},{}],75:[function(e,t,i){(function(t){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(i,"__esModule",{value:!0});var r=e(30),a=n(r),s=e(69),o=n(s),u=e(72),d=n(u),l="undefined"!=typeof window?window.videojs:void 0!==t?t.videojs:null,f=n(l),c=0,h={mode:"auto"};f["default"].mediaSources={};var p=function(e,t){var i=f["default"].mediaSources[e];if(!i)throw new Error("Media Source not found (Video.js)");i.trigger({type:"sourceopen",swfId:t})},m=function(){return!!a["default"].MediaSource&&!!a["default"].MediaSource.isTypeSupported&&a["default"].MediaSource.isTypeSupported('video/mp4;codecs="avc1.4d400d,mp4a.40.2"')},g=function(e){var t=f["default"].mergeOptions(h,e);if(this.MediaSource={open:p,supportsNativeMediaSources:m},"html5"===t.mode||"auto"===t.mode&&m())return new d["default"];if(f["default"].getTech("Flash"))return new o["default"];throw new Error("Cannot use Flash or Html5 to create a MediaSource for this video")};i.MediaSource=g,g.open=p,g.supportsNativeMediaSources=m;var y={createObjectURL:function(e){var t=undefined;return e instanceof d["default"]?(t=a["default"].URL.createObjectURL(e.nativeMediaSource_),e.url_=t,t):e instanceof o["default"]?(t="blob:vjs-media-source/"+c,c++,f["default"].mediaSources[t]=e,t):(t=a["default"].URL.createObjectURL(e),e.url_=t,t)}};i.URL=y,f["default"].MediaSource=g,f["default"].URL=y}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],76:[function(e,t,i){(function(t){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function a(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}Object.defineProperty(i,"__esModule",{value:!0});var s=function(){function e(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,i,n){return i&&e(t.prototype,i),n&&e(t,n),t}}(),o=function(e,t,i){for(var n=!0;n;){var r=e,a=t,s=i;n=!1,null===r&&(r=Function.prototype);var o=Object.getOwnPropertyDescriptor(r,a);if(o!==undefined){if("value"in o)return o.value;var u=o.get;return u===undefined?undefined:u.call(s)}var d=Object.getPrototypeOf(r);if(null===d)return undefined;e=d,t=a,i=s,n=!0,o=d=undefined}},u="undefined"!=typeof window?window.videojs:void 0!==t?t.videojs:null,d=n(u),l=e(67),f=n(l),c=e(73),h=n(c),p=e(65),m=e(77),g=n(m),y=e(74),_=n(y),v=e(66),b=function(){var t=undefined;try{t=e.resolve("./transmuxer-worker")}catch(i){}return t},T=function(e,t){var i=e.addSourceBuffer(t),n=Object.create(null);n.updating=!1,n.realBuffer_=i;for(var r in i)!function(e){"function"==typeof i[e]?n[e]=function(){return i[e].apply(i,arguments)}:"undefined"==typeof n[e]&&Object.defineProperty(n,e,{get:function(){return i[e]},set:function(t){return i[e]=t}})}(r);return n},S=function(e,t,i){if(!t||!e.length)return[];var n=Math.ceil(9e4*(t.currentTime()-i+3)),r=undefined;for(r=0;r<e.length&&!(e[r].pts>n);r++);return e.slice(r)};i.gopsSafeToAlignWith=S;var w=function(e,t,i){if(!t.length)return e;if(i)return t.slice();var n=t[0].pts,r=0;for(r;r<e.length&&!(e[r].pts>=n);r++);return e.slice(0,r).concat(t)};i.updateGopBuffer=w;var k=function(e,t,i,n){for(var r=Math.ceil(9e4*(t-n)),a=Math.ceil(9e4*(i-n)),s=e.slice(),o=e.length;o--&&!(e[o].pts<=a););if(-1===o)return s;for(var u=o+1;u--&&!(e[u].pts<=r););return u=Math.max(u,0),s.splice(u,o-u+1),s};i.removeGopBuffer=k;var E=function(e){function t(e,i){var n=this;r(this,t),o(Object.getPrototypeOf(t.prototype),"constructor",this).call(this,d["default"].EventTarget),this.timestampOffset_=0,this.pendingBuffers_=[],this.bufferUpdating_=!1,this.mediaSource_=e,this.codecs_=i,this.audioCodec_=null,this.videoCodec_=null,this.audioDisabled_=!1,this.appendAudioInitSegment_=!0,this.gopBuffer_=[],this.timeMapping_=0,this.safeAppend_=d["default"].browser.IE_VERSION>=11;var a={remux:!1,alignGopsAtEnd:this.safeAppend_};this.codecs_.forEach(function(e){(0,v.isAudioCodec)(e)?n.audioCodec_=e:(0,v.isVideoCodec)(e)&&(n.videoCodec_=e)}),this.transmuxer_=(0,g["default"])(_["default"],b()),this.transmuxer_.postMessage({action:"init",options:a}),this.transmuxer_.onmessage=function(e){return"data"===e.data.action?n.data_(e):"done"===e.data.action?n.done_(e):"gopInfo"===e.data.action?n.appendGopInfo_(e):void 0},Object.defineProperty(this,"timestampOffset",{get:function(){return this.timestampOffset_},set:function(e){"number"==typeof e&&e>=0&&(this.timestampOffset_=e,this.appendAudioInitSegment_=!0,this.gopBuffer_.length=0,this.timeMapping_=0,this.transmuxer_.postMessage({action:"setTimestampOffset",timestampOffset:e}))}}),Object.defineProperty(this,"appendWindowStart",{get:function(){return(this.videoBuffer_||this.audioBuffer_).appendWindowStart},set:function(e){this.videoBuffer_&&(this.videoBuffer_.appendWindowStart=e),this.audioBuffer_&&(this.audioBuffer_.appendWindowStart=e)}}),Object.defineProperty(this,"updating",{get:function(){return!!(this.bufferUpdating_||!this.audioDisabled_&&this.audioBuffer_&&this.audioBuffer_.updating||this.videoBuffer_&&this.videoBuffer_.updating)}}),Object.defineProperty(this,"buffered",{get:function(){var e=null,t=null,i=0,n=[],r=[];if(!this.videoBuffer_&&!this.audioBuffer_)return d["default"].createTimeRange();if(!this.videoBuffer_)return this.audioBuffer_.buffered;if(!this.audioBuffer_)return this.videoBuffer_.buffered;if(this.audioDisabled_)return this.videoBuffer_.buffered;if(0===this.videoBuffer_.buffered.length&&0===this.audioBuffer_.buffered.length)return d["default"].createTimeRange();for(var a=this.videoBuffer_.buffered,s=this.audioBuffer_.buffered,o=a.length;o--;)n.push({time:a.start(o),type:"start"}),n.push({time:a.end(o),type:"end"});for(o=s.length;o--;)n.push({time:s.start(o),type:"start"}),n.push({time:s.end(o),type:"end"});for(n.sort(function(e,t){return e.time-t.time}),o=0;o<n.length;o++)"start"===n[o].type?2===++i&&(e=n[o].time):"end"===n[o].type&&1===--i&&(t=n[o].time),null!==e&&null!==t&&(r.push([e,t]),e=null,t=null);return d["default"].createTimeRanges(r)}})}return a(t,e),s(t,[{key:"data_",value:function(e){var t=e.data.segment;t.data=new Uint8Array(t.data,e.data.byteOffset,e.data.byteLength),t.initSegment=new Uint8Array(t.initSegment.data,t.initSegment.byteOffset,t.initSegment.byteLength),(0,f["default"])(this,this.mediaSource_,t),this.pendingBuffers_.push(t)}},{key:"done_",value:function(e){if("closed"===this.mediaSource_.readyState)return void(this.pendingBuffers_.length=0);this.processPendingSegments_()}},{key:"createRealSourceBuffers_",value:function(){var e=this,t=["audio","video"];t.forEach(function(i){if(e[i+"Codec_"]&&!e[i+"Buffer_"]){var n=null;if(e.mediaSource_[i+"Buffer_"])n=e.mediaSource_[i+"Buffer_"],n.updating=!1;else{var r=i+"Codec_",a=i+'/mp4;codecs="'+e[r]+'"';n=T(e.mediaSource_.nativeMediaSource_,a),e.mediaSource_[i+"Buffer_"]=n}e[i+"Buffer_"]=n,["update","updatestart","updateend"].forEach(function(r){n.addEventListener(r,function(){if("audio"!==i||!e.audioDisabled_){"updateend"===r&&(e[i+"Buffer_"].updating=!1);return t.every(function(t){return!("audio"!==t||!e.audioDisabled_)||(i===t||!e[t+"Buffer_"]||!e[t+"Buffer_"].updating)})?e.trigger(r):void 0}})})}})}},{key:"appendBuffer",value:function(e){if(this.bufferUpdating_=!0,this.audioBuffer_&&this.audioBuffer_.buffered.length){var t=this.audioBuffer_.buffered;this.transmuxer_.postMessage({action:"setAudioAppendStart",appendStart:t.end(t.length-1)})}this.videoBuffer_&&this.transmuxer_.postMessage({action:"alignGopsWith",gopsToAlignWith:S(this.gopBuffer_,this.mediaSource_.player_,this.timeMapping_)}),this.transmuxer_.postMessage({action:"push",data:e.buffer,byteOffset:e.byteOffset,byteLength:e.byteLength},[e.buffer]),this.transmuxer_.postMessage({action:"flush"})}},{key:"appendGopInfo_",value:function(e){
this.gopBuffer_=w(this.gopBuffer_,e.data.gopInfo,this.safeAppend_)}},{key:"remove",value:function(e,t){if(this.videoBuffer_&&(this.videoBuffer_.updating=!0,this.videoBuffer_.remove(e,t),this.gopBuffer_=k(this.gopBuffer_,e,t,this.timeMapping_)),!this.audioDisabled_&&this.audioBuffer_&&(this.audioBuffer_.updating=!0,this.audioBuffer_.remove(e,t)),(0,h["default"])(e,t,this.metadataTrack_),this.inbandTextTracks_)for(var i in this.inbandTextTracks_)(0,h["default"])(e,t,this.inbandTextTracks_[i])}},{key:"processPendingSegments_",value:function(){var e={video:{segments:[],bytes:0},audio:{segments:[],bytes:0},captions:[],metadata:[]};e=this.pendingBuffers_.reduce(function(e,t){var i=t.type,n=t.data,r=t.initSegment;return e[i].segments.push(n),e[i].bytes+=n.byteLength,e[i].initSegment=r,t.captions&&(e.captions=e.captions.concat(t.captions)),t.info&&(e[i].info=t.info),t.metadata&&(e.metadata=e.metadata.concat(t.metadata)),e},e),this.videoBuffer_||this.audioBuffer_||(0===e.video.bytes&&(this.videoCodec_=null),0===e.audio.bytes&&(this.audioCodec_=null),this.createRealSourceBuffers_()),e.audio.info&&this.mediaSource_.trigger({type:"audioinfo",info:e.audio.info}),e.video.info&&this.mediaSource_.trigger({type:"videoinfo",info:e.video.info}),this.appendAudioInitSegment_&&(!this.audioDisabled_&&this.audioBuffer_&&(e.audio.segments.unshift(e.audio.initSegment),e.audio.bytes+=e.audio.initSegment.byteLength),this.appendAudioInitSegment_=!1);var t=!1;this.videoBuffer_&&e.video.bytes?(e.video.segments.unshift(e.video.initSegment),e.video.bytes+=e.video.initSegment.byteLength,this.concatAndAppendSegments_(e.video,this.videoBuffer_),(0,p.addTextTrackData)(this,e.captions,e.metadata)):!this.videoBuffer_||!this.audioDisabled_&&this.audioBuffer_||(t=!0),!this.audioDisabled_&&this.audioBuffer_&&this.concatAndAppendSegments_(e.audio,this.audioBuffer_),this.pendingBuffers_.length=0,t&&this.trigger("updateend"),this.bufferUpdating_=!1}},{key:"concatAndAppendSegments_",value:function(e,t){var i=0,n=undefined;if(e.bytes){n=new Uint8Array(e.bytes),e.segments.forEach(function(e){n.set(e,i),i+=e.byteLength});try{t.updating=!0,t.appendBuffer(n)}catch(r){this.mediaSource_.player_&&this.mediaSource_.player_.error({code:-3,type:"APPEND_BUFFER_ERR",message:r.message,originalError:r})}}}},{key:"abort",value:function(){this.videoBuffer_&&this.videoBuffer_.abort(),!this.audioDisabled_&&this.audioBuffer_&&this.audioBuffer_.abort(),this.transmuxer_&&this.transmuxer_.postMessage({action:"reset"}),this.pendingBuffers_.length=0,this.bufferUpdating_=!1}}]),t}(d["default"].EventTarget);i["default"]=E}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],77:[function(e,t,i){var n=arguments[3],r=arguments[4],a=arguments[5],s=JSON.stringify,o=!1,u=function(e){function t(n){if(i[n])return i[n].exports;var r=i[n]={i:n,l:!1,exports:{}};return e[n].call(r.exports,r,r.exports,t),r.l=!0,r.exports}var i={};return t.m=e,t.c=i,t.d=function(e,i,n){t.o(e,i)||Object.defineProperty(e,i,{configurable:!1,enumerable:!0,get:n})},t.n=function(e){var i=e&&e.__esModule?function(){return e["default"]}:function(){return e};return t.d(i,"a",i),i},t.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},t.p="",t(t.s=entryModule)};void 0===n&&(o=!0,n=u,r=__webpack_modules__);var d=function(e){for(var t,i=Object.keys(a),o=0;o<i.length;o++){var u=i[o],d=a[u].exports;if(d===e||d&&d["default"]===e){t=u;break}}if(!t){t=Math.floor(Math.pow(16,8)*Math.random()).toString(16);for(var l={},o=0;o<i.length;o++){var u=i[o];l[u]=u}r[t]=["function(require,module,exports){"+e+"(self); }",l]}var f=Math.floor(Math.pow(16,8)*Math.random()).toString(16),c={};return c[t]=t,r[f]=["function(require,module,exports){var f = require("+s(t)+");(f.default ? f.default : f)(self);}",c],"("+n+")({"+Object.keys(r).map(function(e){return s(e)+":["+r[e][0]+","+s(r[e][1])+"]"}).join(",")+"},{},["+s(f)+"])"},l=function(e,t){var i,a="string"==typeof t;i=a?{}:[],Object.keys(r).forEach(function(e){r[e]&&(i[e]=r[e].toString())});var o=__webpack_require__(t);if(!o||o!==e&&o["default"]!==e){var u=i[t];i[t]=u.substring(0,u.length-1)+"\n"+e.name+"();\n}"}var d;if(a){t=s(t);d="{"+Object.keys(i).map(function(e){return s(e)+":"+i[e]}).join(",")+"}"}else d="["+i.join(",")+"]";return"var fn = ("+n.toString().replace("entryModule",t)+")("+d+');\n(typeof fn === "function") && fn(self);'};t.exports=function(e,t){var i;i=o?l(e,t):d(e);var n=new Blob([i],{type:"text/javascript"}),r=window.URL||window.webkitURL||window.mozURL||window.msURL,a=r.createObjectURL(n),s=new Worker(a);return s.objectURL=a,s}},{}],78:[function(e,t,i){(function(i){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function a(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}var s=function(){function e(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,i,n){return i&&e(t.prototype,i),n&&e(t,n),t}}(),o=function(e,t,i){for(var n=!0;n;){var r=e,a=t,s=i;n=!1,null===r&&(r=Function.prototype);var o=Object.getOwnPropertyDescriptor(r,a);if(o!==undefined){if("value"in o)return o.value;var u=o.get;return u===undefined?undefined:u.call(s)}var d=Object.getPrototypeOf(r);if(null===d)return undefined;e=d,t=a,i=s,n=!0,o=d=undefined}},u=e(29),d=n(u),l=e(10),f=n(l),c=e(12),h=n(c),p=e(22),m=n(p),g=e(26),y=e(3),_=n(y),v=e(75),b=e(31),T=n(b),S="undefined"!=typeof window?window.videojs:void 0!==i?i.videojs:null,w=n(S),k=e(6),E=e(4),O=n(E),A=e(15),L=n(A),P=e(30),I=(n(P),e(9)),C=n(I),U=e(14),M=n(U),D=e(11),R={PlaylistLoader:f["default"],Playlist:h["default"],Decrypter:g.Decrypter,AsyncStream:g.AsyncStream,decrypt:g.decrypt,utils:_["default"],STANDARD_PLAYLIST_SELECTOR:D.lastBandwidthSelector,INITIAL_PLAYLIST_SELECTOR:D.lowestBitrateCompatibleVariantSelector,comparePlaylistBandwidth:D.comparePlaylistBandwidth,comparePlaylistResolution:D.comparePlaylistResolution,xhr:(0,m["default"])()};["GOAL_BUFFER_LENGTH","MAX_GOAL_BUFFER_LENGTH","GOAL_BUFFER_LENGTH_RATE","BUFFER_LOW_WATER_LINE","MAX_BUFFER_LOW_WATER_LINE","BUFFER_LOW_WATER_LINE_RATE","BANDWIDTH_VARIANCE"].forEach(function(e){Object.defineProperty(R,e,{get:function(){return w["default"].log.warn("using Hls."+e+" is UNSAFE be sure you know what you are doing"),O["default"][e]},set:function(t){if(w["default"].log.warn("using Hls."+e+" is UNSAFE be sure you know what you are doing"),"number"!=typeof t||t<0)return void w["default"].log.warn("value of Hls."+e+" must be greater than or equal to 0");O["default"][e]=t}})});var x=function(e,t){for(var i=t.media(),n=-1,r=0;r<e.length;r++)if(e[r].id===i.uri){n=r;break}e.selectedIndex_=n,e.trigger({selectedIndex:n,type:"change"})},B=function(e,t){t.representations().forEach(function(t){e.addQualityLevel(t)}),x(e,t.playlists)};R.canPlaySource=function(){return w["default"].log.warn("HLS is no longer a tech. Please remove it from your player's techOrder.")},R.supportsNativeHls=function(){var e=d["default"].createElement("video");return!!w["default"].getTech("Html5").isSupported()&&["application/vnd.apple.mpegurl","audio/mpegurl","audio/x-mpegurl","application/x-mpegurl","video/x-mpegurl","video/mpegurl","application/mpegurl"].some(function(t){return/maybe|probably/i.test(e.canPlayType(t))})}(),R.isSupported=function(){return w["default"].log.warn("HLS is no longer a tech. Please remove it from your player's techOrder.")};var j=w["default"].getComponent("Component"),N=function(e){function t(e,i,n){var a=this;if(r(this,t),o(Object.getPrototypeOf(t.prototype),"constructor",this).call(this,i,n.hls),i.options_&&i.options_.playerId){var s=(0,w["default"])(i.options_.playerId);s.hasOwnProperty("hls")||Object.defineProperty(s,"hls",{get:function(){return w["default"].log.warn("player.hls is deprecated. Use player.tech_.hls instead."),i.trigger({type:"usage",name:"hls-player-access"}),a}})}if(this.tech_=i,this.source_=e,this.stats={},this.ignoreNextSeekingEvent_=!1,this.setOptions_(),this.options_.overrideNative&&(i.featuresNativeVideoTracks||i.featuresNativeAudioTracks))throw new Error("Overriding native HLS requires emulated tracks. See https://git.io/vMpjB");this.on(d["default"],["fullscreenchange","webkitfullscreenchange","mozfullscreenchange","MSFullscreenChange"],function(e){var t=d["default"].fullscreenElement||d["default"].webkitFullscreenElement||d["default"].mozFullScreenElement||d["default"].msFullscreenElement;t&&t.contains(a.tech_.el())&&a.masterPlaylistController_.fastQualityChange_()}),this.on(this.tech_,"seeking",function(){if(this.ignoreNextSeekingEvent_)return void(this.ignoreNextSeekingEvent_=!1);this.setCurrentTime(this.tech_.currentTime())}),this.on(this.tech_,"error",function(){this.masterPlaylistController_&&this.masterPlaylistController_.pauseLoading()}),this.on(this.tech_,"play",this.play)}return a(t,e),s(t,[{key:"setOptions_",value:function(){var e=this;this.options_.withCredentials=this.options_.withCredentials||!1,"number"!=typeof this.options_.blacklistDuration&&(this.options_.blacklistDuration=300),"number"!=typeof this.options_.bandwidth&&(this.options_.bandwidth=4194304),this.options_.enableLowInitialPlaylist=this.options_.enableLowInitialPlaylist&&4194304===this.options_.bandwidth,["withCredentials","bandwidth","handleManifestRedirects"].forEach(function(t){"undefined"!=typeof e.source_[t]&&(e.options_[t]=e.source_[t])}),this.bandwidth=this.options_.bandwidth}},{key:"src",value:function(e){var t=this;e&&(this.setOptions_(),this.options_.url=this.source_.src,this.options_.tech=this.tech_,this.options_.externHls=R,this.masterPlaylistController_=new k.MasterPlaylistController(this.options_),this.playbackWatcher_=new C["default"](w["default"].mergeOptions(this.options_,{seekable:function(){return t.seekable()}})),this.masterPlaylistController_.on("error",function(){w["default"].players[t.tech_.options_.playerId].error(t.masterPlaylistController_.error)}),this.masterPlaylistController_.selectPlaylist=this.selectPlaylist?this.selectPlaylist.bind(this):R.STANDARD_PLAYLIST_SELECTOR.bind(this),this.masterPlaylistController_.selectInitialPlaylist=R.INITIAL_PLAYLIST_SELECTOR.bind(this),this.playlists=this.masterPlaylistController_.masterPlaylistLoader_,this.mediaSource=this.masterPlaylistController_.mediaSource,Object.defineProperties(this,{selectPlaylist:{get:function(){return this.masterPlaylistController_.selectPlaylist},set:function(e){this.masterPlaylistController_.selectPlaylist=e.bind(this)}},throughput:{get:function(){return this.masterPlaylistController_.mainSegmentLoader_.throughput.rate},set:function(e){this.masterPlaylistController_.mainSegmentLoader_.throughput.rate=e,this.masterPlaylistController_.mainSegmentLoader_.throughput.count=1}},bandwidth:{get:function(){return this.masterPlaylistController_.mainSegmentLoader_.bandwidth},set:function(e){this.masterPlaylistController_.mainSegmentLoader_.bandwidth=e,this.masterPlaylistController_.mainSegmentLoader_.throughput={rate:0,count:0}}},systemBandwidth:{get:function(){var e=1/(this.bandwidth||1),t=undefined;return t=this.throughput>0?1/this.throughput:0,Math.floor(1/(e+t))},set:function(){w["default"].log.error('The "systemBandwidth" property is read-only')}}}),Object.defineProperties(this.stats,{bandwidth:{get:function(){return t.bandwidth||0},enumerable:!0},mediaRequests:{get:function(){return t.masterPlaylistController_.mediaRequests_()||0},enumerable:!0},mediaRequestsAborted:{get:function(){return t.masterPlaylistController_.mediaRequestsAborted_()||0},enumerable:!0},mediaRequestsTimedout:{get:function(){return t.masterPlaylistController_.mediaRequestsTimedout_()||0},enumerable:!0},mediaRequestsErrored:{get:function(){return t.masterPlaylistController_.mediaRequestsErrored_()||0},enumerable:!0},mediaTransferDuration:{get:function(){return t.masterPlaylistController_.mediaTransferDuration_()||0},enumerable:!0},mediaBytesTransferred:{get:function(){return t.masterPlaylistController_.mediaBytesTransferred_()||0},enumerable:!0},mediaSecondsLoaded:{get:function(){return t.masterPlaylistController_.mediaSecondsLoaded_()||0},enumerable:!0}}),this.tech_.one("canplay",this.masterPlaylistController_.setupFirstPlay.bind(this.masterPlaylistController_)),this.masterPlaylistController_.on("selectedinitialmedia",function(){(0,L["default"])(t)}),this.on(this.masterPlaylistController_,"progress",function(){this.tech_.trigger("progress")}),this.on(this.masterPlaylistController_,"alive",function(){this.tech_.trigger("alive")}),this.on(this.masterPlaylistController_,"noLiveStream",function(){this.tech_.trigger("noLiveStream")}),this.on(this.masterPlaylistController_,"firstplay",function(){this.ignoreNextSeekingEvent_=!0}),this.tech_.ready(function(){return t.setupQualityLevels_()}),this.tech_.el()&&this.tech_.src(w["default"].URL.createObjectURL(this.masterPlaylistController_.mediaSource)))}},{key:"setupQualityLevels_",value:function(){var e=this,t=w["default"].players[this.tech_.options_.playerId];t&&t.qualityLevels&&(this.qualityLevels_=t.qualityLevels(),this.masterPlaylistController_.on("selectedinitialmedia",function(){B(e.qualityLevels_,e),t.trigger({type:"levels",levels:e.qualityLevels_.levels_,currentQuality:e.qualityLevels_.selectedIndex_})}),this.playlists.on("mediachange",function(){x(e.qualityLevels_,e.playlists),t.trigger({type:"levelsChanged",levels:e.qualityLevels_.levels_,currentQuality:e.qualityLevels_.selectedIndex_})}))}},{key:"play",value:function(){this.masterPlaylistController_.play()}},{key:"setCurrentTime",value:function(e){this.masterPlaylistController_.setCurrentTime(e)}},{key:"duration",value:function(){return this.masterPlaylistController_.duration()}},{key:"seekable",value:function(){return this.masterPlaylistController_.seekable()}},{key:"dispose",value:function(){this.playbackWatcher_&&this.playbackWatcher_.dispose(),this.masterPlaylistController_&&this.masterPlaylistController_.dispose(),this.qualityLevels_&&this.qualityLevels_.dispose(),o(Object.getPrototypeOf(t.prototype),"dispose",this).call(this)}}]),t}(j),F=function q(e){return{canHandleSource:function(t){var i=arguments.length<=1||arguments[1]===undefined?{}:arguments[1],n=w["default"].mergeOptions(w["default"].options,i);return(!n.hls||!n.hls.mode||n.hls.mode===e)&&q.canPlayType(t.type,n)},handleSource:function(t,i){var n=arguments.length<=2||arguments[2]===undefined?{}:arguments[2],r=w["default"].mergeOptions(w["default"].options,n,{hls:{mode:e}});return"flash"===e&&i.setTimeout(function(){i.trigger("loadstart")},1),i.hls=new N(t,i,r),i.hls.xhr=(0,m["default"])(),i.hls.src(t.src),i.hls},canPlayType:function(e){var t=arguments.length<=1||arguments[1]===undefined?{}:arguments[1],i=w["default"].mergeOptions(w["default"].options,t);return q.canPlayType(e,i)?"maybe":""}}};F.canPlayType=function(e,t){return!(w["default"].browser.IE_VERSION&&w["default"].browser.IE_VERSION<=10)&&/^(audio|video|application)\/(x-|vnd\.apple\.)?mpegurl/i.test(e)},"undefined"!=typeof w["default"].MediaSource&&"undefined"!=typeof w["default"].URL||(w["default"].MediaSource=v.MediaSource,w["default"].URL=v.URL);w["default"].getTech("Flash");v.MediaSource.supportsNativeMediaSources()&&w["default"].getTech("Html5").registerSourceHandler(F("html5"),0),w["default"].HlsHandler=N,w["default"].HlsSourceHandler=F,w["default"].Hls=R,w["default"].use||w["default"].registerComponent("Hls",R),w["default"].m3u8=T["default"],w["default"].options.hls=w["default"].options.hls||{},w["default"].registerPlugin?w["default"].registerPlugin("reloadSourceOnError",M["default"]):w["default"].plugin("reloadSourceOnError",M["default"]),t.exports={Hls:R,HlsHandler:N,HlsSourceHandler:F}}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}]},{},[78]);