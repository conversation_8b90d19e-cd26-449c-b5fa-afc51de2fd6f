<template>
  <div >

    <div class="catlist kc-cat-list">
      <span class="title">全部分类：</span>
      <span class="list">
        <a @click="changeCatId()" :class="{'active':null==course_query.CourseCategoryId}">全部</a>
        <span>
          <a v-for="item in cates" @click="changeCatId(item.id)" :class="{'active':item.id==course_query.CourseCategoryId}">{{item.name}}</a>
        </span>
      </span>
      <span class="node-list" v-if="Nodecates.length>0&& IsShowNodeCat">
        <a v-for="item in Nodecates" @click="changeNodeCatId(item.id)" :class="{'active':item.id==course_query.CourseNodeCategoryId}">{{item.name}}</a>
      </span>
      <span class="ft" ref="ft" >
        <span @click="isShowNode=1" class="con">
            <span class="content"> {{course_query.Sorting=="ViewCount desc"?'最热':'最新'}} </span>
            <svg
            xmlns="http://www.w3.org/2000/svg"
            xmlns:xlink="http://www.w3.org/1999/xlink"
            width="17px" height="9px">
            <path fill-rule="evenodd"  fill="rgb(74, 75, 79)"
            d="M16.094,1.691 L9.137,8.647 C9.120,8.668 9.113,8.694 9.094,8.713 C8.897,8.910 8.638,9.005 8.380,9.002 C8.122,9.005 7.864,8.910 7.667,8.713 C7.647,8.694 7.640,8.668 7.623,8.647 L0.667,1.691 C0.279,1.303 0.279,0.674 0.667,0.287 C1.055,-0.101 1.683,-0.101 2.071,0.287 L8.380,6.595 L14.689,0.287 C15.077,-0.101 15.706,-0.101 16.094,0.287 C16.481,0.674 16.481,1.303 16.094,1.691 Z"/>
            </svg>
        </span>
        <span class="node" v-if="isShowNode" >
          <a @click="changeSort()" :class="{'cur':course_query.Sorting!='ViewCount desc'}">最新</a>
          <a @click="changeSort('ViewCount desc')" :class="{'cur':course_query.Sorting=='ViewCount desc'}">最热</a>
        </span>
      </span>
    </div>
    <div class="CourseList">
      <div v-for="item in courseList" class="courseitem" :key="'course' + item.id" @click="prewCourse(item.id)">
        <span class="imgpan"> <img :src="item.coverUrl" /></span>
        <span class="title"  :title="item.name">{{item.name}}</span>
        <span class="star-p-list">
          <span v-for="(i,index) in 5" :key="index" class="star-a-item">
            <img :src="Math.round(item.courseScore) >index?'/images/liang.png':'/images/an.png'" />
          </span>
          <span class="score_txt">{{Math.round(item.courseScore)}}分</span>
        </span>
        <div class="p-div">
          <span class="teacher">
            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="16px" height="16px">
              <path fill-rule="evenodd" fill="rgb(146, 146, 146)"
                d="M15.012,16.010 C14.469,16.010 14.031,15.563 14.025,15.010 L13.846,15.010 C12.923,12.152 8.112,10.038 8.009,9.993 C8.006,9.993 8.003,9.994 8.000,9.994 C7.997,9.994 7.993,9.993 7.991,9.993 C7.888,10.038 3.077,12.152 2.154,15.010 L1.975,15.010 C1.969,15.563 1.531,16.010 0.988,16.010 C0.443,16.010 -0.000,15.558 -0.000,15.000 C-0.000,14.640 0.195,14.339 0.472,14.160 C1.475,12.065 3.938,10.293 5.777,9.195 C4.379,8.340 2.985,6.853 2.985,4.997 C2.985,2.025 5.008,-0.000 8.000,-0.000 C10.992,-0.000 13.015,2.025 13.015,4.997 C13.015,6.853 11.621,8.340 10.223,9.195 C12.062,10.293 14.525,12.065 15.528,14.160 C15.805,14.339 16.000,14.640 16.000,15.000 C16.000,15.558 15.557,16.010 15.012,16.010 ZM10.998,5.009 C10.998,3.168 9.845,2.009 8.000,2.009 C6.155,2.009 5.002,3.168 5.002,5.009 C5.002,6.850 7.012,8.037 8.000,8.009 C8.988,7.982 10.998,6.850 10.998,5.009 Z" />
            </svg>
            <span>
              {{item.lecturer}}
            </span>
          </span>
          <span class="joincount">
            <img src="/images/group.png" />
            <span> {{item.courseJoinCount==null?0:item.courseJoinCount | showNum}}</span>
          </span>
          <span class="classhour">{{item.classHour}}课时</span>
        </div>
      </div>
      <NoContent :desc="'您暂无权限学习该模块课程'" v-if="courseList.length==0"></NoContent>
    </div>
    <el-pagination
      v-if="course_query.total>course_query.MaxResultCount"
      class="my_pagination"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="course_query.page"
      :page-sizes="[8, 20, 40]"
      :page-size="course_query.MaxResultCount"
      layout="total, sizes, prev, pager, next, jumper"
      :total="course_query.total"
    ></el-pagination>
</div>
</template>
<script>
import {getCategory, getCoursesList} from '@/api/course';
import NoContent from '@/components/NoContent'
export default {
  name: 'index',
  components:{
    NoContent
  },
  data() {
   return {
     isShowNode:0,
     cates:[],
     Nodecates:[],
     courseList:[],
     course_query:{
      Filter:this.$route.query.filter,
      CourseCategoryId:null,
      CourseNodeCategoryId:null,
      SkipCount:0,
      MaxResultCount:8,
      page:1,
      total:0,
      Sorting:'', //ViewCount 热度
      IsShowNodeCat:false
     }
   }
  },
  watch:{
    '$route':{
      handler:  function (val, oldVal) {
        if(this.$route.query.node) {
          this.course_query = this.$route.query.course_query
          // this.course_query.CourseNodeCategoryId = this.$route.query.categoryId
          this.course_query.Filter = this.$route.query.filter
          this.IsShowNodeCat = true
          this.cates = this.$route.query.cates
          this.Nodecates = this.$route.query.Nodecates
          this.getCourseList()
          // const res = await getCategory()
          // for await (var i of res.items) {
          //   const nodeRes = await getCategory({parentId:i.id})
          //   let result = nodeRes.items.find(item => item.id === this.$route.query.categoryId)
          //   if (result) {
          //     this.course_query.CourseCategoryId = i.id
          //     this.Nodecates = nodeRes.items
          //     this.getCourseList()
          //     return
          //   }
          // }
        } else {
          this.course_query.CourseCategoryId = this.$route.query.categoryId
          this.course_query.Filter = this.$route.query.filter
          this.getCourseList()
        }
      },
      immediate:true
    }
  },
  beforeRouteLeave(to, form, next) {
    if(this.course_query.CourseNodeCategoryId) {

      to.query.node = true
      to.query.course_query = this.course_query
      to.query.Nodecates = this.Nodecates
      to.query.cates = this.cates
    }
    next()
  },
  beforeDestroy(){
    document.removeEventListener('click',this.clickFun)
  },
  mounted(){
    document.addEventListener('click',this.clickFun)
    getCategory().then(res=>{
      this.cates = res.items
    })
  },
  methods: {
    getCatList(data){
      getCategory(data).then(res=>{
        this.Nodecates = res.items
      })
    },
    clickFun(e){
      //debugger;
      let _this = this
      if(!!_this.$refs.ft!=undefined&& e.target!=undefined&&_this.$refs.ft.contains(e.target)) return;
      _this.isShowNode = false
    },
    // 点击类别
    changeCatId(id){
      this.course_query.page = 1
      this.course_query.CourseCategoryId = id
      this.course_query.CourseNodeCategoryId = null
      this.getCourseList()
      if(id!=undefined){
        let data =  {parentId:id}
        this.getCatList(data)
        this.IsShowNodeCat = true
      } else{
        this.IsShowNodeCat = false
      }
    },
    changeNodeCatId(id){
      this.course_query.page = 1
      // this.course_query.CourseCategoryId = id
      this.course_query.CourseNodeCategoryId = id
      this.getCourseList()
    },
    prewCourse(id){
      this.$router.push({name: 'CourseInfo', query:{id: id}})
    },
    //改变排序方式
    changeSort(str){
      this.isShowNode = false
      this.course_query.Sorting = str
      this.course_query.page = 1
      this.getCourseList()
    },
    handleSizeChange(val) {
      this.course_query.MaxResultCount = val
      this.getCourseList()
    },
    handleCurrentChange(val) {
      this.course_query.page = val
      this.getCourseList()
    },
    getCourseList(){
      let data = {
        Filter:this.course_query.Filter,
        CourseCategoryId:this.course_query.CourseNodeCategoryId ==null?this.course_query.CourseCategoryId:this.course_query.CourseNodeCategoryId,
        SkipCount:this.course_query.MaxResultCount*(this.course_query.page-1),
        MaxResultCount:this.course_query.MaxResultCount,
        Sorting:this.course_query.Sorting
      }
      getCoursesList(data).then(res=>{
        this.courseList = res.items
        this.course_query.total = res.totalCount
      })
    },
  }
}
</script>

