<template>
  <div class="biaozhun">
    <div class="maps">
      <img src="../../assets/image/zan/map.png">
      <span>技术中心</span>
      <img src="../../assets/image/zan/left.png">
      <span>技术文章</span>
    </div>
    <div class="qiye-box">
      <div
        v-for="item in list"
        :key="item.id"
        class="qiye-item"
        @click="go(item.id)"
      >
        <img
          :src="item.imgUrl"
          class="imgs"
        >
        <p class="title">
          {{ item.title }}
        </p>
      </div>
    </div>
  </div>
</template>
<script>
export default {
    data() {
        return {
            list: [
                {
                    id: 1,
                    title: "智能表面：你的车身就是一块触摸屏",
                    imgUrl: require("../../assets/image/static/jishu1.png"),
                },
                {
                    id: 2,
                    title: "氢燃料汽车：加油三分钟，续航1000公里",
                    imgUrl: require("../../assets/image/static/jishu2.png"),
                },
                {
                    id: 3,
                    title: "固态电池：终结“充电焦虑”得终极答案",
                    imgUrl: require("../../assets/image/static/jishu3.png"),
                },
                {
                    id: 4,
                    title: "线控底盘：方向盘和车轮“分手”了",
                    imgUrl: require("../../assets/image/static/jishu4.png"),
                },
                {
                    id: 5,
                    title: "车路协同：道路比你得车更聪明",
                    imgUrl: require("../../assets/image/static/jishu5.png"),
                },
                {
                    id: 6,
                    title: "生物材料：玉米秸秆造车身",
                    imgUrl: require("../../assets/image/static/jishu6.png"),
                },
                {
                    id: 7,
                    title: "数字气味：你的空调能吹出雨后森林",
                    imgUrl: require("../../assets/image/static/jishu7.png"),
                },
                {
                    id: 8,
                    title: "垂直起降：堵车时直接“飞”过去",
                    imgUrl: require("../../assets/image/static/jishu8.png"),
                },
            ],
        };
    },
    methods: {
        go(id) {
            if (id === 1) {
                const url = this.$router.resolve({
                    name: "jishuDetail",
                }).href;

                window.open(url, "_blank");
            }
        },
    },
};
</script>
<style scoped>
.qiye-box {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    margin-top: 20px;
}

.qiye-item {
    box-shadow: 0 5px 10px 0 rgba(0, 0, 0, 0.1);
    border-radius: 20px;
    width: 260px;
    /* height: 150px; */
    -webkit-box-sizing: border-box;
    background: #fff;
    margin-right: 60px;
    margin-bottom: 50px;
    cursor: pointer;
}

.imgs {
    width: 100%;
    height: 220px;
    display: block;
}

.title {
    color: #a9aaab;
    padding: 0 20px;
    box-sizing: border-box;
    border-radius: 3px;
    margin-right: 10px;
    font-size: 14px;
    line-height: 20px;
}
</style>
