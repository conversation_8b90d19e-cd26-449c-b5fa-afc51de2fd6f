<template>
  <div >
    <div class="search-bg-box">
      <img src="/images/search-bg.png" />
      <div class="search_input_box2">
        <input type="text"  v-model='searchVal'/>
        <button  class="btn_search2" @click="search()">
          <img src="/images/icon-search2.png"/>
          <span>搜 索</span>
        </button>
      </div>
    </div>
    <div class="search-tab-box">
      <div  class="tabs">
        <a @click="tabIndex=0" :class="{active:tabIndex==0}">课程</a>
        <a @click="tabIndex=1" :class="{active:tabIndex==1}">直播</a>
        <a @click="tabIndex=2" :class="{active:tabIndex==2}">知识锦囊</a>
        <a @click="tabIndex=3" :class="{active:tabIndex==3}">培训课程</a>
      </div>
    </div>
    <div v-if="tabIndex==0">
      <div class="CourseList" >
        <div v-for="item in courseList" class="courseitem" :key="'course' + item.id" @click="prewCourse(item.id)">
          <span class="imgpan"> <img :src="item.coverUrl" /></span>
          <span class="title"  :title="item.name">{{item.name}}</span>
          <span class="star-p-list">
            <span v-for="(i,index) in 5" :key="index" class="star-a-item">
              <img :src="Math.round(item.courseScore) >index?'/images/liang.png':'/images/an.png'" />
            </span>
            <span class="score_txt">{{Math.round(item.courseScore)}}分</span>
          </span>
          <div class="p-div">
            <span class="teacher">
              <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="16px" height="16px">
                <path fill-rule="evenodd" fill="rgb(146, 146, 146)"
                  d="M15.012,16.010 C14.469,16.010 14.031,15.563 14.025,15.010 L13.846,15.010 C12.923,12.152 8.112,10.038 8.009,9.993 C8.006,9.993 8.003,9.994 8.000,9.994 C7.997,9.994 7.993,9.993 7.991,9.993 C7.888,10.038 3.077,12.152 2.154,15.010 L1.975,15.010 C1.969,15.563 1.531,16.010 0.988,16.010 C0.443,16.010 -0.000,15.558 -0.000,15.000 C-0.000,14.640 0.195,14.339 0.472,14.160 C1.475,12.065 3.938,10.293 5.777,9.195 C4.379,8.340 2.985,6.853 2.985,4.997 C2.985,2.025 5.008,-0.000 8.000,-0.000 C10.992,-0.000 13.015,2.025 13.015,4.997 C13.015,6.853 11.621,8.340 10.223,9.195 C12.062,10.293 14.525,12.065 15.528,14.160 C15.805,14.339 16.000,14.640 16.000,15.000 C16.000,15.558 15.557,16.010 15.012,16.010 ZM10.998,5.009 C10.998,3.168 9.845,2.009 8.000,2.009 C6.155,2.009 5.002,3.168 5.002,5.009 C5.002,6.850 7.012,8.037 8.000,8.009 C8.988,7.982 10.998,6.850 10.998,5.009 Z" />
              </svg>
              <span>
                {{item.lecturer}}
              </span>
            </span>
            <span class="joincount">
              <img src="/images/group.png" />
              <span> {{item.courseJoinCount==null?0:item.courseJoinCount | showNum}}</span>
            </span>
            <span class="classhour">{{item.classHour}}课时</span>
          </div>
        </div>
        <NoContent v-if="courseList.length==0"></NoContent>
      </div>
      <el-pagination
        v-if="course_query.total>course_query.MaxResultCount"
        class="my_pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="course_query.page"
        :page-sizes="[8, 20, 40]"
        :page-size="course_query.MaxResultCount"
        layout="total, sizes, prev, pager, next, jumper"
        :total="course_query.total"
      ></el-pagination>
    </div>
    <div class="myLiveList search—live—list" v-if="tabIndex==1">
      <div class="live-item-div"  v-for="item in lives">
          <div class="live-item" >
            <div class="live-img-box">
              <img :src="item.coverImage" class="coverImage">
              <!-- <div class="shaw"></div> -->
              <div v-if="item.liveStreamStatue === 0" class="live_state_default live_state_box_s">
                <img src="/images/icon_play.png" mode="widthFix">
                <span class="live_span">未开始</span>
              </div>
              <div v-if="item.liveStreamStatue === 1" class="live_state_default live_state_box_p">
                <!-- <img src="/images/icon_liveing.png" mode="widthFix" /> -->
                <img
                  src="data:image/gif;base64,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"
                  alt="">
                <span class="live_span">直播中</span>
              </div>
              <div v-if="item.liveStreamStatue === 2 && item.allowPlayBack" class="live_state_default live_state_box_e live_play_back">
                <img src="/images/icon_play.png" mode="widthFix">
                <span class="live_span">回放</span>
              </div>
              <div v-if="item.liveStreamStatue === 2 && !item.allowPlayBack" class="live_state_default live_state_box_e">
                <img src="/images/icon_noplay.png" mode="widthFix">
                <span class="live_span">回放已禁</span>
              </div>
            </div>
            <div class="live-r-div">
              <div class="title">
                {{ item.title }}
              </div>
              <!-- <div class="desc">
                {{ item.liveStream.description }}
              </div> -->
              <div class="m">
                <span class="lecturer">主讲人：{{ item.lecturer }}</span>
                <span class="usercount"><img src="/images/icon-usercount.png">
                  {{ item.userCount }} 人</span>
                <span class="time"><img src="/images/icon-timelong.png">
                  {{ item.startTime | DateFromte("YYYY.MM.DD HH:mm") }} -
                  {{
                  item.endTime | DateFromte("YYYY.MM.DD HH:mm")
                  }}</span>
                  <span class="time" style="    border-left: 1px solid #d8d8d8; margin-left: 30px;"> {{item.classHour}} 课时</span>
              </div>
              <div class="b">
                <div v-if="item.liveStreamStatue == 1|| item.liveStreamStatue == 0" style="display: inline-block;">
                  <button
                    class="statue2"
                    @click="prewLive(item, 0)"
                  >
                    客户端进入直播
                  </button>
                  <button style="margin-left: 20px" class="statue2"  @click="prewLive(item, 1)">
                    网页进入直播
                  </button>
                </div>
                <button v-else-if="!item.allowPlayBack" class="statue1" disabled="disabled">
                  回放已禁
                </button>
                <button v-else-if="!item.hasPlayBack" class="statue1" disabled="disabled">
                  暂无回放
                </button>
                <button v-else class="statue2"  @click="prewLive(item)">
                  观看回放
                </button>
                <button class="btn_enter" v-if="(item.closeDate==null || (item.closeDate!=null&& new Date()< new Date(item.closeDate)))&&item.examinationId!=null"  @click="intoExam(item)" >进入考评</button>
              </div>
            </div>
          </div>
      </div>
      <el-pagination
        v-if="Live_query.total>Live_query.MaxResultCount"
        class="my_pagination"
        @size-change="handleSizeChange1"
        @current-change="handleCurrentChange1"
        :current-page="Live_query.page"
        :page-sizes="[8, 20, 40]"
        :page-size="Live_query.MaxResultCount"
        layout="total, sizes, prev, pager, next, jumper"
        :total="Live_query.total"
      ></el-pagination>
    </div>
    <div v-if="tabIndex==2">
      <div class="CourseList" >
        <div class="courseitem video-item" @click="viewVideo(item)" v-for="item in videoList">
          <span class="imgpan">
            <img :src="item.thumbnailUrl" />
            <span class="bg-op06"></span>
          </span>
          <div class="infopan">
            <div class="p-div1">
              <span class="teacher-name">
                <img  v-if="item.profilePhoto==null" src="/images/user.png" class="userImg"/>
                <img  v-else :src="item.profilePhoto" class="userImg"/>
                <span>{{item.author}}</span>
              </span>
              <span class="time">{{item.duration | timeFromte2}}</span>
            </div>
            <div class="p-div">
              <span class="title"  :title="item.name">{{item.name }}</span>
              <span class="count">
                <img  src="/microVideo/icon_like_pc_n.png" />
                <span>{{item.likeCount | showNum }}</span>
              </span>
              <span class="count">
                <img src="/microVideo/icon_person_pc.png" />
                <span>{{item.viewCount | showNum}}</span>
              </span>
              <span class="count">
                <img  src="/microVideo/icon_comment_pc.png" />
                <span>{{item.commentCount | showNum}} </span>
              </span>

            </div>
          </div>
          <img class="icon_play" src="/microVideo/icon_play2_n.png" />
        </div>
        <NoContent v-if="videoList.length==0"></NoContent>
      </div>
      <el-pagination
        v-if="Video_query.total>Video_query.MaxResultCount"
        class="my_pagination"
        @size-change="handleSizeChange2"
        @current-change="handleCurrentChange2"
        :current-page="Video_query.page"
        :page-sizes="[8, 16, 32]"
        :page-size="Video_query.MaxResultCount"
        layout="total, sizes, prev, pager, next, jumper"
        :total="Video_query.total"
      ></el-pagination>
    </div>
    <div v-if="tabIndex==3">
      <div class="CourseList" >
        <div v-for="item in traincourseList" class="courseitem" :key="'course' + item.id" @click="prewCourse2(item)">
          <span class="imgpan"> <img :src="item.coverUrl" /></span>
          <span class="title"  :title="item.name">{{item.courseName}}</span>
          <span class="star-p-list">
            <span v-for="(i,index) in 5" :key="index" class="star-a-item">
              <img :src="Math.round(item.courseScore) >index?'/images/liang.png':'/images/an.png'" />
            </span>
            <span class="score_txt">{{Math.round(item.courseScore)}}分</span>
          </span>
          <div class="p-div">
            <span class="teacher">
              <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="16px" height="16px">
                <path fill-rule="evenodd" fill="rgb(146, 146, 146)"
                  d="M15.012,16.010 C14.469,16.010 14.031,15.563 14.025,15.010 L13.846,15.010 C12.923,12.152 8.112,10.038 8.009,9.993 C8.006,9.993 8.003,9.994 8.000,9.994 C7.997,9.994 7.993,9.993 7.991,9.993 C7.888,10.038 3.077,12.152 2.154,15.010 L1.975,15.010 C1.969,15.563 1.531,16.010 0.988,16.010 C0.443,16.010 -0.000,15.558 -0.000,15.000 C-0.000,14.640 0.195,14.339 0.472,14.160 C1.475,12.065 3.938,10.293 5.777,9.195 C4.379,8.340 2.985,6.853 2.985,4.997 C2.985,2.025 5.008,-0.000 8.000,-0.000 C10.992,-0.000 13.015,2.025 13.015,4.997 C13.015,6.853 11.621,8.340 10.223,9.195 C12.062,10.293 14.525,12.065 15.528,14.160 C15.805,14.339 16.000,14.640 16.000,15.000 C16.000,15.558 15.557,16.010 15.012,16.010 ZM10.998,5.009 C10.998,3.168 9.845,2.009 8.000,2.009 C6.155,2.009 5.002,3.168 5.002,5.009 C5.002,6.850 7.012,8.037 8.000,8.009 C8.988,7.982 10.998,6.850 10.998,5.009 Z" />
              </svg>
              <span>
                {{item.lecturer}}
              </span>
            </span>
            <span class="joincount">
              <img src="/images/group.png" />
              <span> {{item.courseJoinCount==null?0:item.courseJoinCount | showNum}}</span>
            </span>
            <span class="classhour">{{item.classHour}}课时</span>
          </div>
        </div>
        <NoContent v-if="courseList.length==0"></NoContent>
      </div>
      <el-pagination
        v-if="traincourse_query.total>traincourse_query.MaxResultCount"
        class="my_pagination"
        @size-change="handleSizeChange3"
        @current-change="handleCurrentChange3"
        :current-page="traincourse_query.page"
        :page-sizes="[8, 20, 40]"
        :page-size="traincourse_query.MaxResultCount"
        layout="total, sizes, prev, pager, next, jumper"
        :total="traincourse_query.total"
      ></el-pagination>
    </div>
    <el-dialog title="提示" :visible.sync="liveDialog" width="30%">
      <div>
        即将打开车博苑直播端，请稍后 <br><br>
        如果您未安装直播客户端，请<a :href="downClient">下载车博苑直播端</a>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="liveDialog = false">确 定</el-button>
      </span>
    </el-dialog>
</div>
</template>
<script>
import { getCoursesList,getMyTrainscourses } from '@/api/course';
import { getlives  } from "@/api/live";
import { myLiveUrl, myLiveBackUrl,  } from "@/api/user";
import { getByLive} from "@/api/exam";
import { getVideoList} from '@/api/microVideo';
import NoContent from '@/components/NoContent'
export default {
  name: 'index',
  components:{
    NoContent
  },
  data() {
   return {
     //isShowNode:0,
     //cates:[],
     //Nodecates:[],
     searchVal:'',
     liveDialog:false,
     downClient:'',
     courseList:[],
     lives:[],
     videoList:[],
     traincourseList:[],
     tabIndex:0,
     userLogin: this.$store.getters.token != null ? true : false,
     course_query:{
      Filter:this.$route.query.filter,
      CourseCategoryId:null,
      CourseNodeCategoryId:null,
      SkipCount:0,
      MaxResultCount:8,
      page:1,
      total:0,
      Sorting:'', //ViewCount 热度
      IsShowNodeCat:false
     },
     Live_query:{
        Filter:'',
        LiveThemeId:'',
        SkipCount:0,
        MaxResultCount:8,
        page:1,
      },
      Video_query: {
				Filter: '',
				KnowledgeCategoryId: null,
				Author:null,
				SkipCount: 0,
				MaxResultCount:8,
				page: 1,
				total: 0,
			},
      traincourse_query:{
        Filter:this.$route.query.filter,
        SkipCount:0,
        MaxResultCount:8,
        page:1,
        total:0,
        Sorting:'', //ViewCount 热度
      },
   }
  },
  watch:{
    '$route':{
      handler: function (val, oldVal) {
        if(this.$route.query.index!=undefined)  this.tabIndex = this.$route.query.index
        this.searchVal = this.$route.query.filter
        this.init()
      },
      immediate:true
    },
    tabIndex: {
      handler: function (val, oldVal) {
        this.init()
      },
      // deep: true
    },
  },
  mounted(){
    this.OSnow()
  },
  methods: {
    intoExam(item){
        getByLive({liveId: item.id}).then(res=>{
          item.isPublish = res.isPublish
          item.passScore = res.passScore
          if(!item.isPublish){ //未发布
            this.$message.info("测评未开始");
          } else{
            if(res.examPass){
              this.$confirm('您本次考评已经通过，确定需要重新答题吗？',"温馨提示",{
                  confirmButtonText: '确定',
                  cancelButtonText: '取消',
                  type:"warning",
              }).then(() => {
                this.$router.push({ name: "LiveEvaluate", query: { ExamId: item.examinationId ,name:item.title,passScore:item.passScore} });
              }).catch(() => { });
            } else{
              this.$router.push({ name: "LiveEvaluate", query: { ExamId: item.examinationId ,name:item.title,passScore:item.passScore} });
            }

          }
        })
      },
    search(){
      this.init()
    },
    init(){
      if(this.searchVal=='')  this.$message.info('请输入搜索关键字')
      else{
        switch(this.tabIndex){
          case 0: { // 课程
            this.course_query.Filter = this.searchVal
            this.course_query.page = 1
            this.getCourseList()
            break;
          }
          case 1:{ // 直播
            this.Live_query.page = 1
            this.Live_query.Filter = this.searchVal
            this.getlives()
            break;
          }
          case 2:{ // 知识锦囊
            this.Video_query.Filter = this.searchVal
            this.Video_query.page = 1
            this.getVideoList()
            break;
          }
          case 3: { // 课程
            this.traincourse_query.Filter = this.searchVal
            this.traincourse_query.page = 1
            this.getTrainCourseList()
            break;
          }
        }
      }

    },
    prewCourse(id){
      this.$router.push({name: 'CourseInfo', query:{id: id}})
    },
    prewCourse2 (item) {
          this.$router.push({ name: 'CourseInfo', query: { id: item.courseId, trainId: item.trainId } })
    },
    viewVideo(item){
      this.$router.push({name: 'mircovideoinfo', query:{id: item.id}})
    },
    //改变排序方式
    changeSort(str){
      this.isShowNode = false
      this.course_query.Sorting = str
      this.course_query.page = 1
      this.getCourseList()
    },
    handleSizeChange(val) {
      this.course_query.MaxResultCount = val
      this.getCourseList()
    },
    handleCurrentChange(val) {
      this.course_query.page = val
      this.getCourseList()
    },
    handleSizeChange1(val) {
        this.Live_query.MaxResultCount = val
        this.getlives()
      },
    handleCurrentChange1(val) {
        this.Live_query.page = val
        this.getlives()
    },
    handleSizeChange2(val) {
      this.Video_query.MaxResultCount = val
      this.getVideoList()
    },
    handleCurrentChange2(val) {
      this.Video_query.page = val
      this.getVideoList()
    },
    handleSizeChange3(val) {
      this.traincourse_query.MaxResultCount = val
      this.getTrainCourseList()
    },
    handleCurrentChange3(val) {
      this.traincourse_query.page = val
      this.getTrainCourseList()
    },
    getCourseList(){
      let data = {
        Filter:this.course_query.Filter,
        CourseCategoryId:this.course_query.CourseNodeCategoryId ==null?this.course_query.CourseCategoryId:this.course_query.CourseNodeCategoryId,
        SkipCount:this.course_query.MaxResultCount*(this.course_query.page-1),
        MaxResultCount:this.course_query.MaxResultCount,
      }
      getCoursesList(data).then(res=>{
        this.courseList = res.items
        this.course_query.total = res.totalCount
      })
    },
    getTrainCourseList(){
      let data = {
        Filter:this.traincourse_query.Filter,
        SkipCount:this.traincourse_query.MaxResultCount*(this.course_query.page-1),
        MaxResultCount:this.traincourse_query.MaxResultCount
      }
      getMyTrainscourses(data).then(res=>{
        this.traincourseList = res.items
        this.traincourse_query.total = res.totalCount
      })
    },
    getlives(){
      let data = {
        Filter:this.Live_query.Filter,
        SkipCount:this.Live_query.MaxResultCount*(this.Live_query.page-1),
        MaxResultCount:this.Live_query.MaxResultCount,
      }
      getlives(data).then(res=>{
        this.lives = res.items
        this.Live_query.total = res.totalCount
      })

    },
    getVideoList(){
      this.Video_query.SkipCount = (this.Video_query.page-1)*this.Video_query.MaxResultCount
      getVideoList(this.Video_query).then(res=>{
        this.videoList = res.items
        this.Video_query.total = res.totalCount
      })
    },
    prewLive (item, t) {
        if (!this.userLogin) {
          this.$store.dispatch("user/toggleloginbox", true);
        } // 未登录
        // else if(item.liveStreamStatue==0){ //未开始
        // 	this.$message.info("直播还未开始，请耐心等待")
        // }
        else if (item.liveStreamStatue == 1 || item.liveStreamStatue == 0) {
          //进行中
          this.intoLive(item.id, t);
        } else if (!item.allowPlayBack) {
          //回放禁止
          this.$message.info("该直播已禁止回放,无法观看");
        } else if (!item.hasPlayBack) {
          //暂无回放
          this.$message.info("该直播暂时未生成回放,无法观看");
        } else {
          //console.log(item.id);
          this.viewBackVideo(item.id);
        }
      },
      async viewBackVideo (id) {
        var res = await myLiveBackUrl(id);
        // this.$router.push({
        //   name: 'LiveCenter'
        // })
         window.open(res, "_blank");

      },
      async intoLive (id, t) {
        var res = await myLiveUrl(id);

        if (!this.Video_query.isClient && t === 1) {
          window.open(res.webUrl, "_blank");
        } else {
          res.clientUrl = res.clientUrl.replace('baijiacloud', 'huizhixueyuan');
          this.getHref(res.clientUrl);
        }
      },
      getbackurl (id) {
        return new Promise((resolve, reject) => {
          myLiveBackUrl({ id: id }).then(
            (res) => {
              resolve(res);
            },
            (error) => { }
          );
        });
      },
      getHref (url) {
        var isFirefox = navigator.userAgent.indexOf("Firefox") > -1; // 是否是火狐  ，火狐内核Gecko
        var isWebKit = navigator.userAgent.indexOf("WebKit") > -1; // 是否是WebKit 内核
        var isChrome = navigator.userAgent.indexOf("Chrome") > -1; // 是否是谷歌
        var isTrident = navigator.userAgent.indexOf("Trident") > -1; // 是否是IE内核
        var isIeL = !!window.ActiveXObject || "ActiveXObject" in window;
        if (isFirefox || isWebKit || isChrome || isTrident || isIeL) {
          // IE和火狐用window.open打开
          // 调起客户端 5秒之后自动关闭调起窗口
          var client = window.open(url);
          setTimeout(function () {
            if (client) {
              client.close(); //关闭新打开的浏览器窗口，避免留下一个空白窗口
            }
          }, 5000);
        } else {
          //其它浏览器使用模拟<a>标签`click`事件的形式调起
          var a = document.createElement("a");
          a.setAttribute("href", url);
          document.body.appendChild(a);
          a.click();
        }
        setTimeout(() => {
          // 5秒之后不管有没有调起都弹窗提示下载客户端
          this.liveDialog = true;
          setTimeout(() => {
            // 5秒之后关闭
            this.liveDialog = false;
          }, 8000);
        }, 5000);
      },
      OSnow () {
        var agent = navigator.userAgent.toLowerCase();
        var isMac = /macintosh|mac os x/i.test(navigator.userAgent);
        if (agent.indexOf("win32") >= 0 || agent.indexOf("wow32") >= 0) {
          this.isMac = false;
          this.downClient =
            // "https://www.baijiayun.com/default/home/<USER>";
            "https://baogang.bj.bcebos.com/installer/Huizhixueyuaninstaller.exe"
        }
        if (agent.indexOf("win64") >= 0 || agent.indexOf("wow64") >= 0) {
          this.isMac = false;
          this.downClient =
            //"https://www.baijiayun.com/default/home/<USER>";
            "https://baogang.bj.bcebos.com/installer/Huizhixueyuaninstaller.exe"
        }
        // if (isMac) {
        //   this.isMac = true;
        //   this.downClient =
        //     "https://www.baijiayun.com/default/home/<USER>";
        // }
      },

  }
}
</script>

<style scoped>
  #app {
    background: #ebeef2;
  }

  .live_state_default {
    position: absolute;
    top: 0;
    left: 0;
    height: 28px;
    border-radius: 16px;
    background-color: #0096ff;
    display: flex;
    align-items: center;
    margin: 10px 0 0 10px;
    padding: 0 10px;
  }

  .live_state_box_s {
    background-color: rgb(194, 198, 214);
  }

  .live_state_box_p {
    background-color: rgb(255, 51, 51);
  }

  .live_state_box_e {
    background: linear-gradient(to right, #005dc2, #024b9a);
  }

  .live_span {
    margin-left: 5px;
    color: #fff;
    font-size: 13px;
  }

  .live_state_default img {
    width: 15px;
    height: 15px;
  }

</style>
