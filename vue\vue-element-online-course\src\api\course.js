import axios from '@/axios'


// 1、	轮播图查询

export function getHomeImgs(){
  return axios.gets('/api/cms/public/home/<USER>')
}

// 2、	推荐课程

export function getRecommendCourses(){
  return axios.gets('/api/cms/public/home/<USER>')
}
// 3、	课程分类

export function getCategory(data){
  return axios.gets('/api/cms/public/courses/category',data)
}
// 4、	课程中心

export function getCoursesList(data){
  return axios.gets('/api/cms/public/courses',data)
}
export function getHotCoursesList(){
  return axios.gets('/api/cms/public/courses/hot')
}
export function getCourseInfo(id){
  return axios.gets('/api/cms/public/courses/'+id)
}

export function updateViewCount(id){
  return axios.gets('/api/cms/public/courses/update-view-count?courseId='+id)
}

//6、	获取资源详情
export function getResourceInfo(id){
  return axios.gets('/api/cms/public/courses/resource/'+id)
}
// 6、	获取资源地址
export function getResourceUrl(data){
  return axios.gets('/api/cms/public/courses/resource/get-url',data)
}
export function getCourseResourceHtmlUrl(url) {
  return axios.gets('api/cms/public/courses/resource/get-html-url?url=' + url)
}
export function getPresignedUrl(url){
  return axios.gets('/api/cloud-resource/bce/get-presigned-url?url='+url)
}

// 获取当前课程全部资源学习情况
export function getUserRecord(data){
  return axios.gets('/api/cms/public/courses/user-record',data)
}
// h5资源学习详情
export function getUserRecordInfo(data){
  return axios.gets('/api/cms/public/courses/user-record/get-record-info',data)
}


// 更新课程学习记录（点击开始学习调用） post
export function updateCourseRecord(data){
  return axios.posts('/api/cms/public/courses/user-record/update-course',data)
}

// 更新课程资源学习时长 post
export function UpdateResUserRecord(data){
  return axios.posts('/api/cms/public/courses/user-record/update-res',data)
}


// 我的培训
// 我的培训列表

export function getMyTrains(data){
  return axios.gets('/api/cms/public/trains/my-trains',data)
}


// 培训详情

export function getTrainsDetail(id){
  return axios.gets('/api/cms/public/trains/detail?trainId='+ id)
}

// 培训课程学习记录
export function getTrainUserRecord(id){
  return axios.gets('/api/cms/public/train/user-record?trianId='+ id)
}
// 培训下的考核
export function getTrainExams(data){
  return axios.gets('/api/exams/student/train-exams', data)
}
// 我的课程
// 我的课程


export function getMyCourse(data){
  return axios.gets('/api/cms/public/courses/my-course', data)
}

// 培训学习记录

export function getMytrainUserRecord(data){
  return axios.gets('/api/cms/public/train/user-record/my-train', data)
}
//自选课程学习记录

export function getMyOptionalCourseUserRecord(data){
  return axios.gets('/api/cms/public/courses/user-record/my-optional-course', data)
}

/*--- 课程问答评论-----*/

//获取评论列表
//参数 Filter，CourseId，ParentId，Sorting，SkipCount，MaxResultCount
export function getCommentList(data){
  return axios.gets('/api/cms/public/course-comment', data)
}

//添加问答评论

export function addComment(data){
  return axios.posts('/api/cms/public/course-comment', data)
}
//删除问答评论
export function deleteComment(data){
  return axios.deletes('/api/cms/public/course-comment', data)
}
//采纳为最佳答案
export function setBestAnswer(data){
  return axios.posts('/api/cms/public/course-comment/set-best-answer', data)
}
//添加浏览数
export function updateCommentViewcount(id){
  return axios.posts('/api/cms/public/course-comment/view?id='+id)
}
export function getMyComment(data){
  return axios.gets('/api/cms/public/course-comment/get-my-comment', data)
}

// 添加课程评分

//获取评分列表
//参数 Filter，CourseId，ParentId，Sorting，SkipCount，MaxResultCount
export function getscoreList(data){
  return axios.gets('/api/cms/public/course-score', data)
}
//添加评分
export function addscore(data){
  return axios.posts('/api/cms/public/course-score', data)
}

// //删除问答评论
// export function deleteComment(data){
//   return axios.deletes('/api/cms/public/course-score', data)
// }

// 获取课程考评
export function getCourseExams(data){
  return axios.gets('/api/cms/public/courses/exams',data)
}

// 培训直播课程
export function getMyTrainLives(data){
  return axios.gets('/api/lms/public/live-users/my-train-lives',data)
}
// 培训 签到
export function signTrain(data){
  return axios.posts('/api/cms/public/trains/signs',data)
}
// 培训 签到记录
export function getSignsRecord(data){
  return axios.gets('/api/cms/public/trains/signs',data)
}
export function getExamInfo(id){
  return axios.gets('/api/exams/student/exam/'+ id)
}
//  公告
export function getNotices(data){
  return axios.gets('/api/notice/announcements/public/system',data)
}
export function getNoticeInfo(data){
  return axios.gets('/api/notice/announcements/public/detail',data)
}

// 虚拟实训
export function getvirtualTrainings(data){
  return axios.gets('/api/cms/public/virtualTrainings/all',data)
}

export function getMyTrainscourses(data){
  return axios.gets('/api/cms/public/trains/my-trains-courses',data)
}
