import { voiceToText } from "@/api/deepseek";
let mediaRecorder;
let audioChunks = [];
let audioContext;
let analyser;
let resolveFunction;
// 接受音频并处理
const voiceStart = async () => {
    try {
        // 请求麦克风权限
        const stream = await navigator.mediaDevices.getUserMedia({ audio: true });

        // 创建音频上下文和分析器
        audioContext = new (window.AudioContext || window.webkitAudioContext)();
        analyser = audioContext.createAnalyser();
        analyser.fftSize = 256;
        const source = audioContext.createMediaStreamSource(stream);
        source.connect(analyser);

        // 初始化录音器
        mediaRecorder = new MediaRecorder(stream);
        audioChunks = [];

        // 监听数据可用事件
        mediaRecorder.ondataavailable = (event) => {
            audioChunks.push(event.data);
        };
        // 录音结束事件
        mediaRecorder.onstop = async() => {
            const audioBlob = new Blob(audioChunks, { type: 'audio/wav' });
            try {
                const res = await voiceToText(audioBlob);
                resolveFunction?.(res);
            } catch (error) {
                console.error('语音转换失败:', error);
            }
            audioChunks = [];
        };
        mediaRecorder.start();

    } catch (error) {
        console.error('Error accessing microphone:', error);
        alert('无法访问麦克风: ' + error.message);
    }
};
const voiceEnd = async () => {
    return new Promise((resolve) => {
        resolveFunction = resolve;
        if (mediaRecorder && mediaRecorder.state !== 'inactive') {
            mediaRecorder.stop();
            mediaRecorder.stream.getTracks().forEach(track => track.stop());
        }
    });
};

export { voiceStart, voiceEnd };
